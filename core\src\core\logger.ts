// Comprehensive logging system
import winston from 'winston';
import { join } from 'path';
import { homedir } from 'os';
import { existsSync, mkdirSync } from 'fs';

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

export interface LogContext {
  [key: string]: any;
}

class Logger {
  private winston: winston.Logger;
  private logDir: string;

  constructor() {
    this.logDir = join(homedir(), '.arien', 'logs');
    this.ensureLogDir();
    this.winston = this.createLogger();
  }

  private ensureLogDir(): void {
    if (!existsSync(this.logDir)) {
      mkdirSync(this.logDir, { recursive: true });
    }
  }

  private createLogger(): winston.Logger {
    const logFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json()
    );

    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({ format: 'HH:mm:ss' }),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
        return `${timestamp} [${level}] ${message}${metaStr}`;
      })
    );

    return winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: logFormat,
      transports: [
        // File transport for all logs
        new winston.transports.File({
          filename: join(this.logDir, 'arien.log'),
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
          tailable: true
        }),
        
        // Separate file for errors
        new winston.transports.File({
          filename: join(this.logDir, 'error.log'),
          level: 'error',
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 3,
          tailable: true
        }),

        // Console transport (only in development or when DEBUG is set)
        ...(process.env.NODE_ENV === 'development' || process.env.DEBUG ? [
          new winston.transports.Console({
            format: consoleFormat,
            level: process.env.DEBUG ? 'debug' : 'info'
          })
        ] : [])
      ],
      
      // Handle uncaught exceptions and rejections
      exceptionHandlers: [
        new winston.transports.File({
          filename: join(this.logDir, 'exceptions.log')
        })
      ],
      
      rejectionHandlers: [
        new winston.transports.File({
          filename: join(this.logDir, 'rejections.log')
        })
      ]
    });
  }

  public error(message: string, context?: LogContext): void {
    this.winston.error(message, context);
  }

  public warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  public info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  public debug(message: string, context?: LogContext): void {
    this.winston.debug(message, context);
  }

  public log(level: LogLevel, message: string, context?: LogContext): void {
    this.winston.log(level, message, context);
  }

  public setLevel(level: LogLevel): void {
    this.winston.level = level;
  }

  public getLevel(): string {
    return this.winston.level;
  }

  public child(defaultContext: LogContext): Logger {
    const childLogger = new Logger();
    const originalLog = childLogger.winston.log.bind(childLogger.winston);
    
    childLogger.winston.log = (level: any, message: any, meta: any = {}) => {
      return originalLog(level, message, { ...defaultContext, ...meta });
    };
    
    return childLogger;
  }

  public profile(id: string): void {
    this.winston.profile(id);
  }

  public startTimer(): winston.Profiler {
    return this.winston.startTimer();
  }

  public async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.winston.on('finish', resolve);
      this.winston.end();
    });
  }
}

// Global logger instance
export const logger = new Logger();

// Convenience functions for common logging patterns
export function logApiRequest(provider: string, model: string, context?: LogContext): void {
  logger.info('API request started', {
    provider,
    model,
    ...context
  });
}

export function logApiResponse(provider: string, model: string, duration: number, context?: LogContext): void {
  logger.info('API request completed', {
    provider,
    model,
    duration,
    ...context
  });
}

export function logToolExecution(toolName: string, args: any, context?: LogContext): void {
  logger.debug('Tool execution started', {
    tool: toolName,
    args,
    ...context
  });
}

export function logToolResult(toolName: string, success: boolean, duration: number, context?: LogContext): void {
  logger.debug('Tool execution completed', {
    tool: toolName,
    success,
    duration,
    ...context
  });
}

export function logUserAction(action: string, context?: LogContext): void {
  logger.info('User action', {
    action,
    ...context
  });
}

export function logSecurityEvent(event: string, context?: LogContext): void {
  logger.warn('Security event', {
    event,
    ...context
  });
}

export function logPerformanceMetric(metric: string, value: number, unit: string, context?: LogContext): void {
  logger.debug('Performance metric', {
    metric,
    value,
    unit,
    ...context
  });
}
