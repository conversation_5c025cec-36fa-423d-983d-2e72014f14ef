// Session information summary component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface SessionInfo {
  id: string;
  startTime: Date;
  duration: number; // in milliseconds
  messageCount: number;
  userMessageCount: number;
  assistantMessageCount: number;
  toolCallCount: number;
  errorCount: number;
  totalTokens: {
    input: number;
    output: number;
    total: number;
  };
  providers: {
    [provider: string]: {
      messageCount: number;
      tokenCount: number;
      errorCount: number;
    };
  };
  filesModified: string[];
  commandsExecuted: string[];
  memoryUsage: {
    peak: number;
    current: number;
    average: number;
  };
  status: 'active' | 'idle' | 'error' | 'completed';
}

interface SessionSummaryDisplayProps {
  session: SessionInfo;
  showDetails?: boolean;
  showProviderBreakdown?: boolean;
  showFileActivity?: boolean;
  compact?: boolean;
}

export const SessionSummaryDisplay: React.FC<SessionSummaryDisplayProps> = ({
  session,
  showDetails = true,
  showProviderBreakdown = false,
  showFileActivity = false,
  compact = false
}) => {
  const theme = useTheme();

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatMemory = (bytes: number) => {
    const mb = bytes / 1024 / 1024;
    if (mb < 1) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${mb.toFixed(1)}MB`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return theme.colors.success;
      case 'idle': return theme.colors.warning;
      case 'error': return theme.colors.error;
      case 'completed': return theme.colors.info;
      default: return theme.colors.text;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '🟢';
      case 'idle': return '🟡';
      case 'error': return '🔴';
      case 'completed': return '✅';
      default: return '⚪';
    }
  };

  if (compact) {
    return (
      <Box flexDirection="row" gap={2}>
        <Text color={getStatusColor(session.status)}>
          {getStatusIcon(session.status)}
        </Text>
        <Text color={theme.colors.text}>
          {session.messageCount} msgs
        </Text>
        <Text color={theme.colors.muted}>
          {formatDuration(session.duration)}
        </Text>
        <Text color={theme.colors.secondary}>
          {(session.totalTokens.total / 1000).toFixed(1)}K tokens
        </Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {/* Session Header */}
      <Box marginBottom={1}>
        <Text color={getStatusColor(session.status)}>
          {getStatusIcon(session.status)} Session {session.id.slice(0, 8)}
        </Text>
        <Text color={theme.colors.muted}>
          {' '}Started: {session.startTime.toLocaleString()}
        </Text>
      </Box>

      {/* Basic Stats */}
      <Box flexDirection="column" marginBottom={1}>
        <Box flexDirection="row" gap={3}>
          <Box>
            <Text color={theme.colors.text}>Duration: </Text>
            <Text color={theme.colors.muted}>{formatDuration(session.duration)}</Text>
          </Box>
          <Box>
            <Text color={theme.colors.text}>Messages: </Text>
            <Text color={theme.colors.muted}>{session.messageCount}</Text>
          </Box>
          <Box>
            <Text color={theme.colors.text}>Status: </Text>
            <Text color={getStatusColor(session.status)}>{session.status}</Text>
          </Box>
        </Box>

        {showDetails && (
          <Box flexDirection="row" gap={3} marginTop={0}>
            <Box>
              <Text color={theme.colors.user}>User: </Text>
              <Text color={theme.colors.muted}>{session.userMessageCount}</Text>
            </Box>
            <Box>
              <Text color={theme.colors.assistant}>Assistant: </Text>
              <Text color={theme.colors.muted}>{session.assistantMessageCount}</Text>
            </Box>
            <Box>
              <Text color={theme.colors.secondary}>Tools: </Text>
              <Text color={theme.colors.muted}>{session.toolCallCount}</Text>
            </Box>
            {session.errorCount > 0 && (
              <Box>
                <Text color={theme.colors.error}>Errors: </Text>
                <Text color={theme.colors.error}>{session.errorCount}</Text>
              </Box>
            )}
          </Box>
        )}
      </Box>

      {/* Token Usage */}
      {showDetails && (
        <Box flexDirection="column" marginBottom={1}>
          <Text color={theme.colors.text}>Token Usage:</Text>
          <Box marginLeft={2} flexDirection="row" gap={2}>
            <Text color={theme.colors.muted}>
              Input: {(session.totalTokens.input / 1000).toFixed(1)}K
            </Text>
            <Text color={theme.colors.muted}>
              Output: {(session.totalTokens.output / 1000).toFixed(1)}K
            </Text>
            <Text color={theme.colors.muted}>
              Total: {(session.totalTokens.total / 1000).toFixed(1)}K
            </Text>
          </Box>
        </Box>
      )}

      {/* Provider Breakdown */}
      {showProviderBreakdown && Object.keys(session.providers).length > 0 && (
        <Box flexDirection="column" marginBottom={1}>
          <Text color={theme.colors.text}>Provider Usage:</Text>
          <Box marginLeft={2} flexDirection="column">
            {Object.entries(session.providers).map(([provider, stats]) => (
              <Box key={provider} flexDirection="row" gap={2}>
                <Text color={theme.colors.secondary}>{provider}:</Text>
                <Text color={theme.colors.muted}>
                  {stats.messageCount} msgs, {(stats.tokenCount / 1000).toFixed(1)}K tokens
                </Text>
                {stats.errorCount > 0 && (
                  <Text color={theme.colors.error}>
                    ({stats.errorCount} errors)
                  </Text>
                )}
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {/* Memory Usage */}
      {showDetails && (
        <Box flexDirection="column" marginBottom={1}>
          <Text color={theme.colors.text}>Memory Usage:</Text>
          <Box marginLeft={2} flexDirection="row" gap={2}>
            <Text color={theme.colors.muted}>
              Current: {formatMemory(session.memoryUsage.current)}
            </Text>
            <Text color={theme.colors.muted}>
              Peak: {formatMemory(session.memoryUsage.peak)}
            </Text>
            <Text color={theme.colors.muted}>
              Avg: {formatMemory(session.memoryUsage.average)}
            </Text>
          </Box>
        </Box>
      )}

      {/* File Activity */}
      {showFileActivity && session.filesModified.length > 0 && (
        <Box flexDirection="column" marginBottom={1}>
          <Text color={theme.colors.text}>Files Modified ({session.filesModified.length}):</Text>
          <Box marginLeft={2} flexDirection="column">
            {session.filesModified.slice(0, 5).map((file, index) => (
              <Text key={index} color={theme.colors.muted}>• {file}</Text>
            ))}
            {session.filesModified.length > 5 && (
              <Text color={theme.colors.muted}>
                ... and {session.filesModified.length - 5} more files
              </Text>
            )}
          </Box>
        </Box>
      )}

      {/* Commands Executed */}
      {showFileActivity && session.commandsExecuted.length > 0 && (
        <Box flexDirection="column">
          <Text color={theme.colors.text}>Commands Executed ({session.commandsExecuted.length}):</Text>
          <Box marginLeft={2} flexDirection="column">
            {session.commandsExecuted.slice(0, 3).map((command, index) => (
              <Text key={index} color={theme.colors.muted}>• {command}</Text>
            ))}
            {session.commandsExecuted.length > 3 && (
              <Text color={theme.colors.muted}>
                ... and {session.commandsExecuted.length - 3} more commands
              </Text>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};
