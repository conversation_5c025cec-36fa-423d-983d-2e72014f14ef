// Retry logic with exponential backoff
import { logger } from '../core/logger.js';

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  jitter: boolean;
  retryCondition?: (error: any) => boolean;
  onRetry?: (error: any, attempt: number) => void;
}

export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 30000,
  backoffFactor: 2,
  jitter: true,
  retryCondition: (error) => {
    // Default: retry on network errors, rate limits, and temporary failures
    if (error?.code === 'ECONNRESET' || error?.code === 'ENOTFOUND' || error?.code === 'ETIMEDOUT') {
      return true;
    }
    if (error?.status === 429 || error?.status === 502 || error?.status === 503 || error?.status === 504) {
      return true;
    }
    return false;
  }
};

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const config = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: any;

  for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
    try {
      const result = await operation();
      
      if (attempt > 1) {
        logger.info('Operation succeeded after retry', { attempt, maxAttempts: config.maxAttempts });
      }
      
      return result;
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      if (attempt === config.maxAttempts || !config.retryCondition?.(error)) {
        logger.error('Operation failed after all retries', { 
          attempt, 
          maxAttempts: config.maxAttempts, 
          error 
        });
        throw error;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = calculateDelay(attempt, config);
      
      logger.warn('Operation failed, retrying', { 
        attempt, 
        maxAttempts: config.maxAttempts, 
        delay, 
        error: error.message 
      });

      // Call retry callback if provided
      config.onRetry?.(error, attempt);

      // Wait before retrying
      await sleep(delay);
    }
  }

  throw lastError;
}

function calculateDelay(attempt: number, options: RetryOptions): number {
  // Calculate exponential backoff delay
  let delay = options.baseDelay * Math.pow(options.backoffFactor, attempt - 1);
  
  // Apply maximum delay limit
  delay = Math.min(delay, options.maxDelay);
  
  // Add jitter to prevent thundering herd
  if (options.jitter) {
    delay = delay * (0.5 + Math.random() * 0.5);
  }
  
  return Math.floor(delay);
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';

  constructor(
    private readonly failureThreshold: number = 5,
    private readonly recoveryTimeout: number = 60000,
    private readonly successThreshold: number = 2
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'half-open';
        logger.info('Circuit breaker transitioning to half-open');
      } else {
        throw new Error('Circuit breaker is open');
      }
    }

    try {
      const result = await operation();
      
      if (this.state === 'half-open') {
        this.reset();
        logger.info('Circuit breaker closed after successful operation');
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
      logger.warn('Circuit breaker opened due to failures', { 
        failures: this.failures, 
        threshold: this.failureThreshold 
      });
    }
  }

  private reset(): void {
    this.failures = 0;
    this.state = 'closed';
  }

  getState(): 'closed' | 'open' | 'half-open' {
    return this.state;
  }

  getFailureCount(): number {
    return this.failures;
  }
}

export class RateLimiter {
  private requests: number[] = [];

  constructor(
    private readonly maxRequests: number,
    private readonly windowMs: number
  ) {}

  async acquire(): Promise<void> {
    const now = Date.now();
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length >= this.maxRequests) {
      const oldestRequest = Math.min(...this.requests);
      const waitTime = this.windowMs - (now - oldestRequest);
      
      logger.debug('Rate limit reached, waiting', { waitTime, requests: this.requests.length });
      
      await sleep(waitTime);
      return this.acquire(); // Recursive call after waiting
    }
    
    this.requests.push(now);
  }

  getRequestCount(): number {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    return this.requests.length;
  }

  getRemainingRequests(): number {
    return Math.max(0, this.maxRequests - this.getRequestCount());
  }
}

// Utility function for retrying API calls with specific conditions
export async function retryApiCall<T>(
  apiCall: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  return withRetry(apiCall, {
    ...options,
    retryCondition: (error) => {
      // Retry on network errors
      if (error?.code === 'ECONNRESET' || error?.code === 'ENOTFOUND' || error?.code === 'ETIMEDOUT') {
        return true;
      }

      // Retry on rate limits and server errors
      if (error?.status === 429 || error?.status >= 500) {
        return true;
      }

      // Don't retry on client errors (4xx except 429)
      if (error?.status >= 400 && error?.status < 500 && error?.status !== 429) {
        return false;
      }

      return options.retryCondition?.(error) ?? true;
    }
  });
}

// Alternative interface for compatibility with other modules
export interface RetryWithBackoffOptions {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  shouldRetry?: (error: any) => boolean;
}

export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  options: RetryWithBackoffOptions
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: options.maxRetries + 1, // withRetry counts total attempts, retryWithBackoff counts retries
    baseDelay: options.baseDelay,
    maxDelay: options.maxDelay,
    retryCondition: options.shouldRetry || DEFAULT_RETRY_OPTIONS.retryCondition,
    backoffFactor: 2,
    jitter: true
  });
}
