// Base class for tools that modify files
import { <PERSON><PERSON><PERSON>, <PERSON>lR<PERSON>ult, ToolContext, ToolExecutionError } from './tools.js';
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../core/logger.js';

export interface ModificationRecord {
  timestamp: Date;
  toolName: string;
  operation: string;
  filePath: string;
  userId: string;
  sessionId: string;
  backupPath?: string;
  checksum?: string;
}

export interface BackupOptions {
  enabled: boolean;
  maxBackups: number;
  retentionDays: number;
  compressionEnabled: boolean;
}

export abstract class ModifiableTool extends BaseTool {
  private static modificationHistory: ModificationRecord[] = [];
  private static readonly MAX_HISTORY_SIZE = 10000;

  protected async validateFileAccess(filePath: string, context: ToolContext): Promise<string> {
    const absolutePath = path.resolve(context.workingDirectory, filePath);
    
    // Security check - ensure path is within working directory
    if (!absolutePath.startsWith(context.workingDirectory)) {
      throw new ToolExecutionError(
        this.definition.name,
        `File path is outside the allowed working directory: ${filePath}`
      );
    }

    // Check if path is in sandbox if enabled
    if (context.sandboxEnabled) {
      await this.validateSandboxAccess(absolutePath, context);
    }

    return absolutePath;
  }

  protected async createBackup(
    filePath: string,
    operation: string,
    context: ToolContext,
    options: BackupOptions = this.getDefaultBackupOptions()
  ): Promise<string | undefined> {
    if (!options.enabled) {
      return undefined;
    }

    try {
      // Check if file exists
      const fileExists = await this.fileExists(filePath);
      if (!fileExists) {
        return undefined;
      }

      // Create backup directory
      const backupDir = path.join(path.dirname(filePath), '.arien-backups');
      await fs.mkdir(backupDir, { recursive: true });

      // Generate backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = path.basename(filePath);
      const backupFileName = `${fileName}.${operation}.${timestamp}.backup`;
      const backupPath = path.join(backupDir, backupFileName);

      // Copy file to backup location
      await fs.copyFile(filePath, backupPath);

      // Clean up old backups
      await this.cleanupOldBackups(backupDir, fileName, options);

      logger.debug('Backup created', {
        originalPath: filePath,
        backupPath,
        operation
      });

      return backupPath;

    } catch (error) {
      logger.warn('Failed to create backup', {
        filePath,
        operation,
        error: error instanceof Error ? error.message : String(error)
      });
      
      // Don't fail the operation if backup fails
      return undefined;
    }
  }

  protected async recordModification(
    operation: string,
    filePath: string,
    context: ToolContext,
    backupPath?: string
  ): Promise<void> {
    const record: ModificationRecord = {
      timestamp: new Date(),
      toolName: this.definition.name,
      operation,
      filePath,
      userId: context.userId,
      sessionId: context.sessionId,
      backupPath
    };

    // Add checksum if file exists
    try {
      if (await this.fileExists(filePath)) {
        record.checksum = await this.calculateChecksum(filePath);
      }
    } catch {
      // Ignore checksum calculation errors
    }

    ModifiableTool.modificationHistory.push(record);

    // Limit history size
    if (ModifiableTool.modificationHistory.length > ModifiableTool.MAX_HISTORY_SIZE) {
      ModifiableTool.modificationHistory = ModifiableTool.modificationHistory.slice(-ModifiableTool.MAX_HISTORY_SIZE);
    }

    logger.info('File modification recorded', {
      toolName: this.definition.name,
      operation,
      filePath,
      userId: context.userId,
      sessionId: context.sessionId
    });
  }

  protected async validateSandboxAccess(filePath: string, context: ToolContext): Promise<void> {
    // Check if file is in allowed paths
    const allowedPaths = context.allowedCommands || [];
    
    if (allowedPaths.length > 0) {
      const isAllowed = allowedPaths.some(allowedPath => {
        const absoluteAllowedPath = path.resolve(context.workingDirectory, allowedPath);
        return filePath.startsWith(absoluteAllowedPath);
      });

      if (!isAllowed) {
        throw new ToolExecutionError(
          this.definition.name,
          `File access denied by sandbox: ${filePath}`
        );
      }
    }

    // Check for dangerous file patterns
    const dangerousPatterns = [
      /\/etc\/passwd$/,
      /\/etc\/shadow$/,
      /\/etc\/sudoers$/,
      /\.ssh\/id_rsa$/,
      /\.ssh\/id_ed25519$/,
      /\.env$/,
      /\.secret$/,
      /password/i,
      /secret/i,
      /private.*key/i
    ];

    const relativePath = path.relative(context.workingDirectory, filePath);
    const isDangerous = dangerousPatterns.some(pattern => pattern.test(relativePath));

    if (isDangerous && context.approvalLevel !== 'yolo') {
      throw new ToolExecutionError(
        this.definition.name,
        `Access to potentially sensitive file requires explicit approval: ${relativePath}`
      );
    }
  }

  protected async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  protected async calculateChecksum(filePath: string): Promise<string> {
    const crypto = await import('crypto');
    const content = await fs.readFile(filePath);
    return crypto.createHash('sha256').update(content).digest('hex');
  }

  private async cleanupOldBackups(
    backupDir: string,
    fileName: string,
    options: BackupOptions
  ): Promise<void> {
    try {
      const entries = await fs.readdir(backupDir, { withFileTypes: true });
      const backupFiles = entries
        .filter(entry => entry.isFile() && entry.name.startsWith(fileName))
        .map(entry => ({
          name: entry.name,
          path: path.join(backupDir, entry.name),
          stat: null as any
        }));

      // Get file stats
      for (const backup of backupFiles) {
        try {
          backup.stat = await fs.stat(backup.path);
        } catch {
          // Ignore files we can't stat
        }
      }

      // Filter out files we couldn't stat
      const validBackups = backupFiles.filter(backup => backup.stat);

      // Sort by modification time (newest first)
      validBackups.sort((a, b) => b.stat.mtime.getTime() - a.stat.mtime.getTime());

      // Remove excess backups (keep only maxBackups)
      if (validBackups.length > options.maxBackups) {
        const toDelete = validBackups.slice(options.maxBackups);
        for (const backup of toDelete) {
          try {
            await fs.unlink(backup.path);
            logger.debug('Old backup removed', { backupPath: backup.path });
          } catch {
            // Ignore deletion errors
          }
        }
      }

      // Remove backups older than retention period
      const cutoffDate = new Date(Date.now() - options.retentionDays * 24 * 60 * 60 * 1000);
      for (const backup of validBackups) {
        if (backup.stat.mtime < cutoffDate) {
          try {
            await fs.unlink(backup.path);
            logger.debug('Expired backup removed', { backupPath: backup.path });
          } catch {
            // Ignore deletion errors
          }
        }
      }

    } catch (error) {
      logger.debug('Backup cleanup failed', {
        backupDir,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private getDefaultBackupOptions(): BackupOptions {
    return {
      enabled: true,
      maxBackups: 10,
      retentionDays: 30,
      compressionEnabled: false
    };
  }

  // Static methods for accessing modification history
  public static getModificationHistory(
    filters?: {
      userId?: string;
      sessionId?: string;
      toolName?: string;
      filePath?: string;
      since?: Date;
    }
  ): ModificationRecord[] {
    let history = [...ModifiableTool.modificationHistory];

    if (filters) {
      if (filters.userId) {
        history = history.filter(record => record.userId === filters.userId);
      }
      
      if (filters.sessionId) {
        history = history.filter(record => record.sessionId === filters.sessionId);
      }
      
      if (filters.toolName) {
        history = history.filter(record => record.toolName === filters.toolName);
      }
      
      if (filters.filePath) {
        history = history.filter(record => record.filePath.includes(filters.filePath!));
      }
      
      if (filters.since) {
        history = history.filter(record => record.timestamp >= filters.since!);
      }
    }

    return history.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  public static clearModificationHistory(olderThan?: Date): number {
    const originalLength = ModifiableTool.modificationHistory.length;
    
    if (olderThan) {
      ModifiableTool.modificationHistory = ModifiableTool.modificationHistory.filter(
        record => record.timestamp >= olderThan
      );
    } else {
      ModifiableTool.modificationHistory = [];
    }

    const cleared = originalLength - ModifiableTool.modificationHistory.length;
    
    if (cleared > 0) {
      logger.info('Modification history cleared', { cleared, olderThan });
    }

    return cleared;
  }

  public static getModificationStats(): {
    totalModifications: number;
    uniqueFiles: number;
    uniqueUsers: number;
    uniqueSessions: number;
    toolUsage: Record<string, number>;
  } {
    const history = ModifiableTool.modificationHistory;
    const uniqueFiles = new Set(history.map(r => r.filePath));
    const uniqueUsers = new Set(history.map(r => r.userId));
    const uniqueSessions = new Set(history.map(r => r.sessionId));
    
    const toolUsage: Record<string, number> = {};
    for (const record of history) {
      toolUsage[record.toolName] = (toolUsage[record.toolName] || 0) + 1;
    }

    return {
      totalModifications: history.length,
      uniqueFiles: uniqueFiles.size,
      uniqueUsers: uniqueUsers.size,
      uniqueSessions: uniqueSessions.size,
      toolUsage
    };
  }
}
