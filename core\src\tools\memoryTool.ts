// Memory/context management tool
import { z } from 'zod';
import { <PERSON><PERSON>ool, <PERSON>l<PERSON><PERSON>ult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const MemorySchema = z.object({
  operation: z.enum(['store', 'retrieve', 'search', 'delete', 'list', 'clear']).describe('Memory operation to perform'),
  key: z.string().optional().describe('Memory key for store/retrieve/delete operations'),
  value: z.string().optional().describe('Value to store (for store operation)'),
  query: z.string().optional().describe('Search query (for search operation)'),
  category: z.string().optional().describe('Memory category for organization'),
  tags: z.array(z.string()).optional().describe('Tags for the memory item'),
  maxResults: z.number().optional().default(10).describe('Maximum number of results to return'),
  includeMetadata: z.boolean().optional().default(true).describe('Include metadata in results')
});

export interface MemoryItem {
  key: string;
  value: string;
  category?: string;
  tags: string[];
  timestamp: Date;
  userId: string;
  sessionId: string;
  accessCount: number;
  lastAccessed: Date;
  metadata?: Record<string, any>;
}

export interface MemorySearchResult {
  item: MemoryItem;
  score: number;
  matchType: 'key' | 'value' | 'tag' | 'category';
}

export interface MemoryStats {
  totalItems: number;
  categories: Record<string, number>;
  tags: Record<string, number>;
  oldestItem?: Date;
  newestItem?: Date;
  totalSize: number;
}

export class MemoryTool extends BaseTool {
  private static memory = new Map<string, MemoryItem>();
  private static readonly MAX_MEMORY_SIZE = 100000; // Maximum number of items
  private static readonly MAX_VALUE_SIZE = 10 * 1024; // 10KB per value

  constructor() {
    super({
      name: 'memory',
      description: 'Store, retrieve, and manage contextual memory for conversations',
      parameters: MemorySchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'memory'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);

      logger.debug('Memory operation', {
        operation: validatedParams.operation,
        key: validatedParams.key,
        category: validatedParams.category,
        userId: context.userId
      });

      let result: any;

      switch (validatedParams.operation) {
        case 'store':
          result = await this.storeMemory(validatedParams, context);
          break;
        case 'retrieve':
          result = await this.retrieveMemory(validatedParams, context);
          break;
        case 'search':
          result = await this.searchMemory(validatedParams, context);
          break;
        case 'delete':
          result = await this.deleteMemory(validatedParams, context);
          break;
        case 'list':
          result = await this.listMemory(validatedParams, context);
          break;
        case 'clear':
          result = await this.clearMemory(validatedParams, context);
          break;
        default:
          throw new ToolExecutionError(
            this.definition.name,
            `Unknown operation: ${validatedParams.operation}`
          );
      }

      logger.info('Memory operation completed', {
        operation: validatedParams.operation,
        success: true,
        userId: context.userId
      });

      return this.createSuccessResult(
        this.formatResult(validatedParams.operation, result),
        result
      );

    } catch (error) {
      logger.error('Memory operation failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async storeMemory(params: any, context: ToolContext): Promise<MemoryItem> {
    if (!params.key || !params.value) {
      throw new ToolExecutionError(
        this.definition.name,
        'Store operation requires both key and value'
      );
    }

    // Validate value size
    if (params.value.length > MemoryTool.MAX_VALUE_SIZE) {
      throw new ToolExecutionError(
        this.definition.name,
        `Value too large: ${params.value.length} bytes (max: ${MemoryTool.MAX_VALUE_SIZE})`
      );
    }

    // Check memory size limit
    if (MemoryTool.memory.size >= MemoryTool.MAX_MEMORY_SIZE) {
      // Remove oldest items to make space
      await this.evictOldestItems(Math.floor(MemoryTool.MAX_MEMORY_SIZE * 0.1));
    }

    const memoryKey = this.createMemoryKey(params.key, context.userId);
    const now = new Date();

    const existingItem = MemoryTool.memory.get(memoryKey);
    
    const memoryItem: MemoryItem = {
      key: params.key,
      value: params.value,
      category: params.category,
      tags: params.tags || [],
      timestamp: existingItem?.timestamp || now,
      userId: context.userId,
      sessionId: context.sessionId,
      accessCount: (existingItem?.accessCount || 0) + 1,
      lastAccessed: now,
      metadata: {
        ...existingItem?.metadata,
        updatedAt: now,
        version: (existingItem?.metadata?.version || 0) + 1
      }
    };

    MemoryTool.memory.set(memoryKey, memoryItem);

    logger.debug('Memory stored', {
      key: params.key,
      category: params.category,
      tags: params.tags,
      userId: context.userId,
      isUpdate: !!existingItem
    });

    return memoryItem;
  }

  private async retrieveMemory(params: any, context: ToolContext): Promise<MemoryItem | null> {
    if (!params.key) {
      throw new ToolExecutionError(
        this.definition.name,
        'Retrieve operation requires a key'
      );
    }

    const memoryKey = this.createMemoryKey(params.key, context.userId);
    const item = MemoryTool.memory.get(memoryKey);

    if (item) {
      // Update access statistics
      item.accessCount++;
      item.lastAccessed = new Date();
      
      logger.debug('Memory retrieved', {
        key: params.key,
        userId: context.userId,
        accessCount: item.accessCount
      });
    }

    return item || null;
  }

  private async searchMemory(params: any, context: ToolContext): Promise<MemorySearchResult[]> {
    if (!params.query) {
      throw new ToolExecutionError(
        this.definition.name,
        'Search operation requires a query'
      );
    }

    const results: MemorySearchResult[] = [];
    const query = params.query.toLowerCase();

    for (const [memoryKey, item] of MemoryTool.memory.entries()) {
      // Only search user's own memories
      if (item.userId !== context.userId) {
        continue;
      }

      let score = 0;
      let matchType: 'key' | 'value' | 'tag' | 'category' = 'value';

      // Search in key
      if (item.key.toLowerCase().includes(query)) {
        score += 10;
        matchType = 'key';
      }

      // Search in value
      if (item.value.toLowerCase().includes(query)) {
        score += 5;
        if (matchType !== 'key') matchType = 'value';
      }

      // Search in tags
      if (item.tags.some(tag => tag.toLowerCase().includes(query))) {
        score += 7;
        if (matchType === 'value') matchType = 'tag';
      }

      // Search in category
      if (item.category && item.category.toLowerCase().includes(query)) {
        score += 6;
        if (matchType === 'value') matchType = 'category';
      }

      // Boost score for exact matches
      if (item.key.toLowerCase() === query) {
        score += 20;
      }

      // Boost score for recent items
      const daysSinceCreated = (Date.now() - item.timestamp.getTime()) / (1000 * 60 * 60 * 24);
      score += Math.max(0, 5 - daysSinceCreated);

      // Boost score for frequently accessed items
      score += Math.min(item.accessCount, 10);

      if (score > 0) {
        results.push({ item, score, matchType });
      }
    }

    // Sort by score (highest first) and limit results
    results.sort((a, b) => b.score - a.score);
    return results.slice(0, params.maxResults);
  }

  private async deleteMemory(params: any, context: ToolContext): Promise<boolean> {
    if (!params.key) {
      throw new ToolExecutionError(
        this.definition.name,
        'Delete operation requires a key'
      );
    }

    const memoryKey = this.createMemoryKey(params.key, context.userId);
    const deleted = MemoryTool.memory.delete(memoryKey);

    if (deleted) {
      logger.debug('Memory deleted', {
        key: params.key,
        userId: context.userId
      });
    }

    return deleted;
  }

  private async listMemory(params: any, context: ToolContext): Promise<MemoryItem[]> {
    const items: MemoryItem[] = [];

    for (const item of MemoryTool.memory.values()) {
      // Only list user's own memories
      if (item.userId !== context.userId) {
        continue;
      }

      // Filter by category if specified
      if (params.category && item.category !== params.category) {
        continue;
      }

      items.push(item);
    }

    // Sort by last accessed (most recent first)
    items.sort((a, b) => b.lastAccessed.getTime() - a.lastAccessed.getTime());

    return items.slice(0, params.maxResults);
  }

  private async clearMemory(params: any, context: ToolContext): Promise<number> {
    let cleared = 0;

    const keysToDelete: string[] = [];

    for (const [memoryKey, item] of MemoryTool.memory.entries()) {
      // Only clear user's own memories
      if (item.userId !== context.userId) {
        continue;
      }

      // Filter by category if specified
      if (params.category && item.category !== params.category) {
        continue;
      }

      keysToDelete.push(memoryKey);
    }

    for (const key of keysToDelete) {
      MemoryTool.memory.delete(key);
      cleared++;
    }

    logger.info('Memory cleared', {
      cleared,
      category: params.category,
      userId: context.userId
    });

    return cleared;
  }

  private async evictOldestItems(count: number): Promise<void> {
    const items = Array.from(MemoryTool.memory.entries())
      .map(([key, item]) => ({ key, item }))
      .sort((a, b) => a.item.lastAccessed.getTime() - b.item.lastAccessed.getTime());

    for (let i = 0; i < Math.min(count, items.length); i++) {
      MemoryTool.memory.delete(items[i].key);
    }

    logger.debug('Evicted old memory items', { count: Math.min(count, items.length) });
  }

  private createMemoryKey(key: string, userId: string): string {
    return `${userId}:${key}`;
  }

  private formatResult(operation: string, result: any): string {
    switch (operation) {
      case 'store':
        return `Memory stored: ${result.key}`;
      case 'retrieve':
        return result ? `Retrieved: ${result.key}` : 'Memory not found';
      case 'search':
        return `Found ${result.length} matching memories`;
      case 'delete':
        return result ? 'Memory deleted' : 'Memory not found';
      case 'list':
        return `Listed ${result.length} memories`;
      case 'clear':
        return `Cleared ${result} memories`;
      default:
        return 'Operation completed';
    }
  }

  private sanitizeParams(params: any): any {
    const sanitized = { ...params };
    
    // Truncate value for logging
    if (sanitized.value && sanitized.value.length > 100) {
      sanitized.value = sanitized.value.substring(0, 100) + '...';
    }
    
    return sanitized;
  }

  // Static methods for memory management
  public static getMemoryStats(userId?: string): MemoryStats {
    const items = Array.from(MemoryTool.memory.values())
      .filter(item => !userId || item.userId === userId);

    const categories: Record<string, number> = {};
    const tags: Record<string, number> = {};
    let totalSize = 0;
    let oldestItem: Date | undefined;
    let newestItem: Date | undefined;

    for (const item of items) {
      // Count categories
      if (item.category) {
        categories[item.category] = (categories[item.category] || 0) + 1;
      }

      // Count tags
      for (const tag of item.tags) {
        tags[tag] = (tags[tag] || 0) + 1;
      }

      // Calculate size
      totalSize += item.value.length;

      // Track dates
      if (!oldestItem || item.timestamp < oldestItem) {
        oldestItem = item.timestamp;
      }
      if (!newestItem || item.timestamp > newestItem) {
        newestItem = item.timestamp;
      }
    }

    return {
      totalItems: items.length,
      categories,
      tags,
      oldestItem,
      newestItem,
      totalSize
    };
  }

  public static exportMemory(userId: string): MemoryItem[] {
    return Array.from(MemoryTool.memory.values())
      .filter(item => item.userId === userId)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  public static importMemory(items: MemoryItem[], userId: string): number {
    let imported = 0;

    for (const item of items) {
      // Ensure the item belongs to the correct user
      const importedItem = { ...item, userId };
      const memoryKey = `${userId}:${item.key}`;
      
      MemoryTool.memory.set(memoryKey, importedItem);
      imported++;
    }

    return imported;
  }
}
