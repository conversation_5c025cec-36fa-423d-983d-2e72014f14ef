// Application information dialog
import React from 'react';
import { Box, Text, Newline } from 'ink';
import { version } from '../../utils/version.js';

interface AboutBoxProps {
  onClose?: () => void;
}

export const AboutBox: React.FC<AboutBoxProps> = ({ onClose }) => {
  return (
    <Box flexDirection="column" padding={2} borderStyle="round" borderColor="blue">
      <Box justifyContent="center">
        <Text bold color="blue">
          🤖 Arien CLI
        </Text>
      </Box>
      
      <Newline />
      
      <Box flexDirection="column" gap={1}>
        <Text>
          <Text bold>Version:</Text> {version}
        </Text>
        
        <Text>
          <Text bold>Description:</Text> A powerful and modern CLI Terminal system powered by AI
        </Text>
        
        <Text>
          <Text bold>Providers:</Text> DeepSeek, OpenAI, Anthropic, Google
        </Text>
        
        <Text>
          <Text bold>Features:</Text>
        </Text>
        <Box flexDirection="column" marginLeft={2}>
          <Text>• Multi-provider AI support</Text>
          <Text>• Security sandbox environment</Text>
          <Text>• Comprehensive tool system</Text>
          <Text>• Interactive terminal interface</Text>
          <Text>• Real-time streaming responses</Text>
          <Text>• Memory management</Text>
          <Text>• Configuration management</Text>
        </Box>
        
        <Newline />
        
        <Text>
          <Text bold>Repository:</Text> https://github.com/arien-cli/arien
        </Text>
        
        <Text>
          <Text bold>Documentation:</Text> https://docs.arien-cli.com
        </Text>
        
        <Text>
          <Text bold>License:</Text> MIT
        </Text>
        
        <Newline />
        
        <Box justifyContent="center">
          <Text dimColor>
            Press any key to close
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
