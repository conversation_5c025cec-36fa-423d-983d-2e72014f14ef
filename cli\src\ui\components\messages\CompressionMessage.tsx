// Message compression indicator component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../../hooks/useTheme.js';

export interface CompressionInfo {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  method: 'truncate' | 'summarize' | 'semantic' | 'token-limit';
  messagesAffected: number;
  tokensRemoved: number;
  preservedContext: string[];
}

interface CompressionMessageProps {
  compressionInfo: CompressionInfo;
  showDetails?: boolean;
  onToggleDetails?: () => void;
  allowExpand?: boolean;
}

export const CompressionMessage: React.FC<CompressionMessageProps> = ({
  compressionInfo,
  showDetails = false,
  onToggleDetails,
  allowExpand = true
}) => {
  const theme = useTheme();
  const [isExpanded, setIsExpanded] = useState(showDetails);

  useInput((input, key) => {
    if (allowExpand && (key.return || input === ' ')) {
      const newExpanded = !isExpanded;
      setIsExpanded(newExpanded);
      onToggleDetails?.();
    }
  });

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / 1024 / 1024).toFixed(1)}MB`;
  };

  const getCompressionIcon = (method: string) => {
    switch (method) {
      case 'truncate': return '✂️';
      case 'summarize': return '📝';
      case 'semantic': return '🧠';
      case 'token-limit': return '🔢';
      default: return '📦';
    }
  };

  const getCompressionColor = (ratio: number) => {
    if (ratio > 0.7) return theme.colors.success;
    if (ratio > 0.4) return theme.colors.warning;
    return theme.colors.error;
  };

  const getMethodDescription = (method: string) => {
    switch (method) {
      case 'truncate':
        return 'Removed oldest messages to fit within limits';
      case 'summarize':
        return 'Summarized conversation history to preserve context';
      case 'semantic':
        return 'Intelligently compressed based on semantic importance';
      case 'token-limit':
        return 'Compressed to stay within token limits';
      default:
        return 'Applied compression to reduce message size';
    }
  };

  return (
    <Box
      flexDirection="column"
      borderStyle="single"
      borderColor={theme.colors.warning}
      padding={1}
      marginY={1}
    >
      {/* Header */}
      <Box marginBottom={isExpanded ? 1 : 0}>
        <Text color={theme.colors.warning}>
          {getCompressionIcon(compressionInfo.method)} Message History Compressed
        </Text>
        {allowExpand && (
          <Text color={theme.colors.muted}>
            {' '}(Press Enter to {isExpanded ? 'collapse' : 'expand'})
          </Text>
        )}
      </Box>

      {/* Summary */}
      <Box flexDirection="row" gap={2}>
        <Text color={theme.colors.text}>
          {formatBytes(compressionInfo.originalSize)} → {formatBytes(compressionInfo.compressedSize)}
        </Text>
        <Text color={getCompressionColor(compressionInfo.compressionRatio)}>
          ({(compressionInfo.compressionRatio * 100).toFixed(1)}% reduction)
        </Text>
        <Text color={theme.colors.muted}>
          {compressionInfo.messagesAffected} messages affected
        </Text>
      </Box>

      {/* Detailed information */}
      {isExpanded && (
        <Box flexDirection="column" marginTop={1}>
          {/* Method description */}
          <Box marginBottom={1}>
            <Text color={theme.colors.muted}>
              Method: {getMethodDescription(compressionInfo.method)}
            </Text>
          </Box>

          {/* Statistics */}
          <Box flexDirection="column" marginBottom={1}>
            <Text color={theme.colors.text}>Compression Details:</Text>
            <Box marginLeft={2} flexDirection="column">
              <Text color={theme.colors.muted}>
                • Original size: {formatBytes(compressionInfo.originalSize)}
              </Text>
              <Text color={theme.colors.muted}>
                • Compressed size: {formatBytes(compressionInfo.compressedSize)}
              </Text>
              <Text color={theme.colors.muted}>
                • Space saved: {formatBytes(compressionInfo.originalSize - compressionInfo.compressedSize)}
              </Text>
              <Text color={theme.colors.muted}>
                • Messages affected: {compressionInfo.messagesAffected}
              </Text>
              <Text color={theme.colors.muted}>
                • Tokens removed: {compressionInfo.tokensRemoved.toLocaleString()}
              </Text>
            </Box>
          </Box>

          {/* Preserved context */}
          {compressionInfo.preservedContext.length > 0 && (
            <Box flexDirection="column">
              <Text color={theme.colors.text}>Preserved Context:</Text>
              <Box marginLeft={2} flexDirection="column">
                {compressionInfo.preservedContext.slice(0, 5).map((context, index) => (
                  <Text key={index} color={theme.colors.success}>
                    • {context}
                  </Text>
                ))}
                {compressionInfo.preservedContext.length > 5 && (
                  <Text color={theme.colors.muted}>
                    ... and {compressionInfo.preservedContext.length - 5} more items
                  </Text>
                )}
              </Box>
            </Box>
          )}

          {/* Impact notice */}
          <Box marginTop={1}>
            <Text color={theme.colors.info}>
              ℹ️ The AI may have reduced context about earlier parts of the conversation.
            </Text>
          </Box>
        </Box>
      )}
    </Box>
  );
};
