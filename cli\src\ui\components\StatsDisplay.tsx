// Statistics visualization component
import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';
import { Stats, StatGroup } from './Stats.js';

export interface SystemStats {
  session: {
    duration: number;
    messageCount: number;
    tokenCount: number;
    errorCount: number;
    successRate: number;
  };
  performance: {
    averageResponseTime: number;
    tokensPerSecond: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  usage: {
    totalSessions: number;
    totalMessages: number;
    totalTokens: number;
    favoriteProvider: string;
    favoriteModel: string;
  };
  providers: {
    [provider: string]: {
      messageCount: number;
      tokenCount: number;
      averageResponseTime: number;
      errorRate: number;
    };
  };
}

interface StatsDisplayProps {
  stats: SystemStats;
  refreshInterval?: number;
  showTrends?: boolean;
  view?: 'overview' | 'session' | 'performance' | 'usage' | 'providers';
  onViewChange?: (view: string) => void;
}

export const StatsDisplay: React.FC<StatsDisplayProps> = ({
  stats,
  refreshInterval = 5000,
  showTrends = true,
  view = 'overview',
  onViewChange
}) => {
  const theme = useTheme();
  const [currentView, setCurrentView] = useState(view);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setLastUpdate(new Date());
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshInterval]);

  useInput((input, key) => {
    if (key.tab) {
      const views = ['overview', 'session', 'performance', 'usage', 'providers'];
      const currentIndex = views.indexOf(currentView);
      const nextIndex = (currentIndex + 1) % views.length;
      const nextView = views[nextIndex];
      setCurrentView(nextView);
      onViewChange?.(nextView);
    }
  });

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  const formatBytes = (bytes: number) => {
    const mb = bytes / 1024 / 1024;
    return `${mb.toFixed(1)}MB`;
  };

  const getOverviewStats = (): StatGroup[] => [
    {
      title: 'Current Session',
      items: [
        {
          label: 'Duration',
          value: formatDuration(stats.session.duration),
          icon: '⏱️',
          color: theme.colors.info
        },
        {
          label: 'Messages',
          value: stats.session.messageCount,
          icon: '💬',
          color: theme.colors.primary
        },
        {
          label: 'Tokens',
          value: stats.session.tokenCount,
          icon: '🔤',
          color: theme.colors.secondary
        },
        {
          label: 'Success Rate',
          value: `${stats.session.successRate.toFixed(1)}%`,
          icon: '✅',
          color: stats.session.successRate > 90 ? theme.colors.success : theme.colors.warning
        }
      ]
    },
    {
      title: 'Performance',
      items: [
        {
          label: 'Avg Response',
          value: `${stats.performance.averageResponseTime.toFixed(1)}s`,
          icon: '⚡',
          color: theme.colors.warning
        },
        {
          label: 'Tokens/sec',
          value: stats.performance.tokensPerSecond.toFixed(1),
          icon: '🚀',
          color: theme.colors.success
        },
        {
          label: 'Memory',
          value: formatBytes(stats.performance.memoryUsage),
          icon: '🧠',
          color: theme.colors.info
        }
      ]
    }
  ];

  const getSessionStats = (): StatGroup[] => [
    {
      title: 'Session Details',
      items: [
        {
          label: 'Total Messages',
          value: stats.session.messageCount,
          icon: '💬'
        },
        {
          label: 'Total Tokens',
          value: stats.session.tokenCount,
          icon: '🔤'
        },
        {
          label: 'Errors',
          value: stats.session.errorCount,
          icon: '❌',
          color: stats.session.errorCount > 0 ? theme.colors.error : theme.colors.success
        },
        {
          label: 'Success Rate',
          value: `${stats.session.successRate.toFixed(1)}%`,
          icon: '📊',
          trend: {
            direction: stats.session.successRate > 95 ? 'up' : 'down',
            isGood: stats.session.successRate > 95
          }
        }
      ]
    }
  ];

  const getPerformanceStats = (): StatGroup[] => [
    {
      title: 'Response Times',
      items: [
        {
          label: 'Average',
          value: `${stats.performance.averageResponseTime.toFixed(1)}s`,
          icon: '⏱️'
        },
        {
          label: 'Throughput',
          value: `${stats.performance.tokensPerSecond.toFixed(1)} tok/s`,
          icon: '🚀'
        }
      ]
    },
    {
      title: 'System Resources',
      items: [
        {
          label: 'Memory Usage',
          value: formatBytes(stats.performance.memoryUsage),
          icon: '🧠',
          trend: {
            direction: stats.performance.memoryUsage > 100 * 1024 * 1024 ? 'up' : 'stable',
            isGood: stats.performance.memoryUsage < 100 * 1024 * 1024
          }
        },
        {
          label: 'CPU Usage',
          value: `${stats.performance.cpuUsage.toFixed(1)}%`,
          icon: '⚙️',
          trend: {
            direction: stats.performance.cpuUsage > 50 ? 'up' : 'stable',
            isGood: stats.performance.cpuUsage < 50
          }
        }
      ]
    }
  ];

  const getUsageStats = (): StatGroup[] => [
    {
      title: 'Overall Usage',
      items: [
        {
          label: 'Total Sessions',
          value: stats.usage.totalSessions,
          icon: '📊'
        },
        {
          label: 'Total Messages',
          value: stats.usage.totalMessages,
          icon: '💬'
        },
        {
          label: 'Total Tokens',
          value: stats.usage.totalTokens,
          icon: '🔤'
        }
      ]
    },
    {
      title: 'Preferences',
      items: [
        {
          label: 'Favorite Provider',
          value: stats.usage.favoriteProvider,
          icon: '🏆'
        },
        {
          label: 'Favorite Model',
          value: stats.usage.favoriteModel,
          icon: '🎯'
        }
      ]
    }
  ];

  const getProviderStats = (): StatGroup[] => {
    return Object.entries(stats.providers).map(([provider, providerStats]) => ({
      title: provider.toUpperCase(),
      items: [
        {
          label: 'Messages',
          value: providerStats.messageCount,
          icon: '💬'
        },
        {
          label: 'Tokens',
          value: providerStats.tokenCount,
          icon: '🔤'
        },
        {
          label: 'Avg Response',
          value: `${providerStats.averageResponseTime.toFixed(1)}s`,
          icon: '⏱️'
        },
        {
          label: 'Error Rate',
          value: `${(providerStats.errorRate * 100).toFixed(1)}%`,
          icon: '❌',
          color: providerStats.errorRate > 0.05 ? theme.colors.error : theme.colors.success
        }
      ]
    }));
  };

  const getStatsForView = (): StatGroup[] => {
    switch (currentView) {
      case 'session': return getSessionStats();
      case 'performance': return getPerformanceStats();
      case 'usage': return getUsageStats();
      case 'providers': return getProviderStats();
      default: return getOverviewStats();
    }
  };

  return (
    <Box flexDirection="column">
      {/* Header */}
      <Box marginBottom={1}>
        <Text color={theme.colors.primary}>
          📊 Statistics - {currentView.charAt(0).toUpperCase() + currentView.slice(1)}
        </Text>
        <Text color={theme.colors.muted}>
          {' '}(Press Tab to switch views)
        </Text>
      </Box>

      {/* Stats */}
      <Stats
        groups={getStatsForView()}
        layout={currentView === 'providers' ? 'grid' : 'vertical'}
        showTrends={showTrends}
      />

      {/* Footer */}
      <Box marginTop={1}>
        <Text color={theme.colors.muted}>
          Last updated: {lastUpdate.toLocaleTimeString()}
        </Text>
      </Box>
    </Box>
  );
};
