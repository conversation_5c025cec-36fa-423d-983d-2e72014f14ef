// Sandbox environment management
import { spawn } from 'child_process';
import { platform } from 'os';
import { join } from 'path';
import { logger } from '@arien/core';

export interface SandboxConfig {
  enabled: boolean;
  restrictive: boolean;
  allowedPaths: string[];
  blockedCommands: string[];
  networkAccess: boolean;
  timeoutMs: number;
}

export interface SandboxResult {
  success: boolean;
  output: string;
  error?: string;
  exitCode: number;
  timedOut: boolean;
}

export class SandboxManager {
  private config: SandboxConfig;

  constructor(config: SandboxConfig) {
    this.config = config;
  }

  public async executeCommand(
    command: string,
    args: string[] = [],
    workingDir: string = process.cwd()
  ): Promise<SandboxResult> {
    if (!this.config.enabled) {
      // No sandbox, execute directly
      return this.executeDirectly(command, args, workingDir);
    }

    // Check if command is blocked
    if (this.isCommandBlocked(command)) {
      return {
        success: false,
        output: '',
        error: `Command '${command}' is blocked by sandbox policy`,
        exitCode: 1,
        timedOut: false
      };
    }

    // Execute in sandbox based on platform
    switch (platform()) {
      case 'darwin':
        return this.executeMacOSSandbox(command, args, workingDir);
      case 'linux':
        return this.executeLinuxSandbox(command, args, workingDir);
      case 'win32':
        return this.executeWindowsSandbox(command, args, workingDir);
      default:
        logger.warn('Sandbox not supported on this platform, executing directly');
        return this.executeDirectly(command, args, workingDir);
    }
  }

  private isCommandBlocked(command: string): boolean {
    const blockedCommands = [
      'rm', 'del', 'format', 'fdisk', 'dd', 'mkfs',
      'shutdown', 'reboot', 'halt', 'poweroff',
      'sudo', 'su', 'chmod', 'chown'
    ];

    const allBlocked = [...blockedCommands, ...this.config.blockedCommands];
    return allBlocked.some(blocked => command.includes(blocked));
  }

  private async executeDirectly(
    command: string,
    args: string[],
    workingDir: string
  ): Promise<SandboxResult> {
    return new Promise((resolve) => {
      let output = '';
      let error = '';
      let timedOut = false;

      const child = spawn(command, args, {
        cwd: workingDir,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      const timeout = setTimeout(() => {
        timedOut = true;
        child.kill('SIGTERM');
      }, this.config.timeoutMs);

      child.stdout?.on('data', (data) => {
        output += data.toString();
      });

      child.stderr?.on('data', (data) => {
        error += data.toString();
      });

      child.on('close', (code) => {
        clearTimeout(timeout);
        resolve({
          success: code === 0 && !timedOut,
          output,
          error,
          exitCode: code || 0,
          timedOut
        });
      });

      child.on('error', (err) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          output,
          error: err.message,
          exitCode: 1,
          timedOut
        });
      });
    });
  }

  private async executeMacOSSandbox(
    command: string,
    args: string[],
    workingDir: string
  ): Promise<SandboxResult> {
    // Use macOS sandbox-exec
    const sandboxProfile = this.config.restrictive 
      ? 'sandbox-macos-restrictive-closed.sb'
      : 'sandbox-macos-permissive-open.sb';
    
    const sandboxArgs = [
      '-f', join(__dirname, '..', 'utils', sandboxProfile),
      command,
      ...args
    ];

    return this.executeDirectly('sandbox-exec', sandboxArgs, workingDir);
  }

  private async executeLinuxSandbox(
    command: string,
    args: string[],
    workingDir: string
  ): Promise<SandboxResult> {
    // Use firejail or bubblewrap if available
    try {
      // Try firejail first
      const firejailArgs = [
        '--quiet',
        '--noprofile',
        '--private-tmp',
        '--noroot',
        '--net=none',
        '--seccomp',
        `--cwd=${workingDir}`,
        command,
        ...args
      ];

      return this.executeDirectly('firejail', firejailArgs, workingDir);
    } catch (error) {
      logger.warn('Firejail not available, trying bubblewrap');
      
      try {
        // Try bubblewrap
        const bwrapArgs = [
          '--ro-bind', '/', '/',
          '--dev', '/dev',
          '--proc', '/proc',
          '--tmpfs', '/tmp',
          '--unshare-net',
          '--chdir', workingDir,
          command,
          ...args
        ];

        return this.executeDirectly('bwrap', bwrapArgs, workingDir);
      } catch (bwrapError) {
        logger.warn('No sandbox available on Linux, executing directly');
        return this.executeDirectly(command, args, workingDir);
      }
    }
  }

  private async executeWindowsSandbox(
    command: string,
    args: string[],
    workingDir: string
  ): Promise<SandboxResult> {
    // Windows sandbox is more complex, for now execute with restrictions
    logger.warn('Windows sandbox not fully implemented, using restricted execution');
    
    // Basic restrictions: no system commands
    const systemCommands = ['format', 'del', 'rd', 'shutdown', 'restart'];
    if (systemCommands.some(cmd => command.toLowerCase().includes(cmd))) {
      return {
        success: false,
        output: '',
        error: 'System command blocked by sandbox',
        exitCode: 1,
        timedOut: false
      };
    }

    return this.executeDirectly(command, args, workingDir);
  }

  public updateConfig(newConfig: Partial<SandboxConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): SandboxConfig {
    return { ...this.config };
  }

  public isEnabled(): boolean {
    return this.config.enabled;
  }
}

// Default sandbox configuration
export const defaultSandboxConfig: SandboxConfig = {
  enabled: true,
  restrictive: true,
  allowedPaths: ['/tmp', '/var/tmp'],
  blockedCommands: ['rm -rf', 'format', 'fdisk'],
  networkAccess: false,
  timeoutMs: 30000
};

// Global sandbox manager
export const sandboxManager = new SandboxManager(defaultSandboxConfig);
