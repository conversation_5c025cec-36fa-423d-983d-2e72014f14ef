// Authentication setup dialog component
import React, { useState, useEffect } from 'react';
import { Box, Text, Newline } from 'ink';
import TextInput from 'ink-text-input';
import SelectInput from 'ink-select-input';
import { Provider, PROVIDERS, getModelsByProvider, getDefaultModelForProvider } from '@arien/core';
import { getConfig } from '@arien/core';

interface AuthDialogProps {
  onComplete: () => void;
  onCancel: () => void;
}

interface ProviderOption {
  label: string;
  value: Provider;
  description: string;
}

interface ModelOption {
  label: string;
  value: string;
}

const PROVIDER_OPTIONS: ProviderOption[] = [
  {
    label: 'DeepSeek',
    value: PROVIDERS.DEEPSEEK,
    description: 'High-performance AI models with competitive pricing'
  },
  {
    label: 'OpenAI',
    value: PROVIDERS.OPENAI,
    description: 'GPT-4 and GPT-3.5 models from OpenAI'
  },
  {
    label: 'Anthropic',
    value: PROVIDERS.ANTHROPIC,
    description: 'Claude models with strong reasoning capabilities'
  },
  {
    label: 'Google',
    value: PROVIDERS.GOOGLE,
    description: 'Gemini models with multimodal capabilities'
  }
];

enum AuthStep {
  SELECT_PROVIDER = 'SELECT_PROVIDER',
  ENTER_API_KEY = 'ENTER_API_KEY',
  ENTER_BASE_URL = 'ENTER_BASE_URL',
  SELECT_MODEL = 'SELECT_MODEL',
  CONFIRM = 'CONFIRM'
}

export const AuthDialog: React.FC<AuthDialogProps> = ({ onComplete, onCancel }) => {
  const [step, setStep] = useState<AuthStep>(AuthStep.SELECT_PROVIDER);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const config = getConfig();

  const handleProviderSelect = (item: ProviderOption) => {
    setSelectedProvider(item.value);
    setError('');
    
    // Set default base URL for DeepSeek
    if (item.value === PROVIDERS.DEEPSEEK) {
      setBaseUrl('https://api.deepseek.com');
    } else {
      setBaseUrl('');
    }
    
    setStep(AuthStep.ENTER_API_KEY);
  };

  const handleApiKeySubmit = () => {
    if (!apiKey.trim()) {
      setError('API key is required');
      return;
    }
    
    setError('');
    
    // For DeepSeek, ask for base URL, for others go to model selection
    if (selectedProvider === PROVIDERS.DEEPSEEK) {
      setStep(AuthStep.ENTER_BASE_URL);
    } else {
      setStep(AuthStep.SELECT_MODEL);
    }
  };

  const handleBaseUrlSubmit = () => {
    if (!baseUrl.trim()) {
      setError('Base URL is required for DeepSeek');
      return;
    }
    
    setError('');
    setStep(AuthStep.SELECT_MODEL);
  };

  const handleModelSelect = (item: ModelOption) => {
    setSelectedModel(item.value);
    setStep(AuthStep.CONFIRM);
  };

  const handleConfirm = async () => {
    if (!selectedProvider) return;
    
    setIsLoading(true);
    setError('');
    
    try {
      // Save provider configuration
      const providerConfig: any = { apiKey };
      if (baseUrl && selectedProvider === PROVIDERS.DEEPSEEK) {
        providerConfig.baseUrl = baseUrl;
      }
      
      config.setProviderConfig(selectedProvider, providerConfig);
      
      // Set as default provider and model
      config.updateConfig({
        defaultProvider: selectedProvider,
        defaultModel: selectedModel
      });
      
      // Test the configuration by making a simple request
      // This would be implemented with a test API call
      
      onComplete();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Configuration failed');
      setIsLoading(false);
    }
  };

  const getModelOptions = (): ModelOption[] => {
    if (!selectedProvider) return [];
    
    const models = getModelsByProvider(selectedProvider);
    return models.map(model => ({
      label: `${model.name} (${model.id})`,
      value: model.id
    }));
  };

  const renderProviderSelection = () => (
    <Box flexDirection="column">
      <Text bold color="blue">
        🔐 Configure AI Provider
      </Text>
      <Newline />
      <Text>Select an AI provider to configure:</Text>
      <Newline />
      
      <SelectInput
        items={PROVIDER_OPTIONS}
        onSelect={handleProviderSelect}
        itemComponent={({ item, isSelected }) => (
          <Box>
            <Text color={isSelected ? 'blue' : 'white'}>
              {isSelected ? '❯ ' : '  '}
              {item.label}
            </Text>
            {isSelected && (
              <Box marginLeft={4}>
                <Text dimColor>{item.description}</Text>
              </Box>
            )}
          </Box>
        )}
      />
      
      <Newline />
      <Text dimColor>Press Ctrl+C to cancel</Text>
    </Box>
  );

  const renderApiKeyInput = () => (
    <Box flexDirection="column">
      <Text bold color="blue">
        🔑 API Key Configuration
      </Text>
      <Newline />
      <Text>
        Enter your {PROVIDER_OPTIONS.find(p => p.value === selectedProvider)?.label} API key:
      </Text>
      <Newline />
      
      <Box>
        <Text>API Key: </Text>
        <TextInput
          value={apiKey}
          onChange={setApiKey}
          onSubmit={handleApiKeySubmit}
          placeholder="sk-..."
          mask="*"
        />
      </Box>
      
      {error && (
        <>
          <Newline />
          <Text color="red">❌ {error}</Text>
        </>
      )}
      
      <Newline />
      <Text dimColor>Press Enter to continue, Ctrl+C to cancel</Text>
    </Box>
  );

  const renderBaseUrlInput = () => (
    <Box flexDirection="column">
      <Text bold color="blue">
        🌐 Base URL Configuration
      </Text>
      <Newline />
      <Text>Enter the base URL for DeepSeek API:</Text>
      <Newline />
      
      <Box>
        <Text>Base URL: </Text>
        <TextInput
          value={baseUrl}
          onChange={setBaseUrl}
          onSubmit={handleBaseUrlSubmit}
          placeholder="https://api.deepseek.com"
        />
      </Box>
      
      {error && (
        <>
          <Newline />
          <Text color="red">❌ {error}</Text>
        </>
      )}
      
      <Newline />
      <Text dimColor>Press Enter to continue, Ctrl+C to cancel</Text>
    </Box>
  );

  const renderModelSelection = () => {
    const modelOptions = getModelOptions();
    
    return (
      <Box flexDirection="column">
        <Text bold color="blue">
          🤖 Model Selection
        </Text>
        <Newline />
        <Text>Select a default model:</Text>
        <Newline />
        
        <SelectInput
          items={modelOptions}
          onSelect={handleModelSelect}
          initialIndex={0}
        />
        
        <Newline />
        <Text dimColor>Press Enter to select, Ctrl+C to cancel</Text>
      </Box>
    );
  };

  const renderConfirmation = () => (
    <Box flexDirection="column">
      <Text bold color="green">
        ✅ Configuration Summary
      </Text>
      <Newline />
      
      <Box flexDirection="column" marginLeft={2}>
        <Text>
          <Text bold>Provider:</Text> {PROVIDER_OPTIONS.find(p => p.value === selectedProvider)?.label}
        </Text>
        <Text>
          <Text bold>API Key:</Text> {apiKey.substring(0, 8)}...
        </Text>
        {baseUrl && (
          <Text>
            <Text bold>Base URL:</Text> {baseUrl}
          </Text>
        )}
        <Text>
          <Text bold>Default Model:</Text> {selectedModel}
        </Text>
      </Box>
      
      <Newline />
      
      {isLoading ? (
        <Text color="yellow">⏳ Saving configuration...</Text>
      ) : (
        <>
          <Text>Press Enter to save configuration, or Ctrl+C to cancel</Text>
          <Newline />
          <Box>
            <TextInput
              value=""
              onChange={() => {}}
              onSubmit={handleConfirm}
              placeholder="Press Enter to confirm"
            />
          </Box>
        </>
      )}
      
      {error && (
        <>
          <Newline />
          <Text color="red">❌ {error}</Text>
        </>
      )}
    </Box>
  );

  // Handle Ctrl+C
  useEffect(() => {
    const handleExit = () => {
      onCancel();
    };

    process.on('SIGINT', handleExit);
    return () => {
      process.off('SIGINT', handleExit);
    };
  }, [onCancel]);

  switch (step) {
    case AuthStep.SELECT_PROVIDER:
      return renderProviderSelection();
    case AuthStep.ENTER_API_KEY:
      return renderApiKeyInput();
    case AuthStep.ENTER_BASE_URL:
      return renderBaseUrlInput();
    case AuthStep.SELECT_MODEL:
      return renderModelSelection();
    case AuthStep.CONFIRM:
      return renderConfirmation();
    default:
      return <Text>Unknown step</Text>;
  }
};
