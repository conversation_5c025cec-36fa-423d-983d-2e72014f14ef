// Session management utilities
import { randomUUID } from 'crypto';

export interface Session {
  id: string;
  userId: string;
  startTime: Date;
  lastActivity: Date;
  metadata: Record<string, any>;
}

export class SessionManager {
  private sessions = new Map<string, Session>();

  public createSession(userId: string, metadata: Record<string, any> = {}): Session {
    const session: Session = {
      id: randomUUID(),
      userId,
      startTime: new Date(),
      lastActivity: new Date(),
      metadata
    };

    this.sessions.set(session.id, session);
    return session;
  }

  public getSession(sessionId: string): Session | undefined {
    return this.sessions.get(sessionId);
  }

  public updateSession(sessionId: string, updates: Partial<Session>): boolean {
    const session = this.sessions.get(sessionId);
    if (!session) return false;

    Object.assign(session, updates, { lastActivity: new Date() });
    return true;
  }

  public deleteSession(sessionId: string): boolean {
    return this.sessions.delete(sessionId);
  }

  public getActiveSessions(): Session[] {
    return Array.from(this.sessions.values());
  }

  public cleanupExpiredSessions(maxAgeMs: number = 24 * 60 * 60 * 1000): number {
    const now = new Date();
    let cleaned = 0;

    for (const [id, session] of this.sessions) {
      if (now.getTime() - session.lastActivity.getTime() > maxAgeMs) {
        this.sessions.delete(id);
        cleaned++;
      }
    }

    return cleaned;
  }
}

// Global session manager
export const sessionManager = new SessionManager();
