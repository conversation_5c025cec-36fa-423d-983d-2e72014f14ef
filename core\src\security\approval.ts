// Tool approval system with different levels
import { logger } from '../core/logger.js';
import { ApprovalRequest } from './sandbox.js';

export type ApprovalLevel = 'default' | 'auto-edit' | 'yolo';

export interface ApprovalResponse {
  approved: boolean;
  reason?: string;
  conditions?: string[];
  expiresAt?: Date;
}

export interface ApprovalRule {
  id: string;
  name: string;
  description: string;
  condition: (request: ApprovalRequest) => boolean;
  action: 'allow' | 'deny' | 'prompt';
  priority: number;
}

export interface ApprovalPolicy {
  name: string;
  description: string;
  level: ApprovalLevel;
  rules: ApprovalRule[];
  defaultAction: 'allow' | 'deny' | 'prompt';
}

export interface ApprovalContext {
  userId: string;
  sessionId: string;
  approvalLevel: ApprovalLevel;
  autoApproveTimeout?: number;
  interactiveMode: boolean;
}

export class ApprovalManager {
  private policies = new Map<ApprovalLevel, ApprovalPolicy>();
  private customRules = new Map<string, ApprovalRule[]>();
  private approvalHistory: Array<{
    request: ApprovalRequest;
    response: ApprovalResponse;
    timestamp: Date;
    userId: string;
  }> = [];

  constructor() {
    this.initializeDefaultPolicies();
  }

  public async requestApproval(
    request: ApprovalRequest,
    context: ApprovalContext
  ): Promise<ApprovalResponse> {
    logger.debug('Processing approval request', {
      operation: request.operation,
      resource: request.resource,
      riskLevel: request.riskLevel,
      approvalLevel: context.approvalLevel
    });

    // Get applicable policy
    const policy = this.policies.get(context.approvalLevel);
    if (!policy) {
      throw new Error(`Unknown approval level: ${context.approvalLevel}`);
    }

    // Check custom rules first
    const customRules = this.customRules.get(context.userId) || [];
    const allRules = [...customRules, ...policy.rules].sort((a, b) => b.priority - a.priority);

    // Evaluate rules
    for (const rule of allRules) {
      if (rule.condition(request)) {
        const response = await this.executeRuleAction(rule, request, context);
        if (response) {
          this.recordApproval(request, response, context.userId);
          return response;
        }
      }
    }

    // Apply default policy action
    const response = await this.executeDefaultAction(policy.defaultAction, request, context);
    this.recordApproval(request, response, context.userId);
    return response;
  }

  public addCustomRule(userId: string, rule: ApprovalRule): void {
    if (!this.customRules.has(userId)) {
      this.customRules.set(userId, []);
    }
    this.customRules.get(userId)!.push(rule);
    
    logger.info('Custom approval rule added', {
      userId,
      ruleId: rule.id,
      ruleName: rule.name
    });
  }

  public removeCustomRule(userId: string, ruleId: string): boolean {
    const rules = this.customRules.get(userId);
    if (!rules) return false;

    const index = rules.findIndex(rule => rule.id === ruleId);
    if (index === -1) return false;

    rules.splice(index, 1);
    logger.info('Custom approval rule removed', { userId, ruleId });
    return true;
  }

  public getApprovalHistory(
    userId?: string,
    limit: number = 100
  ): Array<{
    request: ApprovalRequest;
    response: ApprovalResponse;
    timestamp: Date;
    userId: string;
  }> {
    let history = this.approvalHistory;
    
    if (userId) {
      history = history.filter(entry => entry.userId === userId);
    }

    return history
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  public getApprovalStats(userId?: string): {
    total: number;
    approved: number;
    denied: number;
    approvalRate: number;
    riskLevelBreakdown: Record<string, number>;
  } {
    let history = this.approvalHistory;
    
    if (userId) {
      history = history.filter(entry => entry.userId === userId);
    }

    const total = history.length;
    const approved = history.filter(entry => entry.response.approved).length;
    const denied = total - approved;
    const approvalRate = total > 0 ? approved / total : 0;

    const riskLevelBreakdown: Record<string, number> = {};
    for (const entry of history) {
      const riskLevel = entry.request.riskLevel;
      riskLevelBreakdown[riskLevel] = (riskLevelBreakdown[riskLevel] || 0) + 1;
    }

    return {
      total,
      approved,
      denied,
      approvalRate,
      riskLevelBreakdown
    };
  }

  private async executeRuleAction(
    rule: ApprovalRule,
    request: ApprovalRequest,
    context: ApprovalContext
  ): Promise<ApprovalResponse | null> {
    switch (rule.action) {
      case 'allow':
        logger.debug('Request auto-approved by rule', {
          ruleId: rule.id,
          ruleName: rule.name
        });
        return {
          approved: true,
          reason: `Approved by rule: ${rule.name}`
        };

      case 'deny':
        logger.debug('Request auto-denied by rule', {
          ruleId: rule.id,
          ruleName: rule.name
        });
        return {
          approved: false,
          reason: `Denied by rule: ${rule.name}`
        };

      case 'prompt':
        if (!context.interactiveMode) {
          logger.debug('Interactive prompt required but not in interactive mode', {
            ruleId: rule.id
          });
          return {
            approved: false,
            reason: 'Interactive approval required but not available'
          };
        }
        return await this.promptUser(request, context);

      default:
        return null;
    }
  }

  private async executeDefaultAction(
    action: 'allow' | 'deny' | 'prompt',
    request: ApprovalRequest,
    context: ApprovalContext
  ): Promise<ApprovalResponse> {
    switch (action) {
      case 'allow':
        return {
          approved: true,
          reason: 'Approved by default policy'
        };

      case 'deny':
        return {
          approved: false,
          reason: 'Denied by default policy'
        };

      case 'prompt':
        if (!context.interactiveMode) {
          return {
            approved: false,
            reason: 'Interactive approval required but not available'
          };
        }
        return await this.promptUser(request, context);
    }
  }

  private async promptUser(
    request: ApprovalRequest,
    context: ApprovalContext
  ): Promise<ApprovalResponse> {
    // This would integrate with the CLI interface to prompt the user
    // For now, we'll simulate based on risk level and timeout
    
    logger.info('User approval required', {
      operation: request.operation,
      resource: request.resource,
      riskLevel: request.riskLevel
    });

    // Simulate user response based on risk level
    // In a real implementation, this would show a prompt to the user
    const autoApprove = request.riskLevel === 'low' && context.autoApproveTimeout;
    
    if (autoApprove) {
      logger.debug('Auto-approving low-risk operation due to timeout');
      return {
        approved: true,
        reason: 'Auto-approved (low risk + timeout)',
        expiresAt: new Date(Date.now() + (context.autoApproveTimeout || 0))
      };
    }

    // Default to deny for safety
    return {
      approved: false,
      reason: 'User approval required but not implemented'
    };
  }

  private recordApproval(
    request: ApprovalRequest,
    response: ApprovalResponse,
    userId: string
  ): void {
    this.approvalHistory.push({
      request,
      response,
      timestamp: new Date(),
      userId
    });

    // Keep only last 10000 entries
    if (this.approvalHistory.length > 10000) {
      this.approvalHistory = this.approvalHistory.slice(-10000);
    }

    logger.debug('Approval recorded', {
      operation: request.operation,
      approved: response.approved,
      reason: response.reason,
      userId
    });
  }

  private initializeDefaultPolicies(): void {
    // Default policy - prompt for medium/high risk operations
    this.policies.set('default', {
      name: 'Default',
      description: 'Standard approval policy with prompts for risky operations',
      level: 'default',
      rules: [
        {
          id: 'auto-approve-read',
          name: 'Auto-approve safe read operations',
          description: 'Automatically approve low-risk read operations',
          condition: (req) => req.operation.includes('read') && req.riskLevel === 'low',
          action: 'allow',
          priority: 100
        },
        {
          id: 'deny-high-risk',
          name: 'Deny high-risk operations',
          description: 'Automatically deny high-risk operations',
          condition: (req) => req.riskLevel === 'high',
          action: 'deny',
          priority: 90
        },
        {
          id: 'prompt-medium-risk',
          name: 'Prompt for medium-risk operations',
          description: 'Require user approval for medium-risk operations',
          condition: (req) => req.riskLevel === 'medium',
          action: 'prompt',
          priority: 80
        }
      ],
      defaultAction: 'prompt'
    });

    // Auto-edit policy - auto-approve most operations
    this.policies.set('auto-edit', {
      name: 'Auto-edit',
      description: 'Automatically approve most operations except high-risk ones',
      level: 'auto-edit',
      rules: [
        {
          id: 'deny-destructive',
          name: 'Deny destructive operations',
          description: 'Deny operations that could cause data loss',
          condition: (req) => req.operation.includes('delete') || req.operation.includes('format'),
          action: 'deny',
          priority: 100
        },
        {
          id: 'prompt-high-risk',
          name: 'Prompt for high-risk operations',
          description: 'Require approval for high-risk operations',
          condition: (req) => req.riskLevel === 'high',
          action: 'prompt',
          priority: 90
        }
      ],
      defaultAction: 'allow'
    });

    // YOLO policy - approve everything
    this.policies.set('yolo', {
      name: 'YOLO',
      description: 'Approve all operations without prompts (dangerous!)',
      level: 'yolo',
      rules: [],
      defaultAction: 'allow'
    });

    logger.info('Default approval policies initialized');
  }

  public createRule(
    id: string,
    name: string,
    description: string,
    condition: (request: ApprovalRequest) => boolean,
    action: 'allow' | 'deny' | 'prompt',
    priority: number = 50
  ): ApprovalRule {
    return {
      id,
      name,
      description,
      condition,
      action,
      priority
    };
  }

  public getPolicy(level: ApprovalLevel): ApprovalPolicy | undefined {
    return this.policies.get(level);
  }

  public updatePolicy(level: ApprovalLevel, policy: ApprovalPolicy): void {
    this.policies.set(level, policy);
    logger.info('Approval policy updated', { level, policyName: policy.name });
  }
}
