// Telemetry constants
export const TELEMETRY_EVENTS = {
  APP_STARTED: 'app_started',
  APP_STOPPED: 'app_stopped',
  COMMAND_EXECUTED: 'command_executed',
  TOOL_USED: 'tool_used',
  PROVIDER_SWITCHED: 'provider_switched',
  MODEL_CHANGED: 'model_changed',
  FILE_ACCESSED: 'file_accessed',
  NETWORK_REQUEST: 'network_request',
  ERROR_OCCURRED: 'error_occurred',
  APPROVAL_REQUESTED: 'approval_requested',
  APPROVAL_GRANTED: 'approval_granted',
  APPROVAL_DENIED: 'approval_denied',
  EXTENSION_LOADED: 'extension_loaded',
  EXTENSION_UNLOADED: 'extension_unloaded',
  CONFIG_CHANGED: 'config_changed',
  THEME_CHANGED: 'theme_changed',
  SANDBOX_VIOLATION: 'sandbox_violation',
  PERFORMANCE_ISSUE: 'performance_issue',
  MEMORY_WARNING: 'memory_warning',
  RATE_LIMIT_HIT: 'rate_limit_hit',
  AUTHENTICATION_FAILED: 'authentication_failed',
  SESSION_STARTED: 'session_started',
  SESSION_ENDED: 'session_ended'
} as const;

export const TELEMETRY_METRICS = {
  RESPONSE_TIME: 'response_time',
  TOKEN_COUNT: 'token_count',
  REQUEST_SIZE: 'request_size',
  RESPONSE_SIZE: 'response_size',
  ERROR_COUNT: 'error_count',
  RETRY_COUNT: 'retry_count',
  CACHE_HIT_RATE: 'cache_hit_rate',
  MEMORY_USAGE: 'memory_usage',
  CPU_USAGE: 'cpu_usage',
  DISK_USAGE: 'disk_usage',
  NETWORK_LATENCY: 'network_latency',
  COMMANDS_PER_MINUTE: 'commands_per_minute',
  TOOLS_PER_SESSION: 'tools_per_session',
  FILES_PER_SESSION: 'files_per_session',
  SESSION_DURATION: 'session_duration',
  APPROVAL_RATE: 'approval_rate',
  EXTENSION_COUNT: 'extension_count',
  CONFIG_CHANGES: 'config_changes',
  SANDBOX_VIOLATIONS: 'sandbox_violations',
  AUTHENTICATION_ATTEMPTS: 'authentication_attempts'
} as const;

export const DEFAULT_TELEMETRY_CONFIG = {
  enabled: true,
  batchSize: 50,
  flushInterval: 30000, // 30 seconds
  maxRetries: 3,
  timeout: 10000, // 10 seconds
  debug: false,
  anonymize: true,
  collectSystemInfo: true,
  collectUsageStats: true,
  collectErrorReports: true,
  collectPerformanceMetrics: true
};

export const TELEMETRY_ENDPOINTS = {
  PRODUCTION: 'https://telemetry.arien-cli.com/v1',
  STAGING: 'https://telemetry-staging.arien-cli.com/v1',
  DEVELOPMENT: 'http://localhost:8080/v1'
};

export const METRIC_UNITS = {
  MILLISECONDS: 'ms',
  SECONDS: 's',
  BYTES: 'bytes',
  KILOBYTES: 'kb',
  MEGABYTES: 'mb',
  GIGABYTES: 'gb',
  COUNT: 'count',
  PERCENTAGE: 'percent',
  RATE: 'rate',
  TOKENS: 'tokens'
};

export const ERROR_SEVERITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;

export const TELEMETRY_CATEGORIES = {
  SYSTEM: 'system',
  USAGE: 'usage',
  PERFORMANCE: 'performance',
  ERROR: 'error',
  SECURITY: 'security',
  FEATURE: 'feature',
  CONFIGURATION: 'configuration',
  EXTENSION: 'extension'
} as const;

export const COLLECTION_INTERVALS = {
  SYSTEM_METRICS: 60000, // 1 minute
  PERFORMANCE_METRICS: 30000, // 30 seconds
  USAGE_STATS: 300000, // 5 minutes
  ERROR_REPORTS: 0, // Immediate
  MEMORY_USAGE: 10000, // 10 seconds
  CPU_USAGE: 15000, // 15 seconds
  DISK_USAGE: 120000, // 2 minutes
  NETWORK_METRICS: 30000 // 30 seconds
};

export const RETENTION_PERIODS = {
  EVENTS: 30 * 24 * 60 * 60 * 1000, // 30 days
  METRICS: 90 * 24 * 60 * 60 * 1000, // 90 days
  ERROR_REPORTS: 180 * 24 * 60 * 60 * 1000, // 180 days
  SYSTEM_INFO: 7 * 24 * 60 * 60 * 1000, // 7 days
  PERFORMANCE_DATA: 60 * 24 * 60 * 60 * 1000 // 60 days
};

export const ANONYMIZATION_RULES = {
  USER_ID: (value: string) => `user_${hashString(value)}`,
  SESSION_ID: (value: string) => `session_${hashString(value)}`,
  FILE_PATH: (value: string) => anonymizeFilePath(value),
  COMMAND: (value: string) => anonymizeCommand(value),
  ERROR_MESSAGE: (value: string) => anonymizeErrorMessage(value),
  URL: (value: string) => anonymizeUrl(value)
};

export const PERFORMANCE_THRESHOLDS = {
  RESPONSE_TIME_WARNING: 5000, // 5 seconds
  RESPONSE_TIME_CRITICAL: 15000, // 15 seconds
  MEMORY_USAGE_WARNING: 500 * 1024 * 1024, // 500MB
  MEMORY_USAGE_CRITICAL: 1024 * 1024 * 1024, // 1GB
  CPU_USAGE_WARNING: 80, // 80%
  CPU_USAGE_CRITICAL: 95, // 95%
  ERROR_RATE_WARNING: 0.05, // 5%
  ERROR_RATE_CRITICAL: 0.15, // 15%
  DISK_USAGE_WARNING: 0.85, // 85%
  DISK_USAGE_CRITICAL: 0.95 // 95%
};

export const SAMPLING_RATES = {
  EVENTS: {
    HIGH_FREQUENCY: 0.1, // 10% sampling for high-frequency events
    MEDIUM_FREQUENCY: 0.5, // 50% sampling for medium-frequency events
    LOW_FREQUENCY: 1.0, // 100% sampling for low-frequency events
    ERROR_EVENTS: 1.0, // Always sample error events
    CRITICAL_EVENTS: 1.0 // Always sample critical events
  },
  METRICS: {
    SYSTEM_METRICS: 1.0,
    PERFORMANCE_METRICS: 1.0,
    USAGE_METRICS: 0.8,
    DEBUG_METRICS: 0.1
  }
};

export const TELEMETRY_HEADERS = {
  CONTENT_TYPE: 'application/json',
  USER_AGENT: 'Arien-CLI-Telemetry',
  API_VERSION: 'v1',
  CLIENT_VERSION: '1.0.0'
};

export const BATCH_LIMITS = {
  MAX_EVENTS_PER_BATCH: 100,
  MAX_METRICS_PER_BATCH: 200,
  MAX_BATCH_SIZE_BYTES: 1024 * 1024, // 1MB
  MAX_EVENT_SIZE_BYTES: 64 * 1024, // 64KB
  MAX_METRIC_SIZE_BYTES: 1024 // 1KB
};

export const QUEUE_LIMITS = {
  MAX_QUEUE_SIZE: 10000,
  MAX_MEMORY_USAGE: 50 * 1024 * 1024, // 50MB
  FLUSH_ON_QUEUE_SIZE: 1000,
  FLUSH_ON_MEMORY_USAGE: 10 * 1024 * 1024 // 10MB
};

// Helper functions for anonymization
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

function anonymizeFilePath(filePath: string): string {
  // Replace user-specific parts with placeholders
  return filePath
    .replace(/\/Users\/<USER>\/]+/g, '/Users/<USER>')
    .replace(/\/home\/<USER>\/]+/g, '/home/<USER>')
    .replace(/C:\\Users\\<USER>\\]+/g, 'C:\\Users\\<USER>\/tmp\/[^\/]+/g, '/tmp/<temp>')
    .replace(/\.arien\/[^\/]+/g, '.arien/<config>');
}

function anonymizeCommand(command: string): string {
  // Remove sensitive arguments while preserving command structure
  return command
    .replace(/--api-key\s+\S+/g, '--api-key <redacted>')
    .replace(/--token\s+\S+/g, '--token <redacted>')
    .replace(/--password\s+\S+/g, '--password <redacted>')
    .replace(/--secret\s+\S+/g, '--secret <redacted>');
}

function anonymizeErrorMessage(message: string): string {
  // Remove file paths and sensitive information from error messages
  return message
    .replace(/\/[^\s]+/g, '<path>')
    .replace(/C:\\[^\s]+/g, '<path>')
    .replace(/api[_-]?key[:\s=]+\S+/gi, 'api_key=<redacted>')
    .replace(/token[:\s=]+\S+/gi, 'token=<redacted>');
}

function anonymizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Keep protocol and hostname, remove query parameters and paths that might contain sensitive data
    return `${parsed.protocol}//${parsed.hostname}${parsed.pathname.includes('api') ? '/api/<endpoint>' : parsed.pathname}`;
  } catch {
    return '<invalid-url>';
  }
}
