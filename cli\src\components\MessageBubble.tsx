// Individual message bubble component
import React from 'react';
import { Box, Text } from 'ink';
import { ChatMessage } from '@arien/core';
import { CodeBlock } from './CodeBlock.js';
import { ToolCallDisplay } from './ToolCallDisplay.js';
import { useTheme } from '../hooks/useTheme.js';

export interface MessageBubbleProps {
  message: ChatMessage;
  isLast?: boolean;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({ message, isLast = false }) => {
  const theme = useTheme();
  
  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isSystem = message.role === 'system';

  const getBorderColor = () => {
    if (isUser) return theme.colors.user;
    if (isAssistant) return theme.colors.assistant;
    if (isSystem) return theme.colors.system;
    return theme.colors.border;
  };

  const getHeaderColor = () => {
    if (isUser) return theme.colors.user;
    if (isAssistant) return theme.colors.assistant;
    if (isSystem) return theme.colors.system;
    return theme.colors.text;
  };

  const getRoleLabel = () => {
    if (isUser) return 'You';
    if (isAssistant) return 'Arien';
    if (isSystem) return 'System';
    return message.role;
  };

  // Parse content for code blocks and other formatting
  const parseContent = (content: string) => {
    const parts: React.ReactNode[] = [];
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    let lastIndex = 0;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        const textBefore = content.slice(lastIndex, match.index);
        if (textBefore.trim()) {
          parts.push(
            <Text key={`text-${lastIndex}`} color={theme.colors.text}>
              {textBefore}
            </Text>
          );
        }
      }

      // Add code block
      const language = match[1] || 'text';
      const code = match[2];
      parts.push(
        <CodeBlock
          key={`code-${match.index}`}
          language={language}
          code={code}
        />
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const remainingText = content.slice(lastIndex);
      if (remainingText.trim()) {
        parts.push(
          <Text key={`text-${lastIndex}`} color={theme.colors.text}>
            {remainingText}
          </Text>
        );
      }
    }

    return parts.length > 0 ? parts : [
      <Text key="content" color={theme.colors.text}>
        {content}
      </Text>
    ];
  };

  return (
    <Box flexDirection="column" marginY={1}>
      {/* Message header */}
      <Box marginBottom={1}>
        <Text color={getHeaderColor()} bold>
          {getRoleLabel()}
        </Text>
        <Box marginLeft="auto">
          <Text color={theme.colors.muted} dimColor>
            {new Date().toLocaleTimeString()}
          </Text>
        </Box>
      </Box>

      {/* Message content */}
      <Box
        borderStyle="round"
        borderColor={getBorderColor()}
        paddingX={2}
        paddingY={1}
        flexDirection="column"
      >
        {/* Main content */}
        <Box flexDirection="column">
          {parseContent(message.content)}
        </Box>

        {/* Tool calls */}
        {message.toolCalls && message.toolCalls.length > 0 && (
          <Box flexDirection="column" marginTop={1}>
            <Text color={theme.colors.muted} bold>
              Tool Calls:
            </Text>
            {message.toolCalls.map((toolCall, index) => (
              <ToolCallDisplay
                key={index}
                toolCall={toolCall}
              />
            ))}
          </Box>
        )}

        {/* Tool results */}
        {message.toolResults && message.toolResults.length > 0 && (
          <Box flexDirection="column" marginTop={1}>
            <Text color={theme.colors.muted} bold>
              Tool Results:
            </Text>
            {message.toolResults.map((result, index) => (
              <Box key={index} flexDirection="column" marginLeft={2}>
                <Text color={theme.colors.success}>
                  ✓ {result.toolName}
                </Text>
                {result.data && (
                  <Box marginLeft={2}>
                    <Text color={theme.colors.text}>
                      {typeof result.data === 'string' 
                        ? result.data 
                        : JSON.stringify(result.data, null, 2)
                      }
                    </Text>
                  </Box>
                )}
                {result.error && (
                  <Box marginLeft={2}>
                    <Text color={theme.colors.error}>
                      Error: {result.error}
                    </Text>
                  </Box>
                )}
              </Box>
            ))}
          </Box>
        )}
      </Box>
    </Box>
  );
};
