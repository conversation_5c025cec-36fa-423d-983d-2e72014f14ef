// Message list component with syntax highlighting
import React from 'react';
import { Box, Text } from 'ink';
import { ChatMessage } from '@arien/core';
import { MessageBubble } from './MessageBubble.js';
import { LoadingSpinner } from './LoadingSpinner.js';
import { ErrorDisplay } from './ErrorDisplay.js';
import { useTheme } from '../hooks/useTheme.js';

export interface MessageListProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  error?: Error | null;
}

export const MessageList: React.FC<MessageListProps> = ({ 
  messages, 
  isLoading = false, 
  error = null 
}) => {
  const theme = useTheme();

  if (messages.length === 0 && !isLoading && !error) {
    return (
      <Box 
        flexGrow={1} 
        justifyContent="center" 
        alignItems="center"
        flexDirection="column"
      >
        <Text color={theme.colors.muted}>
          Welcome to Arien! How can I help you today?
        </Text>
        <Text color={theme.colors.muted} dimColor>
          Type a message or use /help for commands
        </Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" flexGrow={1}>
      {messages.map((message, index) => (
        <MessageBubble
          key={`${message.role}-${index}`}
          message={message}
          isLast={index === messages.length - 1}
        />
      ))}
      
      {isLoading && (
        <Box marginY={1}>
          <LoadingSpinner text="AI is thinking..." />
        </Box>
      )}
      
      {error && (
        <Box marginY={1}>
          <ErrorDisplay error={error} />
        </Box>
      )}
    </Box>
  );
};
