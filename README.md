# Arien CLI

A powerful and modern CLI Terminal system powered by AI with support for multiple providers including DeepSeek, OpenAI, Anthropic, and Google.

## Features

- 🤖 **Multi-Provider AI Support**: DeepSeek, OpenAI, Anthropic, and Google
- 🛡️ **Security First**: Sandbox environment, command validation, and user approval system
- 🎨 **Modern UI**: Clean React/Ink-based terminal interface with multiple themes
- 🔧 **Comprehensive Tools**: File operations, shell commands, web tools, and MCP integration
- ⚙️ **Flexible Configuration**: User settings, project settings, and environment variables
- 🔄 **Error Recovery**: Intelligent retry policies and recovery mechanisms
- 📝 **Memory Management**: Context-aware conversations with intelligent memory
- 🎯 **Interactive Experience**: Real-time streaming responses and tool execution

## Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd arien-cli

# Install dependencies
npm run install:all

# Build the project
npm run build

# Start the CLI
npm start
```

### Configuration

On first run, you'll be prompted to configure your AI provider:

```bash
# Configure authentication
arien auth

# Set your preferred model
arien config set model deepseek-chat

# View current configuration
arien config show
```

## Architecture

The system consists of two main packages:

- **`cli/`** - User-facing terminal interface using React/Ink
- **`core/`** - Business logic, AI integration, and tool implementations

### Key Components

- **CLI Interface**: User interface, input processing, theme management
- **Core Engine**: AI client, chat manager, tool registry, API communication
- **Tool System**: File system, shell, web, memory, and MCP tools
- **Configuration**: User settings, project settings, environment variables
- **Security**: Sandbox environment, command validation, user approval

## Usage

### Basic Commands

```bash
# Ask a question
arien "How do I create a React component?"

# Execute with file context
arien "Refactor this function" @src/utils/helper.ts

# Shell integration
arien "What files were modified recently?" && git status

# Web search
arien "Search for React best practices" --web

# Memory management
arien "Remember that I prefer TypeScript"
arien "What do you remember about my preferences?"
```

### Advanced Features

```bash
# Auto-approve mode (use with caution)
arien --auto-approve "Create a new component"

# Specific provider
arien --provider openai "Generate documentation"

# Custom model
arien --model gpt-4 "Complex reasoning task"

# Non-interactive mode
echo "Create README" | arien --non-interactive
```

## Configuration

### Provider Setup

Configure your AI providers in `~/.arien/config.json`:

```json
{
  "providers": {
    "deepseek": {
      "apiKey": "your-deepseek-api-key",
      "baseUrl": "https://api.deepseek.com"
    },
    "openai": {
      "apiKey": "your-openai-api-key"
    },
    "anthropic": {
      "apiKey": "your-anthropic-api-key"
    },
    "google": {
      "apiKey": "your-google-api-key"
    }
  },
  "defaultProvider": "deepseek",
  "defaultModel": "deepseek-chat"
}
```

### Security Settings

```json
{
  "security": {
    "approvalLevel": "default",
    "sandbox": {
      "enabled": true,
      "restrictive": true
    },
    "allowedCommands": ["git", "npm", "node"]
  }
}
```

## Development

### Project Structure

```
arien-cli/
├── cli/                 # Terminal interface
│   ├── src/
│   │   ├── ui/         # React components
│   │   ├── hooks/      # React hooks
│   │   ├── themes/     # Visual themes
│   │   └── utils/      # CLI utilities
├── core/               # Business logic
│   ├── src/
│   │   ├── core/       # AI integration
│   │   ├── tools/      # Tool implementations
│   │   ├── config/     # Configuration
│   │   └── utils/      # Core utilities
└── docs/               # Documentation
```

### Building

```bash
# Development mode
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
