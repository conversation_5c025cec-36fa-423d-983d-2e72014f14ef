// Batch file reading tool
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { BaseTool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const ReadManyFilesSchema = z.object({
  paths: z.array(z.string()).describe('Array of file paths to read'),
  encoding: z.enum(['utf8', 'ascii', 'base64', 'hex', 'binary']).optional().default('utf8').describe('File encoding'),
  maxFileSize: z.number().optional().default(10 * 1024 * 1024).describe('Maximum file size in bytes (default: 10MB)'),
  includeMetadata: z.boolean().optional().default(true).describe('Include file metadata in results'),
  continueOnError: z.boolean().optional().default(true).describe('Continue reading other files if one fails'),
  maxTotalSize: z.number().optional().default(50 * 1024 * 1024).describe('Maximum total size of all files (default: 50MB)'),
  pattern: z.string().optional().describe('Glob pattern to filter files (if paths contains directories)'),
  recursive: z.boolean().optional().default(false).describe('Recursively read files in directories')
});

export interface FileContent {
  path: string;
  content: string;
  size: number;
  encoding: string;
  metadata?: {
    modified: Date;
    created: Date;
    permissions: string;
    type: 'file' | 'directory' | 'symlink';
  };
  error?: string;
}

export interface ReadManyFilesResult {
  files: FileContent[];
  totalFiles: number;
  successfulReads: number;
  failedReads: number;
  totalSize: number;
  totalSizeHuman: string;
  errors: string[];
}

export class ReadManyFilesTool extends BaseTool {
  constructor() {
    super({
      name: 'read-many-files',
      description: 'Read multiple files at once with batch processing',
      parameters: ReadManyFilesSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      
      logger.debug('Reading multiple files', {
        pathCount: validatedParams.paths.length,
        options: this.sanitizeParams(validatedParams)
      });

      // Expand paths and collect all files to read
      const filePaths = await this.expandPaths(validatedParams.paths, validatedParams, context);
      
      // Validate total file count
      if (filePaths.length > 1000) {
        throw new ToolExecutionError(
          this.definition.name,
          `Too many files to read: ${filePaths.length}. Maximum is 1000 files.`
        );
      }

      const result = await this.readFiles(filePaths, validatedParams, context);

      logger.info('Batch file reading completed', {
        totalFiles: result.totalFiles,
        successfulReads: result.successfulReads,
        failedReads: result.failedReads,
        totalSize: result.totalSizeHuman
      });

      const summary = this.createSummary(result);
      return this.createSuccessResult(summary, result);

    } catch (error) {
      logger.error('Batch file reading failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async expandPaths(
    inputPaths: string[],
    params: any,
    context: ToolContext
  ): Promise<string[]> {
    const expandedPaths: string[] = [];
    
    for (const inputPath of inputPaths) {
      const absolutePath = path.resolve(context.workingDirectory, inputPath);
      
      // Security check - ensure path is within working directory
      if (!absolutePath.startsWith(context.workingDirectory)) {
        throw new ToolExecutionError(
          this.definition.name,
          `Path is outside the allowed working directory: ${inputPath}`
        );
      }

      try {
        const stat = await fs.stat(absolutePath);
        
        if (stat.isFile()) {
          expandedPaths.push(absolutePath);
        } else if (stat.isDirectory()) {
          const dirFiles = await this.getFilesFromDirectory(
            absolutePath,
            params.pattern,
            params.recursive
          );
          expandedPaths.push(...dirFiles);
        }
      } catch (error) {
        if (params.continueOnError) {
          logger.warn('Failed to access path', {
            path: inputPath,
            error: error instanceof Error ? error.message : String(error)
          });
        } else {
          throw new ToolExecutionError(
            this.definition.name,
            `Failed to access path ${inputPath}: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }
    }

    return expandedPaths;
  }

  private async getFilesFromDirectory(
    dirPath: string,
    pattern?: string,
    recursive: boolean = false
  ): Promise<string[]> {
    const files: string[] = [];
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isFile()) {
          // Apply pattern filter if specified
          if (!pattern || this.matchesPattern(entry.name, pattern)) {
            files.push(fullPath);
          }
        } else if (entry.isDirectory() && recursive) {
          const subFiles = await this.getFilesFromDirectory(fullPath, pattern, recursive);
          files.push(...subFiles);
        }
      }
    } catch (error) {
      logger.debug('Failed to read directory', {
        directory: dirPath,
        error: error instanceof Error ? error.message : String(error)
      });
    }

    return files;
  }

  private async readFiles(
    filePaths: string[],
    params: any,
    context: ToolContext
  ): Promise<ReadManyFilesResult> {
    const files: FileContent[] = [];
    const errors: string[] = [];
    let totalSize = 0;
    let successfulReads = 0;
    let failedReads = 0;

    for (const filePath of filePaths) {
      try {
        // Check individual file size
        const stat = await fs.stat(filePath);
        
        if (stat.size > params.maxFileSize) {
          const error = `File too large: ${filePath} (${this.formatSize(stat.size)} > ${this.formatSize(params.maxFileSize)})`;
          errors.push(error);
          failedReads++;
          
          if (params.continueOnError) {
            files.push({
              path: filePath,
              content: '',
              size: stat.size,
              encoding: params.encoding,
              error
            });
            continue;
          } else {
            throw new ToolExecutionError(this.definition.name, error);
          }
        }

        // Check total size limit
        if (totalSize + stat.size > params.maxTotalSize) {
          const error = `Total size limit exceeded. Cannot read ${filePath}`;
          errors.push(error);
          failedReads++;
          
          if (params.continueOnError) {
            files.push({
              path: filePath,
              content: '',
              size: stat.size,
              encoding: params.encoding,
              error
            });
            continue;
          } else {
            throw new ToolExecutionError(this.definition.name, error);
          }
        }

        // Read file content
        const content = await fs.readFile(filePath, params.encoding as BufferEncoding);
        
        const fileContent: FileContent = {
          path: filePath,
          content,
          size: stat.size,
          encoding: params.encoding
        };

        // Add metadata if requested
        if (params.includeMetadata) {
          fileContent.metadata = {
            modified: stat.mtime,
            created: stat.birthtime,
            permissions: this.formatPermissions(stat.mode),
            type: stat.isDirectory() ? 'directory' : stat.isSymbolicLink() ? 'symlink' : 'file'
          };
        }

        files.push(fileContent);
        totalSize += stat.size;
        successfulReads++;

      } catch (error) {
        const errorMessage = `Failed to read ${filePath}: ${error instanceof Error ? error.message : String(error)}`;
        errors.push(errorMessage);
        failedReads++;

        if (params.continueOnError) {
          files.push({
            path: filePath,
            content: '',
            size: 0,
            encoding: params.encoding,
            error: errorMessage
          });
        } else {
          throw new ToolExecutionError(this.definition.name, errorMessage);
        }
      }
    }

    return {
      files,
      totalFiles: filePaths.length,
      successfulReads,
      failedReads,
      totalSize,
      totalSizeHuman: this.formatSize(totalSize),
      errors
    };
  }

  private matchesPattern(fileName: string, pattern: string): boolean {
    // Simple glob pattern matching
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(fileName);
  }

  private formatPermissions(mode: number): string {
    const permissions = [];
    
    // Owner permissions
    permissions.push((mode & 0o400) ? 'r' : '-');
    permissions.push((mode & 0o200) ? 'w' : '-');
    permissions.push((mode & 0o100) ? 'x' : '-');
    
    // Group permissions
    permissions.push((mode & 0o040) ? 'r' : '-');
    permissions.push((mode & 0o020) ? 'w' : '-');
    permissions.push((mode & 0o010) ? 'x' : '-');
    
    // Other permissions
    permissions.push((mode & 0o004) ? 'r' : '-');
    permissions.push((mode & 0o002) ? 'w' : '-');
    permissions.push((mode & 0o001) ? 'x' : '-');
    
    return permissions.join('');
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(unitIndex === 0 ? 0 : 1)}${units[unitIndex]}`;
  }

  private createSummary(result: ReadManyFilesResult): string {
    const lines = [
      `Successfully read ${result.successfulReads} of ${result.totalFiles} files`,
      `Total size: ${result.totalSizeHuman}`
    ];

    if (result.failedReads > 0) {
      lines.push(`Failed to read ${result.failedReads} files`);
    }

    if (result.errors.length > 0) {
      lines.push('');
      lines.push('Errors:');
      result.errors.forEach(error => {
        lines.push(`  - ${error}`);
      });
    }

    return lines.join('\n');
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Limit paths array for logging
    if (sanitized.paths && sanitized.paths.length > 10) {
      sanitized.paths = [
        ...sanitized.paths.slice(0, 5),
        `... and ${sanitized.paths.length - 5} more`
      ];
    }
    
    return sanitized;
  }
}
