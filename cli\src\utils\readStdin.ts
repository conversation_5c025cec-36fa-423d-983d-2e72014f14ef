// Standard input reading functionality
import { stdin } from 'process';

export async function readStdin(): Promise<string | null> {
  // Check if stdin is a TTY (interactive terminal)
  if (stdin.isTTY) {
    return null;
  }

  return new Promise((resolve) => {
    let data = '';
    
    stdin.setEncoding('utf8');
    
    stdin.on('data', (chunk) => {
      data += chunk;
    });
    
    stdin.on('end', () => {
      resolve(data.trim() || null);
    });
    
    // Handle case where stdin is empty
    setTimeout(() => {
      if (data === '') {
        resolve(null);
      }
    }, 100);
  });
}
