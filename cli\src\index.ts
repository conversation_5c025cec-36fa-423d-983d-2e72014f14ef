#!/usr/bin/env node

// Main CLI entry point
import { Command } from 'commander';
import { render } from 'ink';
import React from 'react';
import { App } from './ui/App.js';
import { getConfig, logger } from '@arien/core';
import { readStdin } from './utils/readStdin.js';
import { nonInteractiveCli } from './nonInteractiveCli.js';
import { version } from './utils/version.js';

const program = new Command();

program
  .name('arien')
  .description('A powerful and modern CLI Terminal system powered by AI')
  .version(version);

program
  .argument('[message]', 'Message to send to AI')
  .option('-p, --provider <provider>', 'AI provider to use (deepseek, openai, anthropic, google)')
  .option('-m, --model <model>', 'AI model to use')
  .option('-t, --temperature <temperature>', 'Temperature for AI responses', parseFloat)
  .option('--max-tokens <tokens>', 'Maximum tokens for AI responses', parseInt)
  .option('--non-interactive', 'Run in non-interactive mode')
  .option('--auto-approve', 'Auto-approve all tool executions (use with caution)')
  .option('--stream', 'Stream responses in real-time')
  .option('--web', 'Enable web search capabilities')
  .option('--debug', 'Enable debug logging')
  .option('--config-dir <dir>', 'Custom configuration directory')
  .action(async (message, options) => {
    try {
      // Initialize configuration
      if (options.configDir) {
        const { initConfig } = await import('@arien/core');
        initConfig(options.configDir);
      }

      // Set debug logging if requested
      if (options.debug) {
        logger.setLevel('debug');
        process.env.DEBUG = 'true';
      }

      // Check if we have input from stdin
      const stdinInput = await readStdin();
      const inputMessage = message || stdinInput;

      // Validate configuration
      const config = getConfig();
      const validation = config.validateConfig();
      
      if (!validation.valid) {
        console.error('❌ Configuration error:');
        validation.errors.forEach(error => console.error(`  ${error}`));
        console.error('\nRun "arien auth" to configure authentication.');
        process.exit(1);
      }

      // Run in non-interactive mode if requested or if we have input
      if (options.nonInteractive || inputMessage) {
        if (!inputMessage) {
          console.error('❌ No input provided for non-interactive mode');
          process.exit(1);
        }
        
        await nonInteractiveCli(inputMessage, options);
        return;
      }

      // Run interactive mode
      const { waitUntilExit } = render(
        React.createElement(App, {
          initialMessage: inputMessage,
          options
        })
      );

      await waitUntilExit();
    } catch (error) {
      logger.error('CLI startup failed', { error });
      console.error('❌ Failed to start Arien CLI:', error);
      process.exit(1);
    }
  });

// Auth command
program
  .command('auth')
  .description('Configure AI provider authentication')
  .option('--provider <provider>', 'Specific provider to configure')
  .option('--reset', 'Reset all authentication settings')
  .action(async (options) => {
    try {
      const { AuthDialog } = await import('./ui/components/AuthDialog.js');
      
      if (options.reset) {
        const config = getConfig();
        config.reset();
        console.log('✅ Authentication settings reset');
        return;
      }

      const { waitUntilExit } = render(
        React.createElement(AuthDialog, {
          onComplete: () => {
            console.log('✅ Authentication configured successfully');
            process.exit(0);
          },
          onCancel: () => {
            console.log('❌ Authentication cancelled');
            process.exit(1);
          }
        })
      );

      await waitUntilExit();
    } catch (error) {
      logger.error('Auth command failed', { error });
      console.error('❌ Authentication failed:', error);
      process.exit(1);
    }
  });

// Config command
program
  .command('config')
  .description('Manage configuration settings')
  .argument('[action]', 'Action to perform (show, set, get, reset)')
  .argument('[key]', 'Configuration key')
  .argument('[value]', 'Configuration value')
  .action(async (action, key, value) => {
    try {
      const config = getConfig();
      
      switch (action) {
        case 'show':
          console.log(JSON.stringify(config.getConfig(), null, 2));
          break;
          
        case 'get':
          if (!key) {
            console.error('❌ Key is required for get action');
            process.exit(1);
          }
          const currentConfig = config.getConfig();
          const keyValue = key.split('.').reduce((obj: any, k) => obj?.[k], currentConfig);
          console.log(keyValue);
          break;
          
        case 'set':
          if (!key || value === undefined) {
            console.error('❌ Key and value are required for set action');
            process.exit(1);
          }
          // Simple key-value setting (could be enhanced for nested keys)
          const updates: any = {};
          updates[key] = value;
          config.updateConfig(updates);
          console.log(`✅ Set ${key} = ${value}`);
          break;
          
        case 'reset':
          config.reset();
          console.log('✅ Configuration reset to defaults');
          break;
          
        default:
          console.log(JSON.stringify(config.getConfig(), null, 2));
      }
    } catch (error) {
      logger.error('Config command failed', { error });
      console.error('❌ Configuration command failed:', error);
      process.exit(1);
    }
  });

// Models command
program
  .command('models')
  .description('List available AI models')
  .option('--provider <provider>', 'Filter by provider')
  .action(async (options) => {
    try {
      const { MODELS, getModelsByProvider, PROVIDERS } = await import('@arien/core');
      
      if (options.provider) {
        const models = getModelsByProvider(options.provider);
        console.log(`\n${options.provider.toUpperCase()} Models:`);
        models.forEach(model => {
          console.log(`  ${model.id} - ${model.name}`);
          console.log(`    Max Tokens: ${model.maxTokens}`);
          console.log(`    Tools: ${model.supportsTools ? '✅' : '❌'}`);
          console.log(`    Streaming: ${model.supportsStreaming ? '✅' : '❌'}`);
          console.log(`    Cost: $${model.costPer1kTokens.input}/$${model.costPer1kTokens.output} per 1K tokens\n`);
        });
      } else {
        Object.values(PROVIDERS).forEach(provider => {
          const models = getModelsByProvider(provider);
          console.log(`\n${provider.toUpperCase()} Models:`);
          models.forEach(model => {
            console.log(`  ${model.id} - ${model.name}`);
          });
        });
      }
    } catch (error) {
      logger.error('Models command failed', { error });
      console.error('❌ Failed to list models:', error);
      process.exit(1);
    }
  });

// Status command
program
  .command('status')
  .description('Show current status and configuration')
  .action(async () => {
    try {
      const config = getConfig();
      const currentConfig = config.getConfig();
      const validation = config.validateConfig();
      
      console.log('🤖 Arien CLI Status\n');
      
      console.log('Configuration:');
      console.log(`  Default Provider: ${currentConfig.defaultProvider}`);
      console.log(`  Default Model: ${config.getDefaultModel()}`);
      console.log(`  Theme: ${currentConfig.theme}`);
      console.log(`  Security Level: ${currentConfig.security.approvalLevel}`);
      console.log(`  Sandbox: ${currentConfig.security.sandbox.enabled ? '✅' : '❌'}`);
      
      console.log('\nProviders:');
      Object.values(PROVIDERS).forEach(provider => {
        const isConfigured = config.isProviderConfigured(provider);
        console.log(`  ${provider}: ${isConfigured ? '✅' : '❌'}`);
      });
      
      console.log('\nValidation:');
      if (validation.valid) {
        console.log('  ✅ Configuration is valid');
      } else {
        console.log('  ❌ Configuration has errors:');
        validation.errors.forEach(error => console.log(`    ${error}`));
      }
    } catch (error) {
      logger.error('Status command failed', { error });
      console.error('❌ Failed to get status:', error);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();
