// Security approval system for tool execution
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ApprovalRequest {
  id: string;
  operation: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  details: {
    command?: string;
    filePaths?: string[];
    parameters?: Record<string, any>;
    estimatedImpact?: string;
    reversible?: boolean;
  };
  context: {
    workingDirectory: string;
    userId: string;
    sessionId: string;
    timestamp: Date;
  };
  autoApprovalEligible: boolean;
}

export interface ApprovalResponse {
  approved: boolean;
  reason?: string;
  conditions?: string[];
  rememberChoice?: boolean;
  alwaysAllow?: boolean;
  alwaysDeny?: boolean;
}

export interface ApprovalRule {
  id: string;
  pattern: string;
  action: 'allow' | 'deny' | 'prompt';
  riskLevel?: 'low' | 'medium' | 'high' | 'critical';
  conditions?: string[];
  description: string;
  createdAt: Date;
  lastUsed?: Date;
  useCount: number;
}

export type ApprovalLevel = 'default' | 'auto-edit' | 'yolo';

export interface ApprovalSystemConfig {
  level: ApprovalLevel;
  rules: ApprovalRule[];
  autoApprovalTimeout: number;
  requireConfirmationFor: string[];
  alwaysDenyPatterns: string[];
  trustedDirectories: string[];
}

export class ApprovalSystem {
  private config: ApprovalSystemConfig;
  private pendingRequests = new Map<string, ApprovalRequest>();
  private approvalHistory: Array<{ request: ApprovalRequest; response: ApprovalResponse; timestamp: Date }> = [];

  constructor(config: Partial<ApprovalSystemConfig> = {}) {
    this.config = {
      level: 'default',
      rules: [],
      autoApprovalTimeout: 30000, // 30 seconds
      requireConfirmationFor: ['rm', 'del', 'format', 'sudo'],
      alwaysDenyPatterns: ['rm -rf /', 'format c:', 'sudo rm -rf'],
      trustedDirectories: [],
      ...config
    };
  }

  public async requestApproval(request: ApprovalRequest): Promise<ApprovalResponse> {
    logger.debug('Approval requested', { 
      operation: request.operation, 
      riskLevel: request.riskLevel,
      autoEligible: request.autoApprovalEligible 
    });

    // Check for always-deny patterns first
    if (this.isAlwaysDenied(request)) {
      const response: ApprovalResponse = {
        approved: false,
        reason: 'Operation matches always-deny pattern'
      };
      this.recordApproval(request, response);
      return response;
    }

    // Check existing rules
    const ruleResponse = this.checkRules(request);
    if (ruleResponse) {
      this.recordApproval(request, ruleResponse);
      return ruleResponse;
    }

    // Check auto-approval eligibility
    if (this.canAutoApprove(request)) {
      const response: ApprovalResponse = {
        approved: true,
        reason: `Auto-approved (level: ${this.config.level})`
      };
      this.recordApproval(request, response);
      return response;
    }

    // Require manual approval
    return this.requestManualApproval(request);
  }

  private isAlwaysDenied(request: ApprovalRequest): boolean {
    const command = request.details.command || request.operation;
    
    return this.config.alwaysDenyPatterns.some(pattern => {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
      return regex.test(command);
    });
  }

  private checkRules(request: ApprovalRequest): ApprovalResponse | null {
    for (const rule of this.config.rules) {
      if (this.matchesRule(request, rule)) {
        // Update rule usage
        rule.lastUsed = new Date();
        rule.useCount++;

        switch (rule.action) {
          case 'allow':
            return {
              approved: true,
              reason: `Approved by rule: ${rule.description}`
            };
          case 'deny':
            return {
              approved: false,
              reason: `Denied by rule: ${rule.description}`
            };
          case 'prompt':
            // Continue to manual approval
            break;
        }
      }
    }

    return null;
  }

  private matchesRule(request: ApprovalRequest, rule: ApprovalRule): boolean {
    const command = request.details.command || request.operation;
    const regex = new RegExp(rule.pattern.replace(/\*/g, '.*'), 'i');
    
    if (!regex.test(command)) {
      return false;
    }

    // Check risk level if specified
    if (rule.riskLevel && request.riskLevel !== rule.riskLevel) {
      return false;
    }

    return true;
  }

  private canAutoApprove(request: ApprovalRequest): boolean {
    if (!request.autoApprovalEligible) {
      return false;
    }

    switch (this.config.level) {
      case 'yolo':
        return true;
      case 'auto-edit':
        return request.riskLevel === 'low' || 
               (request.riskLevel === 'medium' && this.isFileOperation(request));
      case 'default':
      default:
        return false;
    }
  }

  private isFileOperation(request: ApprovalRequest): boolean {
    const fileOperations = ['read', 'write', 'edit', 'create', 'copy', 'move'];
    return fileOperations.some(op => request.operation.toLowerCase().includes(op));
  }

  private isTrustedDirectory(path: string): boolean {
    return this.config.trustedDirectories.some(trusted => 
      path.startsWith(trusted)
    );
  }

  private async requestManualApproval(request: ApprovalRequest): Promise<ApprovalResponse> {
    // Store pending request
    this.pendingRequests.set(request.id, request);

    try {
      // In a real implementation, this would trigger UI approval dialog
      // For now, we'll simulate based on risk level
      const response = await this.simulateManualApproval(request);
      this.recordApproval(request, response);
      return response;
    } finally {
      this.pendingRequests.delete(request.id);
    }
  }

  private async simulateManualApproval(request: ApprovalRequest): Promise<ApprovalResponse> {
    // This is a simulation - in real implementation, this would show UI
    logger.info('Manual approval required', {
      operation: request.operation,
      riskLevel: request.riskLevel,
      description: request.description
    });

    // Simulate approval based on risk level
    const approved = request.riskLevel !== 'critical';
    
    return {
      approved,
      reason: approved ? 'Manually approved' : 'Manually denied due to high risk'
    };
  }

  public addRule(rule: Omit<ApprovalRule, 'id' | 'createdAt' | 'useCount'>): void {
    const newRule: ApprovalRule = {
      ...rule,
      id: `rule-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      useCount: 0
    };

    this.config.rules.push(newRule);
    logger.debug('Approval rule added', { rule: newRule });
  }

  public removeRule(ruleId: string): boolean {
    const index = this.config.rules.findIndex(rule => rule.id === ruleId);
    if (index >= 0) {
      this.config.rules.splice(index, 1);
      logger.debug('Approval rule removed', { ruleId });
      return true;
    }
    return false;
  }

  public setApprovalLevel(level: ApprovalLevel): void {
    this.config.level = level;
    logger.info('Approval level changed', { level });
  }

  public getApprovalLevel(): ApprovalLevel {
    return this.config.level;
  }

  public getRules(): ApprovalRule[] {
    return [...this.config.rules];
  }

  public getApprovalHistory(limit?: number): Array<{ request: ApprovalRequest; response: ApprovalResponse; timestamp: Date }> {
    const history = [...this.approvalHistory].reverse();
    return limit ? history.slice(0, limit) : history;
  }

  public clearHistory(): void {
    this.approvalHistory = [];
    logger.debug('Approval history cleared');
  }

  private recordApproval(request: ApprovalRequest, response: ApprovalResponse): void {
    this.approvalHistory.push({
      request,
      response,
      timestamp: new Date()
    });

    // Keep only last 1000 entries
    if (this.approvalHistory.length > 1000) {
      this.approvalHistory = this.approvalHistory.slice(-1000);
    }

    logger.debug('Approval recorded', {
      operation: request.operation,
      approved: response.approved,
      reason: response.reason
    });
  }

  public getStats(): {
    totalRequests: number;
    approvedRequests: number;
    deniedRequests: number;
    autoApprovedRequests: number;
    ruleBasedApprovals: number;
    currentLevel: ApprovalLevel;
    activeRules: number;
  } {
    const total = this.approvalHistory.length;
    const approved = this.approvalHistory.filter(h => h.response.approved).length;
    const autoApproved = this.approvalHistory.filter(h => 
      h.response.approved && h.response.reason?.includes('Auto-approved')
    ).length;
    const ruleBasedApprovals = this.approvalHistory.filter(h => 
      h.response.reason?.includes('rule')
    ).length;

    return {
      totalRequests: total,
      approvedRequests: approved,
      deniedRequests: total - approved,
      autoApprovedRequests: autoApproved,
      ruleBasedApprovals,
      currentLevel: this.config.level,
      activeRules: this.config.rules.length
    };
  }
}

// Global approval system instance
let globalApprovalSystem: ApprovalSystem | null = null;

export function getApprovalSystem(): ApprovalSystem {
  if (!globalApprovalSystem) {
    globalApprovalSystem = new ApprovalSystem();
  }
  return globalApprovalSystem;
}

export function setApprovalSystem(system: ApprovalSystem): void {
  globalApprovalSystem = system;
}

// Utility functions
export function createApprovalRequest(
  operation: string,
  description: string,
  riskLevel: ApprovalRequest['riskLevel'],
  details: ApprovalRequest['details'] = {},
  context: Partial<ApprovalRequest['context']> = {}
): ApprovalRequest {
  return {
    id: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    operation,
    description,
    riskLevel,
    details,
    context: {
      workingDirectory: process.cwd(),
      userId: 'default',
      sessionId: 'default',
      timestamp: new Date(),
      ...context
    },
    autoApprovalEligible: riskLevel !== 'critical'
  };
}
