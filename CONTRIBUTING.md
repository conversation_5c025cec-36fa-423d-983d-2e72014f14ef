# Contributing to Arien CLI

Thank you for your interest in contributing to Arien CLI! This document provides guidelines and information for contributors.

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/arien-cli.git
   cd arien-cli
   ```

2. **Install Dependencies**
   ```bash
   npm run install:all
   ```

3. **Build the Project**
   ```bash
   npm run build
   ```

4. **Run Tests**
   ```bash
   npm test
   ```

5. **Start Development**
   ```bash
   npm run dev
   ```

## Project Structure

```
arien-cli/
├── cli/                 # Terminal interface (React/Ink)
│   ├── src/
│   │   ├── ui/         # UI components
│   │   ├── hooks/      # React hooks
│   │   ├── themes/     # Visual themes
│   │   └── utils/      # CLI utilities
├── core/               # Business logic
│   ├── src/
│   │   ├── core/       # AI integration
│   │   ├── tools/      # Tool implementations
│   │   ├── config/     # Configuration
│   │   └── utils/      # Core utilities
└── examples/           # Usage examples
```

## Development Guidelines

### Code Style

- Use TypeScript for all new code
- Follow existing code formatting (Prettier)
- Use meaningful variable and function names
- Add JSDoc comments for public APIs
- Keep functions small and focused

### Commit Messages

Use conventional commit format:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

Examples:
```
feat(cli): add theme selection dialog
fix(core): handle API timeout errors
docs(readme): update installation instructions
```

### Testing

- Write tests for new features
- Ensure existing tests pass
- Test with multiple AI providers
- Test both interactive and non-interactive modes

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following guidelines
   - Add tests
   - Update documentation

3. **Test Changes**
   ```bash
   npm test
   npm run lint
   npm run build
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add your feature"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## Contributing Areas

### 1. Core Features

- **AI Provider Integration**: Add support for new AI providers
- **Tool System**: Create new tools for file operations, web access, etc.
- **Security**: Improve sandbox and approval systems
- **Performance**: Optimize response times and memory usage

### 2. CLI Interface

- **UI Components**: Create new React/Ink components
- **Themes**: Design new visual themes
- **User Experience**: Improve interaction patterns
- **Accessibility**: Enhance keyboard navigation and screen reader support

### 3. Documentation

- **API Documentation**: Document public APIs
- **Usage Examples**: Create practical examples
- **Tutorials**: Write step-by-step guides
- **Troubleshooting**: Document common issues and solutions

### 4. Testing

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows
- **Performance Tests**: Benchmark critical operations

## Specific Contribution Ideas

### Easy (Good First Issues)

- Add new themes to the theme system
- Improve error messages
- Add more usage examples
- Fix typos in documentation
- Add unit tests for utility functions

### Medium

- Implement new tools (e.g., database query, image processing)
- Add support for new AI models
- Improve the configuration system
- Add keyboard shortcuts
- Implement command history

### Hard

- Add plugin system for custom tools
- Implement distributed tool execution
- Add real-time collaboration features
- Create web interface
- Add voice input/output

## Code Review Guidelines

### For Contributors

- Keep PRs focused and small
- Provide clear descriptions
- Include tests and documentation
- Respond to feedback promptly
- Be open to suggestions

### For Reviewers

- Be constructive and helpful
- Focus on code quality and maintainability
- Check for security issues
- Verify tests pass
- Ensure documentation is updated

## Release Process

1. **Version Bump**
   ```bash
   npm version patch|minor|major
   ```

2. **Update Changelog**
   - Document new features
   - List bug fixes
   - Note breaking changes

3. **Create Release**
   ```bash
   git tag v1.x.x
   git push origin v1.x.x
   ```

4. **Publish**
   ```bash
   npm publish
   ```

## Getting Help

- **Discord**: Join our community server
- **GitHub Issues**: Report bugs or request features
- **GitHub Discussions**: Ask questions or share ideas
- **Email**: Contact maintainers directly

## Code of Conduct

- Be respectful and inclusive
- Welcome newcomers
- Focus on constructive feedback
- Help others learn and grow
- Follow GitHub's community guidelines

## License

By contributing to Arien CLI, you agree that your contributions will be licensed under the MIT License.

## Recognition

Contributors will be:
- Listed in the README
- Mentioned in release notes
- Invited to the contributors team
- Given credit in documentation

Thank you for contributing to Arien CLI! 🚀
