// Command validation and security checking
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface CommandValidationResult {
  isValid: boolean;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  issues: ValidationIssue[];
  sanitizedCommand?: string;
  recommendations?: string[];
}

export interface ValidationIssue {
  type: 'security' | 'syntax' | 'permission' | 'destructive' | 'suspicious';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  position?: { start: number; end: number };
  suggestion?: string;
}

export interface CommandContext {
  workingDirectory: string;
  userId: string;
  environment: Record<string, string>;
  allowedCommands?: string[];
  blockedCommands?: string[];
  trustedDirectories?: string[];
}

export class CommandValidator {
  private dangerousCommands = new Set([
    'rm', 'del', 'format', 'fdisk', 'mkfs', 'dd', 'shred',
    'sudo', 'su', 'chmod', 'chown', 'passwd', 'useradd', 'userdel',
    'systemctl', 'service', 'init', 'shutdown', 'reboot', 'halt',
    'iptables', 'ufw', 'firewall-cmd', 'netsh',
    'crontab', 'at', 'batch'
  ]);

  private suspiciousPatterns = [
    /\$\(.*\)/g,           // Command substitution
    /`.*`/g,               // Backtick command substitution
    /\|\s*sh/g,            // Pipe to shell
    /\|\s*bash/g,          // Pipe to bash
    /\|\s*zsh/g,           // Pipe to zsh
    />\s*\/dev\/null/g,    // Redirect to null (hiding output)
    /2>&1/g,               // Error redirection
    /&\s*$/g,              // Background execution
    /;\s*rm/g,             // Chained with rm
    /&&\s*rm/g,            // Conditional rm
    /\|\|\s*rm/g,          // Or rm
    /curl.*\|\s*sh/g,      // Download and execute
    /wget.*\|\s*sh/g,      // Download and execute
    /eval\s*\(/g,          // Eval function
    /exec\s*\(/g,          // Exec function
    /system\s*\(/g,        // System function
    /\/etc\/passwd/g,      // Password file access
    /\/etc\/shadow/g,      // Shadow file access
    /\/proc\/\d+/g,        // Process access
    /\/dev\/sd[a-z]/g,     // Direct disk access
    /\/dev\/hd[a-z]/g,     // Direct disk access
  ];

  private destructivePatterns = [
    /rm\s+(-[rf]*\s+)*\//g,           // rm with root or recursive
    /rm\s+(-[rf]*\s+)*\*/g,           // rm with wildcards
    /del\s+\/[sq]/g,                  // Windows del with force/quiet
    /format\s+[a-z]:/gi,              // Format drive
    /fdisk\s+\/dev/g,                 // Disk partitioning
    /dd\s+.*of=/g,                    // Disk dump
    /mkfs\s+/g,                       // Make filesystem
    /shred\s+/g,                      // Secure delete
    />\s*\/dev\/sd[a-z]/g,           // Write to disk
    />\s*\/dev\/hd[a-z]/g,           // Write to disk
  ];

  public validateCommand(command: string, context: CommandContext): CommandValidationResult {
    const issues: ValidationIssue[] = [];
    let riskLevel: CommandValidationResult['riskLevel'] = 'low';
    const recommendations: string[] = [];

    try {
      // Basic syntax validation
      this.validateSyntax(command, issues);

      // Security validation
      this.validateSecurity(command, context, issues);

      // Permission validation
      this.validatePermissions(command, context, issues);

      // Destructive operation detection
      this.validateDestructiveOperations(command, issues);

      // Suspicious pattern detection
      this.validateSuspiciousPatterns(command, issues);

      // Calculate overall risk level
      riskLevel = this.calculateRiskLevel(issues);

      // Generate recommendations
      this.generateRecommendations(command, issues, recommendations);

      // Generate sanitized command if possible
      const sanitizedCommand = this.sanitizeCommand(command, issues);

      return {
        isValid: !issues.some(issue => issue.severity === 'critical'),
        riskLevel,
        issues,
        sanitizedCommand,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };
    } catch (error) {
      logger.error('Command validation failed', { command, error });
      
      return {
        isValid: false,
        riskLevel: 'critical',
        issues: [{
          type: 'security',
          severity: 'critical',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`
        }]
      };
    }
  }

  private validateSyntax(command: string, issues: ValidationIssue[]): void {
    // Check for unmatched quotes
    const singleQuotes = (command.match(/'/g) || []).length;
    const doubleQuotes = (command.match(/"/g) || []).length;
    
    if (singleQuotes % 2 !== 0) {
      issues.push({
        type: 'syntax',
        severity: 'medium',
        message: 'Unmatched single quotes detected',
        suggestion: 'Ensure all quotes are properly closed'
      });
    }

    if (doubleQuotes % 2 !== 0) {
      issues.push({
        type: 'syntax',
        severity: 'medium',
        message: 'Unmatched double quotes detected',
        suggestion: 'Ensure all quotes are properly closed'
      });
    }

    // Check for unmatched parentheses
    const openParens = (command.match(/\(/g) || []).length;
    const closeParens = (command.match(/\)/g) || []).length;
    
    if (openParens !== closeParens) {
      issues.push({
        type: 'syntax',
        severity: 'medium',
        message: 'Unmatched parentheses detected',
        suggestion: 'Ensure all parentheses are properly closed'
      });
    }
  }

  private validateSecurity(command: string, context: CommandContext, issues: ValidationIssue[]): void {
    const commandParts = command.trim().split(/\s+/);
    const baseCommand = commandParts[0];

    // Check against blocked commands
    if (context.blockedCommands?.includes(baseCommand)) {
      issues.push({
        type: 'security',
        severity: 'critical',
        message: `Command '${baseCommand}' is explicitly blocked`,
        suggestion: 'Use an alternative command or request permission'
      });
    }

    // Check against allowed commands (if whitelist is defined)
    if (context.allowedCommands && !context.allowedCommands.includes(baseCommand)) {
      issues.push({
        type: 'security',
        severity: 'high',
        message: `Command '${baseCommand}' is not in the allowed list`,
        suggestion: 'Use only whitelisted commands'
      });
    }

    // Check for dangerous commands
    if (this.dangerousCommands.has(baseCommand)) {
      issues.push({
        type: 'security',
        severity: 'high',
        message: `Potentially dangerous command: ${baseCommand}`,
        suggestion: 'Review the command carefully before execution'
      });
    }

    // Check for privilege escalation
    if (command.includes('sudo') || command.includes('su ')) {
      issues.push({
        type: 'security',
        severity: 'high',
        message: 'Privilege escalation detected',
        suggestion: 'Ensure you have proper authorization for elevated privileges'
      });
    }

    // Check for network operations
    if (/curl|wget|nc|netcat|telnet|ssh|scp|rsync/.test(command)) {
      issues.push({
        type: 'security',
        severity: 'medium',
        message: 'Network operation detected',
        suggestion: 'Verify the destination and ensure secure connections'
      });
    }
  }

  private validatePermissions(command: string, context: CommandContext, issues: ValidationIssue[]): void {
    // Check for operations outside trusted directories
    const pathMatches = command.match(/(?:^|\s)([\/~][^\s]*)/g);
    
    if (pathMatches && context.trustedDirectories) {
      for (const pathMatch of pathMatches) {
        const path = pathMatch.trim();
        const isTrusted = context.trustedDirectories.some(trusted => 
          path.startsWith(trusted)
        );
        
        if (!isTrusted) {
          issues.push({
            type: 'permission',
            severity: 'medium',
            message: `Operation on untrusted path: ${path}`,
            suggestion: 'Ensure you have permission to access this path'
          });
        }
      }
    }

    // Check for system directory access
    const systemPaths = ['/etc', '/usr', '/var', '/sys', '/proc', '/boot'];
    for (const sysPath of systemPaths) {
      if (command.includes(sysPath)) {
        issues.push({
          type: 'permission',
          severity: 'high',
          message: `System directory access detected: ${sysPath}`,
          suggestion: 'System directory operations require careful consideration'
        });
      }
    }
  }

  private validateDestructiveOperations(command: string, issues: ValidationIssue[]): void {
    for (const pattern of this.destructivePatterns) {
      const matches = command.match(pattern);
      if (matches) {
        for (const match of matches) {
          issues.push({
            type: 'destructive',
            severity: 'critical',
            message: `Potentially destructive operation detected: ${match}`,
            suggestion: 'Double-check this operation as it may cause data loss'
          });
        }
      }
    }

    // Check for recursive operations
    if (/rm\s+.*-r/.test(command) || /del\s+.*\/s/.test(command)) {
      issues.push({
        type: 'destructive',
        severity: 'high',
        message: 'Recursive deletion detected',
        suggestion: 'Verify the target directory before proceeding'
      });
    }

    // Check for wildcard operations
    if (/rm\s+.*\*/.test(command) || /del\s+.*\*/.test(command)) {
      issues.push({
        type: 'destructive',
        severity: 'high',
        message: 'Wildcard deletion detected',
        suggestion: 'List files first to verify what will be deleted'
      });
    }
  }

  private validateSuspiciousPatterns(command: string, issues: ValidationIssue[]): void {
    for (const pattern of this.suspiciousPatterns) {
      const matches = command.match(pattern);
      if (matches) {
        for (const match of matches) {
          issues.push({
            type: 'suspicious',
            severity: 'medium',
            message: `Suspicious pattern detected: ${match}`,
            suggestion: 'Review this pattern for potential security implications'
          });
        }
      }
    }

    // Check for encoded content
    if (/base64|hex|url|rot13/.test(command)) {
      issues.push({
        type: 'suspicious',
        severity: 'medium',
        message: 'Encoded content detected',
        suggestion: 'Decode and verify the content before execution'
      });
    }

    // Check for obfuscation
    if (command.length > 200 && /[{}$()\\]/.test(command)) {
      issues.push({
        type: 'suspicious',
        severity: 'medium',
        message: 'Potentially obfuscated command',
        suggestion: 'Break down complex commands for better understanding'
      });
    }
  }

  private calculateRiskLevel(issues: ValidationIssue[]): CommandValidationResult['riskLevel'] {
    if (issues.some(issue => issue.severity === 'critical')) {
      return 'critical';
    }
    
    const highIssues = issues.filter(issue => issue.severity === 'high').length;
    if (highIssues >= 2) {
      return 'critical';
    } else if (highIssues >= 1) {
      return 'high';
    }
    
    const mediumIssues = issues.filter(issue => issue.severity === 'medium').length;
    if (mediumIssues >= 3) {
      return 'high';
    } else if (mediumIssues >= 1) {
      return 'medium';
    }
    
    return 'low';
  }

  private generateRecommendations(
    command: string, 
    issues: ValidationIssue[], 
    recommendations: string[]
  ): void {
    if (issues.some(issue => issue.type === 'destructive')) {
      recommendations.push('Consider backing up important data before proceeding');
      recommendations.push('Test the command in a safe environment first');
    }

    if (issues.some(issue => issue.type === 'security')) {
      recommendations.push('Review security implications carefully');
      recommendations.push('Ensure you have proper authorization');
    }

    if (issues.some(issue => issue.type === 'suspicious')) {
      recommendations.push('Verify the command source and intent');
      recommendations.push('Consider breaking down complex operations');
    }

    if (command.includes('sudo')) {
      recommendations.push('Use sudo only when necessary');
      recommendations.push('Verify the command before using elevated privileges');
    }
  }

  private sanitizeCommand(command: string, issues: ValidationIssue[]): string | undefined {
    let sanitized = command;
    let modified = false;

    // Remove dangerous redirections
    if (/>\s*\/dev\/null/.test(sanitized)) {
      sanitized = sanitized.replace(/>\s*\/dev\/null/g, '');
      modified = true;
    }

    // Remove background execution
    if (/&\s*$/.test(sanitized)) {
      sanitized = sanitized.replace(/&\s*$/, '');
      modified = true;
    }

    return modified ? sanitized.trim() : undefined;
  }

  public addDangerousCommand(command: string): void {
    this.dangerousCommands.add(command);
  }

  public removeDangerousCommand(command: string): void {
    this.dangerousCommands.delete(command);
  }

  public getDangerousCommands(): string[] {
    return Array.from(this.dangerousCommands);
  }
}
