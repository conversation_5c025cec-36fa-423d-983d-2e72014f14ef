// Metrics collection and reporting
import * as os from 'os';
import * as process from 'process';
import { logger } from '../core/logger.js';
import { 
  TelemetryMetric, 
  TelemetryEvent, 
  PerformanceMetrics, 
  UsageStats, 
  SystemInfo,
  TelemetryMetricName,
  TelemetryEventName
} from './types.js';
import { 
  TELEMETRY_METRICS, 
  TELEMETRY_EVENTS, 
  METRIC_UNITS,
  COLLECTION_INTERVALS,
  PERFORMANCE_THRESHOLDS
} from './constants.js';

export class MetricsCollector {
  private metrics = new Map<string, TelemetryMetric[]>();
  private events = new Map<string, TelemetryEvent[]>();
  private collectors = new Map<string, NodeJS.Timeout>();
  private startTime = Date.now();
  private counters = new Map<string, number>();
  private gauges = new Map<string, number>();
  private histograms = new Map<string, number[]>();

  constructor() {
    this.initializeCounters();
  }

  public start(): void {
    logger.info('Starting metrics collection');
    
    // Start system metrics collection
    this.startSystemMetricsCollection();
    
    // Start performance metrics collection
    this.startPerformanceMetricsCollection();
    
    // Start usage stats collection
    this.startUsageStatsCollection();
  }

  public stop(): void {
    logger.info('Stopping metrics collection');
    
    // Clear all collectors
    for (const [name, timer] of this.collectors) {
      clearInterval(timer);
      logger.debug('Stopped metrics collector', { name });
    }
    this.collectors.clear();
  }

  public recordEvent(name: TelemetryEventName, properties?: Record<string, any>): void {
    const event: TelemetryEvent = {
      name,
      timestamp: new Date(),
      properties: properties || {},
      context: this.getContext()
    };

    if (!this.events.has(name)) {
      this.events.set(name, []);
    }
    this.events.get(name)!.push(event);

    logger.debug('Event recorded', { name, properties });
  }

  public recordMetric(name: TelemetryMetricName, value: number, unit?: string, tags?: Record<string, string>): void {
    const metric: TelemetryMetric = {
      name,
      value,
      unit: unit || this.getDefaultUnit(name),
      timestamp: new Date(),
      tags: tags || {}
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(metric);

    logger.debug('Metric recorded', { name, value, unit });
  }

  public incrementCounter(name: string, value: number = 1): void {
    const current = this.counters.get(name) || 0;
    this.counters.set(name, current + value);
  }

  public setGauge(name: string, value: number): void {
    this.gauges.set(name, value);
  }

  public recordHistogram(name: string, value: number): void {
    if (!this.histograms.has(name)) {
      this.histograms.set(name, []);
    }
    this.histograms.get(name)!.push(value);
  }

  public getMetrics(): TelemetryMetric[] {
    const allMetrics: TelemetryMetric[] = [];
    
    // Add collected metrics
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    
    // Add counter metrics
    for (const [name, value] of this.counters) {
      allMetrics.push({
        name: name as TelemetryMetricName,
        value,
        unit: METRIC_UNITS.COUNT,
        timestamp: new Date()
      });
    }
    
    // Add gauge metrics
    for (const [name, value] of this.gauges) {
      allMetrics.push({
        name: name as TelemetryMetricName,
        value,
        unit: this.getDefaultUnit(name as TelemetryMetricName),
        timestamp: new Date()
      });
    }
    
    // Add histogram metrics (percentiles)
    for (const [name, values] of this.histograms) {
      if (values.length > 0) {
        const sorted = values.sort((a, b) => a - b);
        const percentiles = [50, 90, 95, 99];
        
        for (const p of percentiles) {
          const index = Math.floor((p / 100) * sorted.length);
          allMetrics.push({
            name: `${name}_p${p}` as TelemetryMetricName,
            value: sorted[index] || 0,
            unit: this.getDefaultUnit(name as TelemetryMetricName),
            timestamp: new Date(),
            tags: { percentile: p.toString() }
          });
        }
      }
    }
    
    return allMetrics;
  }

  public getEvents(): TelemetryEvent[] {
    const allEvents: TelemetryEvent[] = [];
    for (const events of this.events.values()) {
      allEvents.push(...events);
    }
    return allEvents;
  }

  public clearMetrics(): void {
    this.metrics.clear();
    this.events.clear();
    this.counters.clear();
    this.gauges.clear();
    this.histograms.clear();
    this.initializeCounters();
  }

  public getSystemInfo(): SystemInfo {
    const memUsage = process.memoryUsage();
    
    return {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      arienVersion: process.env.ARIEN_VERSION || '1.0.0',
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      uptime: os.uptime(),
      loadAverage: os.loadavg(),
      diskSpace: this.getDiskSpace()
    };
  }

  public getPerformanceMetrics(): PerformanceMetrics {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    return {
      responseTime: this.getAverageResponseTime(),
      tokenCount: this.counters.get('total_tokens') || 0,
      requestSize: this.getAverageRequestSize(),
      responseSize: this.getAverageResponseSize(),
      errorCount: this.counters.get('errors') || 0,
      retryCount: this.counters.get('retries') || 0,
      cacheHitRate: this.getCacheHitRate(),
      memoryUsage: memUsage.heapUsed,
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
    };
  }

  public getUsageStats(): UsageStats {
    return {
      commandsExecuted: this.counters.get('commands_executed') || 0,
      toolsUsed: this.getToolUsageStats(),
      providersUsed: this.getProviderUsageStats(),
      modelsUsed: this.getModelUsageStats(),
      filesAccessed: this.counters.get('files_accessed') || 0,
      networkRequests: this.counters.get('network_requests') || 0,
      errorsEncountered: this.counters.get('errors') || 0,
      sessionDuration: Date.now() - this.startTime,
      featuresUsed: this.getFeaturesUsed()
    };
  }

  private startSystemMetricsCollection(): void {
    const collectSystemMetrics = () => {
      try {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();
        
        this.recordMetric(TELEMETRY_METRICS.MEMORY_USAGE, memUsage.heapUsed, METRIC_UNITS.BYTES);
        this.recordMetric(TELEMETRY_METRICS.CPU_USAGE, (cpuUsage.user + cpuUsage.system) / 1000000, METRIC_UNITS.SECONDS);
        
        // Check for performance issues
        if (memUsage.heapUsed > PERFORMANCE_THRESHOLDS.MEMORY_USAGE_WARNING) {
          this.recordEvent(TELEMETRY_EVENTS.MEMORY_WARNING, {
            memoryUsage: memUsage.heapUsed,
            threshold: PERFORMANCE_THRESHOLDS.MEMORY_USAGE_WARNING
          });
        }
        
      } catch (error) {
        logger.error('Failed to collect system metrics', { error });
      }
    };

    const timer = setInterval(collectSystemMetrics, COLLECTION_INTERVALS.SYSTEM_METRICS);
    this.collectors.set('system_metrics', timer);
    
    // Collect initial metrics
    collectSystemMetrics();
  }

  private startPerformanceMetricsCollection(): void {
    const collectPerformanceMetrics = () => {
      try {
        const performanceMetrics = this.getPerformanceMetrics();
        
        this.recordMetric(TELEMETRY_METRICS.RESPONSE_TIME, performanceMetrics.responseTime, METRIC_UNITS.MILLISECONDS);
        this.recordMetric(TELEMETRY_METRICS.ERROR_COUNT, performanceMetrics.errorCount, METRIC_UNITS.COUNT);
        this.recordMetric(TELEMETRY_METRICS.RETRY_COUNT, performanceMetrics.retryCount, METRIC_UNITS.COUNT);
        this.recordMetric(TELEMETRY_METRICS.CACHE_HIT_RATE, performanceMetrics.cacheHitRate, METRIC_UNITS.PERCENTAGE);
        
        // Check for performance issues
        if (performanceMetrics.responseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING) {
          this.recordEvent(TELEMETRY_EVENTS.PERFORMANCE_ISSUE, {
            responseTime: performanceMetrics.responseTime,
            threshold: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING,
            type: 'slow_response'
          });
        }
        
      } catch (error) {
        logger.error('Failed to collect performance metrics', { error });
      }
    };

    const timer = setInterval(collectPerformanceMetrics, COLLECTION_INTERVALS.PERFORMANCE_METRICS);
    this.collectors.set('performance_metrics', timer);
  }

  private startUsageStatsCollection(): void {
    const collectUsageStats = () => {
      try {
        const usageStats = this.getUsageStats();
        
        this.recordMetric(TELEMETRY_METRICS.COMMANDS_PER_MINUTE, 
          usageStats.commandsExecuted / (usageStats.sessionDuration / 60000), 
          METRIC_UNITS.RATE);
        
        this.recordMetric(TELEMETRY_METRICS.SESSION_DURATION, 
          usageStats.sessionDuration, 
          METRIC_UNITS.MILLISECONDS);
        
      } catch (error) {
        logger.error('Failed to collect usage stats', { error });
      }
    };

    const timer = setInterval(collectUsageStats, COLLECTION_INTERVALS.USAGE_STATS);
    this.collectors.set('usage_stats', timer);
  }

  private initializeCounters(): void {
    const counterNames = [
      'commands_executed',
      'tools_used',
      'files_accessed',
      'network_requests',
      'errors',
      'retries',
      'cache_hits',
      'cache_misses',
      'total_tokens',
      'approvals_requested',
      'approvals_granted',
      'approvals_denied'
    ];

    for (const name of counterNames) {
      this.counters.set(name, 0);
    }
  }

  private getContext() {
    return {
      version: process.env.ARIEN_VERSION || '1.0.0',
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version
    };
  }

  private getDefaultUnit(metricName: TelemetryMetricName): string {
    const unitMap: Record<string, string> = {
      [TELEMETRY_METRICS.RESPONSE_TIME]: METRIC_UNITS.MILLISECONDS,
      [TELEMETRY_METRICS.TOKEN_COUNT]: METRIC_UNITS.TOKENS,
      [TELEMETRY_METRICS.REQUEST_SIZE]: METRIC_UNITS.BYTES,
      [TELEMETRY_METRICS.RESPONSE_SIZE]: METRIC_UNITS.BYTES,
      [TELEMETRY_METRICS.ERROR_COUNT]: METRIC_UNITS.COUNT,
      [TELEMETRY_METRICS.RETRY_COUNT]: METRIC_UNITS.COUNT,
      [TELEMETRY_METRICS.CACHE_HIT_RATE]: METRIC_UNITS.PERCENTAGE,
      [TELEMETRY_METRICS.MEMORY_USAGE]: METRIC_UNITS.BYTES,
      [TELEMETRY_METRICS.CPU_USAGE]: METRIC_UNITS.PERCENTAGE,
      [TELEMETRY_METRICS.DISK_USAGE]: METRIC_UNITS.PERCENTAGE,
      [TELEMETRY_METRICS.NETWORK_LATENCY]: METRIC_UNITS.MILLISECONDS,
      [TELEMETRY_METRICS.SESSION_DURATION]: METRIC_UNITS.MILLISECONDS
    };

    return unitMap[metricName] || METRIC_UNITS.COUNT;
  }

  private getAverageResponseTime(): number {
    const responseTimes = this.histograms.get('response_time') || [];
    if (responseTimes.length === 0) return 0;
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }

  private getAverageRequestSize(): number {
    const requestSizes = this.histograms.get('request_size') || [];
    if (requestSizes.length === 0) return 0;
    return requestSizes.reduce((sum, size) => sum + size, 0) / requestSizes.length;
  }

  private getAverageResponseSize(): number {
    const responseSizes = this.histograms.get('response_size') || [];
    if (responseSizes.length === 0) return 0;
    return responseSizes.reduce((sum, size) => sum + size, 0) / responseSizes.length;
  }

  private getCacheHitRate(): number {
    const hits = this.counters.get('cache_hits') || 0;
    const misses = this.counters.get('cache_misses') || 0;
    const total = hits + misses;
    return total > 0 ? (hits / total) * 100 : 0;
  }

  private getToolUsageStats(): Record<string, number> {
    // This would be populated by tool usage tracking
    return {};
  }

  private getProviderUsageStats(): Record<string, number> {
    // This would be populated by provider usage tracking
    return {};
  }

  private getModelUsageStats(): Record<string, number> {
    // This would be populated by model usage tracking
    return {};
  }

  private getFeaturesUsed(): string[] {
    // This would be populated by feature usage tracking
    return [];
  }

  private getDiskSpace(): { total: number; free: number; used: number } | undefined {
    try {
      // This would require platform-specific implementation
      // For now, return undefined
      return undefined;
    } catch {
      return undefined;
    }
  }
}

// Global metrics collector instance
let globalMetricsCollector: MetricsCollector | null = null;

export function getMetricsCollector(): MetricsCollector {
  if (!globalMetricsCollector) {
    globalMetricsCollector = new MetricsCollector();
  }
  return globalMetricsCollector;
}

export function startMetricsCollection(): void {
  const collector = getMetricsCollector();
  collector.start();
}

export function stopMetricsCollection(): void {
  if (globalMetricsCollector) {
    globalMetricsCollector.stop();
  }
}

export function recordEvent(name: TelemetryEventName, properties?: Record<string, any>): void {
  const collector = getMetricsCollector();
  collector.recordEvent(name, properties);
}

export function recordMetric(name: TelemetryMetricName, value: number, unit?: string, tags?: Record<string, string>): void {
  const collector = getMetricsCollector();
  collector.recordMetric(name, value, unit, tags);
}

export function incrementCounter(name: string, value?: number): void {
  const collector = getMetricsCollector();
  collector.incrementCounter(name, value);
}

export function setGauge(name: string, value: number): void {
  const collector = getMetricsCollector();
  collector.setGauge(name, value);
}

export function recordHistogram(name: string, value: number): void {
  const collector = getMetricsCollector();
  collector.recordHistogram(name, value);
}
