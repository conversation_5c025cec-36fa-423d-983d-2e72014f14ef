// Main API client implementation with multi-provider support
import { anthropic } from '@ai-sdk/anthropic';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { generateText, streamText, CoreMessage, CoreTool, LanguageModel } from 'ai';
import { ConfigManager, getConfig } from '../config/config.js';
import { Provider, PROVIDERS, getModelConfig } from '../config/models.js';
import { logger } from './logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ClientOptions {
  provider?: Provider;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  toolCalls?: any[];
  toolResults?: any[];
}

export interface ChatResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
}

export class ArienClient {
  private config: ConfigManager;
  private currentProvider: Provider;
  private currentModel: string;

  constructor(options: ClientOptions = {}) {
    this.config = getConfig();
    this.currentProvider = options.provider || this.config.getConfig().defaultProvider;
    this.currentModel = options.model || this.config.getDefaultModel();
    
    this.validateConfiguration();
  }

  private validateConfiguration(): void {
    const validation = this.config.validateConfig();
    if (!validation.valid) {
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        `Configuration validation failed: ${validation.errors.join(', ')}`
      );
    }

    if (!this.config.isProviderConfigured(this.currentProvider)) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        `Provider '${this.currentProvider}' is not configured. Please run 'arien auth' to set up authentication.`
      );
    }
  }

  private getLanguageModel(): LanguageModel {
    const providerConfig = this.config.getProviderConfig(this.currentProvider);
    const modelConfig = getModelConfig(this.currentModel);

    if (!modelConfig) {
      throw new ArienError(
        ErrorCode.MODEL_ERROR,
        `Model '${this.currentModel}' not found`
      );
    }

    switch (this.currentProvider) {
      case PROVIDERS.DEEPSEEK:
        return openai(this.currentModel, {
          apiKey: providerConfig.apiKey,
          baseURL: providerConfig.baseUrl || 'https://api.deepseek.com'
        });

      case PROVIDERS.OPENAI:
        return openai(this.currentModel, {
          apiKey: providerConfig.apiKey,
          organization: providerConfig.organization,
          project: providerConfig.project
        });

      case PROVIDERS.ANTHROPIC:
        return anthropic(this.currentModel, {
          apiKey: providerConfig.apiKey
        });

      case PROVIDERS.GOOGLE:
        return google(this.currentModel, {
          apiKey: providerConfig.apiKey
        });

      default:
        throw new ArienError(
          ErrorCode.PROVIDER_ERROR,
          `Unsupported provider: ${this.currentProvider}`
        );
    }
  }

  private convertMessages(messages: ChatMessage[]): CoreMessage[] {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }));
  }

  public async chat(
    messages: ChatMessage[],
    tools?: Record<string, CoreTool>,
    options: Partial<ClientOptions> = {}
  ): Promise<ChatResponse> {
    try {
      const model = this.getLanguageModel();
      const coreMessages = this.convertMessages(messages);

      logger.debug('Sending chat request', {
        provider: this.currentProvider,
        model: this.currentModel,
        messageCount: messages.length,
        toolCount: tools ? Object.keys(tools).length : 0
      });

      const result = await generateText({
        model,
        messages: coreMessages,
        tools,
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || getModelConfig(this.currentModel)?.maxTokens
      });

      logger.debug('Chat response received', {
        usage: result.usage,
        finishReason: result.finishReason
      });

      return {
        content: result.text,
        usage: result.usage ? {
          promptTokens: result.usage.promptTokens,
          completionTokens: result.usage.completionTokens,
          totalTokens: result.usage.totalTokens
        } : undefined,
        finishReason: result.finishReason
      };
    } catch (error) {
      logger.error('Chat request failed', { error, provider: this.currentProvider, model: this.currentModel });
      
      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('authentication')) {
          throw new ArienError(ErrorCode.AUTHENTICATION_ERROR, 'Authentication failed. Please check your API key.');
        }
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          throw new ArienError(ErrorCode.RATE_LIMIT_ERROR, 'Rate limit exceeded. Please try again later.');
        }
        if (error.message.includes('quota') || error.message.includes('billing')) {
          throw new ArienError(ErrorCode.QUOTA_EXCEEDED_ERROR, 'API quota exceeded. Please check your billing.');
        }
      }
      
      throw new ArienError(ErrorCode.API_ERROR, `Chat request failed: ${error}`);
    }
  }

  public async *streamChat(
    messages: ChatMessage[],
    tools?: Record<string, CoreTool>,
    options: Partial<ClientOptions> = {}
  ): AsyncGenerator<string, ChatResponse, unknown> {
    try {
      const model = this.getLanguageModel();
      const coreMessages = this.convertMessages(messages);

      logger.debug('Starting streaming chat', {
        provider: this.currentProvider,
        model: this.currentModel,
        messageCount: messages.length
      });

      const result = await streamText({
        model,
        messages: coreMessages,
        tools,
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || getModelConfig(this.currentModel)?.maxTokens
      });

      let fullContent = '';
      
      for await (const delta of result.textStream) {
        fullContent += delta;
        yield delta;
      }

      const finalResult = await result.finishReason;
      const usage = await result.usage;

      return {
        content: fullContent,
        usage: usage ? {
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
          totalTokens: usage.totalTokens
        } : undefined,
        finishReason: finalResult
      };
    } catch (error) {
      logger.error('Streaming chat failed', { error, provider: this.currentProvider, model: this.currentModel });
      throw new ArienError(ErrorCode.API_ERROR, `Streaming chat failed: ${error}`);
    }
  }

  public setProvider(provider: Provider): void {
    if (!this.config.isProviderConfigured(provider)) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        `Provider '${provider}' is not configured`
      );
    }
    this.currentProvider = provider;
  }

  public setModel(model: string): void {
    const modelConfig = getModelConfig(model);
    if (!modelConfig) {
      throw new ArienError(ErrorCode.MODEL_ERROR, `Model '${model}' not found`);
    }
    this.currentModel = model;
    this.currentProvider = modelConfig.provider as Provider;
  }

  public getCurrentProvider(): Provider {
    return this.currentProvider;
  }

  public getCurrentModel(): string {
    return this.currentModel;
  }

  public getModelInfo() {
    return getModelConfig(this.currentModel);
  }
}
