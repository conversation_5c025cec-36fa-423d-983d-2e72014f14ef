// Code block component with syntax highlighting
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../hooks/useTheme.js';

export interface CodeBlockProps {
  code: string;
  language?: string;
  showLineNumbers?: boolean;
  maxLines?: number;
}

export const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = 'text',
  showLineNumbers = true,
  maxLines = 50
}) => {
  const theme = useTheme();
  
  const lines = code.split('\n');
  const displayLines = maxLines ? lines.slice(0, maxLines) : lines;
  const hasMoreLines = lines.length > maxLines;

  // Simple syntax highlighting based on language
  const highlightLine = (line: string, lineNumber: number): React.ReactNode => {
    // Basic highlighting for common patterns
    if (language === 'javascript' || language === 'typescript' || language === 'js' || language === 'ts') {
      return highlightJavaScript(line);
    } else if (language === 'python' || language === 'py') {
      return highlightPython(line);
    } else if (language === 'json') {
      return highlightJSON(line);
    } else if (language === 'bash' || language === 'sh') {
      return highlightBash(line);
    }
    
    return <Text color={theme.colors.text}>{line}</Text>;
  };

  const highlightJavaScript = (line: string): React.ReactNode => {
    const keywords = /\b(const|let|var|function|class|if|else|for|while|return|import|export|from|async|await|try|catch|finally)\b/g;
    const strings = /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g;
    const comments = /(\/\/.*$|\/\*[\s\S]*?\*\/)/g;
    
    // This is a simplified highlighter - in production, use a proper syntax highlighter
    return <Text color={theme.colors.text}>{line}</Text>;
  };

  const highlightPython = (line: string): React.ReactNode => {
    return <Text color={theme.colors.text}>{line}</Text>;
  };

  const highlightJSON = (line: string): React.ReactNode => {
    return <Text color={theme.colors.text}>{line}</Text>;
  };

  const highlightBash = (line: string): React.ReactNode => {
    return <Text color={theme.colors.text}>{line}</Text>;
  };

  const getLineNumberWidth = (): number => {
    return Math.max(2, displayLines.length.toString().length);
  };

  return (
    <Box flexDirection="column" marginY={1}>
      {/* Header */}
      <Box
        borderStyle="single"
        borderColor={theme.colors.border}
        paddingX={1}
        paddingY={0}
      >
        <Text color={theme.colors.muted} bold>
          {language}
        </Text>
        {hasMoreLines && (
          <Box marginLeft="auto">
            <Text color={theme.colors.warning}>
              +{lines.length - maxLines} more lines
            </Text>
          </Box>
        )}
      </Box>

      {/* Code content */}
      <Box
        borderStyle="single"
        borderColor={theme.colors.border}
        borderTop={false}
        paddingX={1}
        paddingY={0}
        flexDirection="column"
      >
        {displayLines.map((line, index) => (
          <Box key={index}>
            {showLineNumbers && (
              <Text color={theme.colors.muted} dimColor>
                {(index + 1).toString().padStart(getLineNumberWidth(), ' ')}
              </Text>
            )}
            {showLineNumbers && (
              <Text color={theme.colors.muted} dimColor>
                {' │ '}
              </Text>
            )}
            <Box flexGrow={1}>
              {highlightLine(line, index + 1)}
            </Box>
          </Box>
        ))}
      </Box>

      {/* Footer with copy hint */}
      <Box
        borderStyle="single"
        borderColor={theme.colors.border}
        borderTop={false}
        paddingX={1}
        paddingY={0}
      >
        <Text color={theme.colors.muted} dimColor>
          {displayLines.length} lines
        </Text>
        <Box marginLeft="auto">
          <Text color={theme.colors.muted} dimColor>
            Ctrl+C to copy
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
