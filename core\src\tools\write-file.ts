// File writing tool with backup functionality
import { writeFile, mkdir, stat, copyFile } from 'fs/promises';
import { dirname, resolve, isAbsolute, join } from 'path';
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult } from './tools.js';
import { logger } from '../core/logger.js';

const WriteFileSchema = z.object({
  path: z.string().describe('Path to the file to write'),
  content: z.string().describe('Content to write to the file'),
  encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
  createDirectories: z.boolean().optional().default(true).describe('Create parent directories if they don\'t exist'),
  backup: z.boolean().optional().default(true).describe('Create a backup of existing file'),
  overwrite: z.boolean().optional().default(false).describe('Allow overwriting existing files')
});

export class WriteFileTool extends BaseTool {
  constructor() {
    super({
      name: 'write_file',
      description: 'Write content to a file',
      parameters: WriteFileSchema,
      requiresApproval: true,
      riskLevel: 'medium',
      category: 'file'
    });
  }

  async execute(params: z.infer<typeof WriteFileSchema>, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { path, content, encoding, createDirectories, backup, overwrite } = validatedParams;

      // Resolve the file path
      const filePath = isAbsolute(path) ? path : resolve(context.workingDirectory, path);
      const fileDir = dirname(filePath);
      
      logger.debug('Writing file', { filePath, encoding, contentLength: content.length });

      // Check if file exists
      let fileExists = false;
      let existingFileStats;
      try {
        existingFileStats = await stat(filePath);
        fileExists = existingFileStats.isFile();
      } catch (error) {
        // File doesn't exist, which is fine
      }

      // Check overwrite permission
      if (fileExists && !overwrite) {
        return this.createErrorResult(
          `File already exists: ${path}. Use overwrite: true to replace it.`
        );
      }

      // Create parent directories if needed
      if (createDirectories) {
        try {
          await mkdir(fileDir, { recursive: true });
        } catch (error) {
          return this.createErrorResult(
            `Failed to create directories: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      // Create backup if file exists and backup is enabled
      let backupPath: string | undefined;
      if (fileExists && backup) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        backupPath = `${filePath}.backup.${timestamp}`;
        
        try {
          await copyFile(filePath, backupPath);
          logger.debug('Backup created', { originalPath: filePath, backupPath });
        } catch (error) {
          logger.warn('Failed to create backup', { error, filePath });
          // Continue without backup
        }
      }

      // Write the file
      await writeFile(filePath, content, encoding);

      // Get new file stats
      const newFileStats = await stat(filePath);

      const metadata = {
        path: filePath,
        size: newFileStats.size,
        encoding,
        created: !fileExists,
        overwritten: fileExists,
        backupPath,
        lastModified: newFileStats.mtime.toISOString()
      };

      const action = fileExists ? 'updated' : 'created';
      const resultMessage = `File ${action} successfully: ${path} (${newFileStats.size} bytes)`;

      logger.debug('File written successfully', { filePath, size: newFileStats.size, action });

      return this.createSuccessResult(resultMessage, metadata);
    } catch (error) {
      logger.error('Failed to write file', { error, params });
      return this.createErrorResult(`Failed to write file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

// Append to file tool
const AppendFileSchema = z.object({
  path: z.string().describe('Path to the file to append to'),
  content: z.string().describe('Content to append to the file'),
  encoding: z.enum(['utf8', 'base64', 'hex']).optional().default('utf8').describe('File encoding'),
  createIfNotExists: z.boolean().optional().default(true).describe('Create file if it doesn\'t exist'),
  separator: z.string().optional().default('\n').describe('Separator to add before new content')
});

export class AppendFileTool extends BaseTool {
  constructor() {
    super({
      name: 'append_file',
      description: 'Append content to an existing file',
      parameters: AppendFileSchema,
      requiresApproval: true,
      riskLevel: 'medium',
      category: 'file'
    });
  }

  async execute(params: z.infer<typeof AppendFileSchema>, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { path, content, encoding, createIfNotExists, separator } = validatedParams;

      // Resolve the file path
      const filePath = isAbsolute(path) ? path : resolve(context.workingDirectory, path);
      
      logger.debug('Appending to file', { filePath, encoding, contentLength: content.length });

      // Check if file exists
      let fileExists = false;
      try {
        const stats = await stat(filePath);
        fileExists = stats.isFile();
      } catch (error) {
        // File doesn't exist
      }

      if (!fileExists && !createIfNotExists) {
        return this.createErrorResult(`File does not exist: ${path}`);
      }

      // Prepare content to append
      const contentToAppend = fileExists ? separator + content : content;

      // Use writeFile with append flag
      const fs = await import('fs');
      await fs.promises.appendFile(filePath, contentToAppend, encoding);

      // Get file stats
      const fileStats = await stat(filePath);

      const metadata = {
        path: filePath,
        size: fileStats.size,
        encoding,
        created: !fileExists,
        appended: fileExists,
        lastModified: fileStats.mtime.toISOString()
      };

      const action = fileExists ? 'appended to' : 'created';
      const resultMessage = `Content ${action} file: ${path} (${fileStats.size} bytes total)`;

      logger.debug('Content appended successfully', { filePath, size: fileStats.size, action });

      return this.createSuccessResult(resultMessage, metadata);
    } catch (error) {
      logger.error('Failed to append to file', { error, params });
      return this.createErrorResult(`Failed to append to file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
