// Sandbox configuration for security
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../core/logger.js';

export interface SandboxRule {
  type: 'allow' | 'deny';
  resource: 'file' | 'network' | 'process' | 'env';
  pattern: string;
  description?: string;
}

export interface SandboxProfile {
  name: string;
  description: string;
  rules: SandboxRule[];
  allowedCommands: string[];
  allowedPaths: string[];
  networkAccess: boolean;
  processSpawning: boolean;
  environmentAccess: string[];
  maxFileSize: number;
  maxMemoryUsage: number;
  timeoutSeconds: number;
}

export interface SandboxConfig {
  enabled: boolean;
  profile: string;
  profiles: Record<string, SandboxProfile>;
  customRules: SandboxRule[];
  logViolations: boolean;
  strictMode: boolean;
}

const SandboxRuleSchema = z.object({
  type: z.enum(['allow', 'deny']),
  resource: z.enum(['file', 'network', 'process', 'env']),
  pattern: z.string(),
  description: z.string().optional()
});

const SandboxProfileSchema = z.object({
  name: z.string(),
  description: z.string(),
  rules: z.array(SandboxRuleSchema),
  allowedCommands: z.array(z.string()),
  allowedPaths: z.array(z.string()),
  networkAccess: z.boolean(),
  processSpawning: z.boolean(),
  environmentAccess: z.array(z.string()),
  maxFileSize: z.number(),
  maxMemoryUsage: z.number(),
  timeoutSeconds: z.number()
});

const SandboxConfigSchema = z.object({
  enabled: z.boolean().default(true),
  profile: z.string().default('default'),
  profiles: z.record(SandboxProfileSchema),
  customRules: z.array(SandboxRuleSchema).default([]),
  logViolations: z.boolean().default(true),
  strictMode: z.boolean().default(false)
});

export class SandboxManager {
  private config: SandboxConfig;
  private violationLog: Array<{
    timestamp: Date;
    rule: SandboxRule;
    resource: string;
    action: string;
    userId: string;
  }> = [];

  constructor(config?: Partial<SandboxConfig>) {
    this.config = SandboxConfigSchema.parse({
      profiles: this.getDefaultProfiles(),
      ...config
    });
  }

  public isAllowed(
    resource: 'file' | 'network' | 'process' | 'env',
    action: string,
    target: string,
    userId: string
  ): boolean {
    if (!this.config.enabled) {
      return true;
    }

    const profile = this.getCurrentProfile();
    if (!profile) {
      logger.warn('Sandbox profile not found, denying access', {
        profileName: this.config.profile,
        resource,
        action,
        target
      });
      return false;
    }

    // Check profile-specific rules first
    const profileResult = this.checkProfileRules(profile, resource, action, target);
    if (profileResult !== null) {
      if (!profileResult) {
        this.logViolation(resource, action, target, userId, 'profile rule');
      }
      return profileResult;
    }

    // Check custom rules
    const customResult = this.checkCustomRules(resource, action, target);
    if (customResult !== null) {
      if (!customResult) {
        this.logViolation(resource, action, target, userId, 'custom rule');
      }
      return customResult;
    }

    // Default behavior based on strict mode
    const allowed = !this.config.strictMode;
    if (!allowed) {
      this.logViolation(resource, action, target, userId, 'strict mode default deny');
    }

    return allowed;
  }

  public checkFileAccess(filePath: string, operation: 'read' | 'write' | 'execute', userId: string): boolean {
    const profile = this.getCurrentProfile();
    if (!profile) {
      return false;
    }

    // Check if path is in allowed paths
    const normalizedPath = path.resolve(filePath);
    const isAllowedPath = profile.allowedPaths.some(allowedPath => {
      const normalizedAllowedPath = path.resolve(allowedPath);
      return normalizedPath.startsWith(normalizedAllowedPath);
    });

    if (!isAllowedPath) {
      this.logViolation('file', operation, filePath, userId, 'path not in allowed list');
      return false;
    }

    // Check file size limits for write operations
    if (operation === 'write') {
      try {
        const stats = require('fs').statSync(filePath);
        if (stats.size > profile.maxFileSize) {
          this.logViolation('file', operation, filePath, userId, 'file size exceeds limit');
          return false;
        }
      } catch {
        // File doesn't exist yet, which is fine for write operations
      }
    }

    return this.isAllowed('file', operation, filePath, userId);
  }

  public checkNetworkAccess(url: string, method: string, userId: string): boolean {
    const profile = this.getCurrentProfile();
    if (!profile || !profile.networkAccess) {
      this.logViolation('network', method, url, userId, 'network access disabled');
      return false;
    }

    return this.isAllowed('network', method, url, userId);
  }

  public checkProcessSpawning(command: string, args: string[], userId: string): boolean {
    const profile = this.getCurrentProfile();
    if (!profile || !profile.processSpawning) {
      this.logViolation('process', 'spawn', command, userId, 'process spawning disabled');
      return false;
    }

    // Check if command is in allowed list
    const baseCommand = command.split(' ')[0];
    if (!profile.allowedCommands.includes(baseCommand)) {
      this.logViolation('process', 'spawn', command, userId, 'command not in allowed list');
      return false;
    }

    return this.isAllowed('process', 'spawn', `${command} ${args.join(' ')}`, userId);
  }

  public checkEnvironmentAccess(variable: string, operation: 'read' | 'write', userId: string): boolean {
    const profile = this.getCurrentProfile();
    if (!profile) {
      return false;
    }

    // Check if environment variable is in allowed list
    if (!profile.environmentAccess.includes(variable) && !profile.environmentAccess.includes('*')) {
      this.logViolation('env', operation, variable, userId, 'environment variable not in allowed list');
      return false;
    }

    return this.isAllowed('env', operation, variable, userId);
  }

  private checkProfileRules(
    profile: SandboxProfile,
    resource: string,
    action: string,
    target: string
  ): boolean | null {
    for (const rule of profile.rules) {
      if (rule.resource === resource && this.matchesPattern(target, rule.pattern)) {
        return rule.type === 'allow';
      }
    }
    return null;
  }

  private checkCustomRules(
    resource: string,
    action: string,
    target: string
  ): boolean | null {
    for (const rule of this.config.customRules) {
      if (rule.resource === resource && this.matchesPattern(target, rule.pattern)) {
        return rule.type === 'allow';
      }
    }
    return null;
  }

  private matchesPattern(target: string, pattern: string): boolean {
    // Convert glob pattern to regex
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(target);
  }

  private logViolation(
    resource: string,
    action: string,
    target: string,
    userId: string,
    reason: string
  ): void {
    if (!this.config.logViolations) {
      return;
    }

    const violation = {
      timestamp: new Date(),
      rule: {
        type: 'deny' as const,
        resource: resource as any,
        pattern: target,
        description: reason
      },
      resource: target,
      action,
      userId
    };

    this.violationLog.push(violation);

    // Keep only last 1000 violations
    if (this.violationLog.length > 1000) {
      this.violationLog = this.violationLog.slice(-1000);
    }

    logger.warn('Sandbox violation', {
      resource,
      action,
      target,
      userId,
      reason
    });
  }

  public getCurrentProfile(): SandboxProfile | undefined {
    return this.config.profiles[this.config.profile];
  }

  public setProfile(profileName: string): boolean {
    if (this.config.profiles[profileName]) {
      this.config.profile = profileName;
      logger.info('Sandbox profile changed', { profileName });
      return true;
    }
    return false;
  }

  public addCustomRule(rule: SandboxRule): void {
    this.config.customRules.push(rule);
    logger.info('Custom sandbox rule added', { rule });
  }

  public removeCustomRule(index: number): boolean {
    if (index >= 0 && index < this.config.customRules.length) {
      const removed = this.config.customRules.splice(index, 1)[0];
      logger.info('Custom sandbox rule removed', { rule: removed });
      return true;
    }
    return false;
  }

  public getViolationLog(): typeof this.violationLog {
    return [...this.violationLog];
  }

  public clearViolationLog(): void {
    this.violationLog = [];
    logger.info('Sandbox violation log cleared');
  }

  public getConfig(): SandboxConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<SandboxConfig>): void {
    this.config = SandboxConfigSchema.parse({ ...this.config, ...updates });
    logger.info('Sandbox config updated');
  }

  private getDefaultProfiles(): Record<string, SandboxProfile> {
    return {
      permissive: {
        name: 'Permissive',
        description: 'Allows most operations with basic safety checks',
        rules: [
          {
            type: 'deny',
            resource: 'file',
            pattern: '/etc/passwd',
            description: 'Deny access to system password file'
          },
          {
            type: 'deny',
            resource: 'file',
            pattern: '/etc/shadow',
            description: 'Deny access to system shadow file'
          },
          {
            type: 'deny',
            resource: 'file',
            pattern: '*.key',
            description: 'Deny access to private key files'
          }
        ],
        allowedCommands: ['git', 'npm', 'node', 'python', 'pip', 'ls', 'cat', 'grep', 'find'],
        allowedPaths: [process.cwd(), require('os').homedir()],
        networkAccess: true,
        processSpawning: true,
        environmentAccess: ['PATH', 'HOME', 'USER', 'NODE_ENV'],
        maxFileSize: 100 * 1024 * 1024, // 100MB
        maxMemoryUsage: 1024 * 1024 * 1024, // 1GB
        timeoutSeconds: 300
      },

      restrictive: {
        name: 'Restrictive',
        description: 'Strict security with limited operations',
        rules: [
          {
            type: 'allow',
            resource: 'file',
            pattern: `${process.cwd()}/*`,
            description: 'Allow access to current working directory'
          },
          {
            type: 'deny',
            resource: 'network',
            pattern: '*',
            description: 'Deny all network access'
          }
        ],
        allowedCommands: ['git', 'ls', 'cat'],
        allowedPaths: [process.cwd()],
        networkAccess: false,
        processSpawning: false,
        environmentAccess: ['PATH'],
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxMemoryUsage: 256 * 1024 * 1024, // 256MB
        timeoutSeconds: 60
      },

      development: {
        name: 'Development',
        description: 'Balanced security for development work',
        rules: [
          {
            type: 'allow',
            resource: 'file',
            pattern: `${process.cwd()}/*`,
            description: 'Allow access to project directory'
          },
          {
            type: 'allow',
            resource: 'network',
            pattern: 'https://*',
            description: 'Allow HTTPS requests'
          },
          {
            type: 'deny',
            resource: 'network',
            pattern: 'http://localhost*',
            description: 'Deny localhost access'
          }
        ],
        allowedCommands: ['git', 'npm', 'node', 'python', 'pip', 'ls', 'cat', 'grep', 'find', 'curl'],
        allowedPaths: [process.cwd(), path.join(require('os').homedir(), '.npm')],
        networkAccess: true,
        processSpawning: true,
        environmentAccess: ['PATH', 'HOME', 'USER', 'NODE_ENV', 'NPM_TOKEN'],
        maxFileSize: 50 * 1024 * 1024, // 50MB
        maxMemoryUsage: 512 * 1024 * 1024, // 512MB
        timeoutSeconds: 180
      }
    };
  }
}
