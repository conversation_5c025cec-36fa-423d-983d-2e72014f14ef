#!/bin/bash

# Arien CLI Installation Script
set -e

echo "🤖 Installing Arien CLI..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "❌ Node.js version $NODE_VERSION is not supported. Please install Node.js 18+ first."
    exit 1
fi

echo "✅ Node.js $NODE_VERSION detected"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ npm detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm run install:all

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

echo "✅ Dependencies installed"

# Build the project
echo "🔨 Building project..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build project"
    exit 1
fi

echo "✅ Project built successfully"

# Create symlink for global access (optional)
read -p "🔗 Create global symlink for 'arien' command? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v sudo &> /dev/null; then
        sudo npm link cli
        echo "✅ Global symlink created. You can now use 'arien' command anywhere."
    else
        npm link cli
        echo "✅ Global symlink created. You can now use 'arien' command anywhere."
    fi
else
    echo "ℹ️  You can run Arien CLI with: npm start"
fi

echo ""
echo "🎉 Arien CLI installation completed!"
echo ""
echo "Next steps:"
echo "1. Configure your AI provider: arien auth"
echo "2. Start using Arien: arien \"Hello, how can you help me?\""
echo ""
echo "For more information, run: arien --help"
