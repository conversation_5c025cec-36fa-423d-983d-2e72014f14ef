{"name": "@arien/cli", "version": "1.0.0", "description": "Terminal interface for Arien CLI", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "bin": {"arien": "./dist/index.js"}, "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "dependencies": {"@arien/core": "workspace:*", "ink": "^4.4.0", "react": "^18.2.0", "chalk": "^5.3.0", "commander": "^11.0.0", "conf": "^11.0.0", "figures": "^5.0.0", "ink-spinner": "^5.0.0", "ink-text-input": "^5.0.0", "ink-select-input": "^5.0.0", "ink-box": "^3.0.0", "ink-gradient": "^3.0.0", "ink-big-text": "^2.0.0", "ink-divider": "^3.0.0", "ink-table": "^3.0.0", "ink-testing-library": "^3.0.0", "lodash": "^4.17.21", "ora": "^7.0.0", "strip-ansi": "^7.1.0", "terminal-size": "^3.0.0", "uuid": "^9.0.0", "wrap-ansi": "^9.0.0", "yargs": "^17.7.0"}, "devDependencies": {"@types/lodash": "^4.14.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/uuid": "^9.0.0", "@types/yargs": "^17.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0", "vitest": "^1.0.0"}, "keywords": ["cli", "terminal", "ui", "react", "ink"], "author": "Arien CLI Team", "license": "MIT"}