// Error reporting and handling utilities
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from './errors.js';

export interface ErrorReport {
  id: string;
  timestamp: Date;
  error: ArienError;
  context: {
    userId: string;
    sessionId: string;
    command?: string;
    provider?: string;
    model?: string;
    [key: string]: any;
  };
  stackTrace?: string;
  userAgent?: string;
  version?: string;
}

export class ErrorReporter {
  private reports: ErrorReport[] = [];
  private maxReports = 100;

  public reportError(error: ArienError, context: any = {}): string {
    const reportId = this.generateReportId();
    
    const report: ErrorReport = {
      id: reportId,
      timestamp: new Date(),
      error,
      context,
      stackTrace: error.stack,
      version: process.env.npm_package_version || '1.0.0'
    };

    this.reports.push(report);
    
    // Keep only the most recent reports
    if (this.reports.length > this.maxReports) {
      this.reports = this.reports.slice(-this.maxReports);
    }

    // Log the error
    logger.error('Error reported', {
      reportId,
      code: error.code,
      message: error.message,
      context
    });

    return reportId;
  }

  public getReport(reportId: string): ErrorReport | undefined {
    return this.reports.find(report => report.id === reportId);
  }

  public getRecentReports(limit: number = 10): ErrorReport[] {
    return this.reports.slice(-limit);
  }

  public getReportsByErrorCode(code: ErrorCode): ErrorReport[] {
    return this.reports.filter(report => report.error.code === code);
  }

  public clearReports(): void {
    this.reports = [];
  }

  private generateReportId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Global error reporter
export const errorReporter = new ErrorReporter();

// Global error handlers
export function setupGlobalErrorHandlers(): void {
  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception', { error });
    
    if (error instanceof ArienError) {
      errorReporter.reportError(error, { type: 'uncaughtException' });
    } else {
      const arienError = new ArienError(ErrorCode.SYSTEM_ERROR, error.message);
      errorReporter.reportError(arienError, { type: 'uncaughtException', originalError: error });
    }
    
    // Exit gracefully
    process.exit(1);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled promise rejection', { reason, promise });
    
    let arienError: ArienError;
    if (reason instanceof ArienError) {
      arienError = reason;
    } else if (reason instanceof Error) {
      arienError = new ArienError(ErrorCode.SYSTEM_ERROR, reason.message);
    } else {
      arienError = new ArienError(ErrorCode.UNKNOWN_ERROR, String(reason));
    }
    
    errorReporter.reportError(arienError, { type: 'unhandledRejection', promise });
  });

  // Handle SIGINT (Ctrl+C)
  process.on('SIGINT', () => {
    logger.info('Received SIGINT, shutting down gracefully');
    process.exit(0);
  });

  // Handle SIGTERM
  process.on('SIGTERM', () => {
    logger.info('Received SIGTERM, shutting down gracefully');
    process.exit(0);
  });
}

export function formatErrorForUser(error: ArienError): string {
  const messages: Record<ErrorCode, string> = {
    [ErrorCode.CONFIGURATION_ERROR]: '⚙️ Configuration Error',
    [ErrorCode.AUTHENTICATION_ERROR]: '🔐 Authentication Error',
    [ErrorCode.API_ERROR]: '🌐 API Error',
    [ErrorCode.RATE_LIMIT_ERROR]: '⏱️ Rate Limit Error',
    [ErrorCode.QUOTA_EXCEEDED_ERROR]: '💳 Quota Exceeded',
    [ErrorCode.MODEL_ERROR]: '🤖 Model Error',
    [ErrorCode.PROVIDER_ERROR]: '🔌 Provider Error',
    [ErrorCode.TOOL_ERROR]: '🔧 Tool Error',
    [ErrorCode.TOOL_VALIDATION_ERROR]: '✅ Tool Validation Error',
    [ErrorCode.TOOL_EXECUTION_ERROR]: '⚡ Tool Execution Error',
    [ErrorCode.FILE_NOT_FOUND]: '📁 File Not Found',
    [ErrorCode.FILE_ACCESS_ERROR]: '📄 File Access Error',
    [ErrorCode.PERMISSION_DENIED]: '🚫 Permission Denied',
    [ErrorCode.SECURITY_ERROR]: '🛡️ Security Error',
    [ErrorCode.SANDBOX_ERROR]: '📦 Sandbox Error',
    [ErrorCode.NETWORK_ERROR]: '🌐 Network Error',
    [ErrorCode.TIMEOUT_ERROR]: '⏰ Timeout Error',
    [ErrorCode.USER_CANCELLED]: '❌ Cancelled',
    [ErrorCode.INVALID_INPUT]: '📝 Invalid Input',
    [ErrorCode.SYSTEM_ERROR]: '💻 System Error',
    [ErrorCode.MEMORY_ERROR]: '🧠 Memory Error',
    [ErrorCode.UNKNOWN_ERROR]: '❓ Unknown Error'
  };

  const prefix = messages[error.code] || '❓ Error';
  return `${prefix}: ${error.message}`;
}

export function getSuggestedFix(error: ArienError): string | null {
  const suggestions: Partial<Record<ErrorCode, string>> = {
    [ErrorCode.CONFIGURATION_ERROR]: 'Run "arien config show" to check your configuration.',
    [ErrorCode.AUTHENTICATION_ERROR]: 'Run "arien auth" to configure your API credentials.',
    [ErrorCode.RATE_LIMIT_ERROR]: 'Wait a moment and try again, or upgrade your API plan.',
    [ErrorCode.QUOTA_EXCEEDED_ERROR]: 'Check your API usage and billing settings.',
    [ErrorCode.MODEL_ERROR]: 'Try using a different model with "arien models".',
    [ErrorCode.PROVIDER_ERROR]: 'Check if the provider service is available.',
    [ErrorCode.FILE_NOT_FOUND]: 'Check the file path and ensure the file exists.',
    [ErrorCode.PERMISSION_DENIED]: 'Check file permissions or run with appropriate privileges.',
    [ErrorCode.NETWORK_ERROR]: 'Check your internet connection and try again.',
    [ErrorCode.TIMEOUT_ERROR]: 'The operation took too long. Try with a smaller request.',
    [ErrorCode.INVALID_INPUT]: 'Check your input format and try again.'
  };

  return suggestions[error.code] || null;
}
