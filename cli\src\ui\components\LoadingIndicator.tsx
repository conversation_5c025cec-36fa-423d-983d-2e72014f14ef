// Generic loading indicator component
import React from 'react';
import { Box, Text } from 'ink';
import Spinner from 'ink-spinner';

interface LoadingIndicatorProps {
  message?: string;
  color?: string;
}

export const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ 
  message = 'Loading...', 
  color = 'blue' 
}) => {
  return (
    <Box justifyContent="center" alignItems="center" height="100%">
      <Box flexDirection="column" alignItems="center">
        <Box marginBottom={1}>
          <Text color={color}>
            <Spinner type="dots" />
          </Text>
          <Text> {message}</Text>
        </Box>
        
        <Text dimColor>
          Please wait...
        </Text>
      </Box>
    </Box>
  );
};
