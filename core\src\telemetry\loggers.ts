// Telemetry loggers implementation
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../core/logger.js';
import { 
  TelemetryEvent, 
  TelemetryMetric, 
  TelemetryBatch, 
  TelemetryConfig,
  ErrorReport,
  TelemetryProvider
} from './types.js';
import { 
  DEFAULT_TELEMETRY_CONFIG,
  BATCH_LIMITS,
  QUEUE_LIMITS,
  RETENTION_PERIODS
} from './constants.js';

export class TelemetryLogger {
  private config: TelemetryConfig;
  private eventQueue: TelemetryEvent[] = [];
  private metricQueue: TelemetryMetric[] = [];
  private providers: TelemetryProvider[] = [];
  private flushTimer?: NodeJS.Timeout;
  private isShuttingDown = false;

  constructor(config: Partial<TelemetryConfig> = {}) {
    this.config = { ...DEFAULT_TELEMETRY_CONFIG, ...config };
    this.startFlushTimer();
  }

  public addProvider(provider: TelemetryProvider): void {
    this.providers.push(provider);
    provider.configure(this.config);
    logger.debug('Telemetry provider added', { providerName: provider.name });
  }

  public removeProvider(providerName: string): void {
    const index = this.providers.findIndex(p => p.name === providerName);
    if (index !== -1) {
      this.providers.splice(index, 1);
      logger.debug('Telemetry provider removed', { providerName });
    }
  }

  public async logEvent(event: TelemetryEvent): Promise<void> {
    if (!this.config.enabled || this.isShuttingDown) {
      return;
    }

    try {
      // Validate event size
      const eventSize = JSON.stringify(event).length;
      if (eventSize > BATCH_LIMITS.MAX_EVENT_SIZE_BYTES) {
        logger.warn('Event too large, skipping', { 
          eventName: event.name, 
          size: eventSize 
        });
        return;
      }

      // Add to queue
      this.eventQueue.push(event);
      
      // Check if we need to flush
      if (this.shouldFlush()) {
        await this.flush();
      }

      logger.debug('Event logged', { eventName: event.name });
    } catch (error) {
      logger.error('Failed to log event', { 
        eventName: event.name, 
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async logMetric(metric: TelemetryMetric): Promise<void> {
    if (!this.config.enabled || this.isShuttingDown) {
      return;
    }

    try {
      // Validate metric size
      const metricSize = JSON.stringify(metric).length;
      if (metricSize > BATCH_LIMITS.MAX_METRIC_SIZE_BYTES) {
        logger.warn('Metric too large, skipping', { 
          metricName: metric.name, 
          size: metricSize 
        });
        return;
      }

      // Add to queue
      this.metricQueue.push(metric);
      
      // Check if we need to flush
      if (this.shouldFlush()) {
        await this.flush();
      }

      logger.debug('Metric logged', { metricName: metric.name, value: metric.value });
    } catch (error) {
      logger.error('Failed to log metric', { 
        metricName: metric.name, 
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async logError(errorReport: ErrorReport): Promise<void> {
    if (!this.config.enabled || !this.config.collectErrorReports) {
      return;
    }

    const event: TelemetryEvent = {
      name: 'error_occurred',
      timestamp: errorReport.timestamp,
      userId: errorReport.userId,
      sessionId: errorReport.sessionId,
      properties: {
        errorType: errorReport.errorType,
        errorMessage: errorReport.errorMessage,
        errorCode: errorReport.errorCode,
        stackTrace: errorReport.stackTrace,
        context: errorReport.context,
        severity: errorReport.severity,
        reproducible: errorReport.reproducible
      },
      context: {
        version: errorReport.version,
        platform: errorReport.platform,
        arch: process.arch,
        nodeVersion: process.version
      }
    };

    await this.logEvent(event);
  }

  public async flush(): Promise<void> {
    if (this.eventQueue.length === 0 && this.metricQueue.length === 0) {
      return;
    }

    try {
      const batch = this.createBatch();
      
      if (batch.events.length === 0 && batch.metrics.length === 0) {
        return;
      }

      // Send to all providers
      const sendPromises = this.providers.map(async (provider) => {
        try {
          await provider.send(batch);
          logger.debug('Batch sent to provider', { 
            providerName: provider.name,
            eventCount: batch.events.length,
            metricCount: batch.metrics.length
          });
        } catch (error) {
          logger.error('Failed to send batch to provider', {
            providerName: provider.name,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      });

      await Promise.allSettled(sendPromises);

      // Clear queues after successful send
      this.eventQueue = [];
      this.metricQueue = [];

      logger.debug('Telemetry batch flushed', {
        eventCount: batch.events.length,
        metricCount: batch.metrics.length,
        providerCount: this.providers.length
      });

    } catch (error) {
      logger.error('Failed to flush telemetry batch', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    
    // Stop flush timer
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    // Flush remaining data
    await this.flush();

    // Close all providers
    const closePromises = this.providers.map(async (provider) => {
      try {
        await provider.close();
        logger.debug('Provider closed', { providerName: provider.name });
      } catch (error) {
        logger.error('Failed to close provider', {
          providerName: provider.name,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    });

    await Promise.allSettled(closePromises);

    logger.info('Telemetry logger shut down');
  }

  public updateConfig(config: Partial<TelemetryConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Update provider configurations
    for (const provider of this.providers) {
      provider.configure(this.config);
    }

    // Restart flush timer if interval changed
    if (config.flushInterval !== undefined) {
      this.startFlushTimer();
    }

    logger.debug('Telemetry config updated', { config });
  }

  public getQueueStats(): {
    eventCount: number;
    metricCount: number;
    totalSize: number;
    memoryUsage: number;
  } {
    const eventSize = JSON.stringify(this.eventQueue).length;
    const metricSize = JSON.stringify(this.metricQueue).length;
    
    return {
      eventCount: this.eventQueue.length,
      metricCount: this.metricQueue.length,
      totalSize: eventSize + metricSize,
      memoryUsage: process.memoryUsage().heapUsed
    };
  }

  private createBatch(): TelemetryBatch {
    const now = new Date();
    const batchId = `batch_${now.getTime()}_${Math.random().toString(36).substr(2, 9)}`;

    // Limit batch size
    const events = this.eventQueue.slice(0, BATCH_LIMITS.MAX_EVENTS_PER_BATCH);
    const metrics = this.metricQueue.slice(0, BATCH_LIMITS.MAX_METRICS_PER_BATCH);

    return {
      events,
      metrics,
      timestamp: now,
      batchId
    };
  }

  private shouldFlush(): boolean {
    const stats = this.getQueueStats();
    
    return (
      stats.eventCount >= this.config.batchSize ||
      stats.metricCount >= this.config.batchSize ||
      stats.totalSize >= QUEUE_LIMITS.FLUSH_ON_MEMORY_USAGE ||
      stats.eventCount + stats.metricCount >= QUEUE_LIMITS.FLUSH_ON_QUEUE_SIZE
    );
  }

  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(async () => {
      try {
        await this.flush();
      } catch (error) {
        logger.error('Scheduled flush failed', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }, this.config.flushInterval);
  }
}

export class FileTelemetryProvider implements TelemetryProvider {
  public readonly name = 'file';
  private config: TelemetryConfig = DEFAULT_TELEMETRY_CONFIG;
  private logDir: string;

  constructor(logDir: string) {
    this.logDir = logDir;
  }

  public configure(config: Partial<TelemetryConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public async send(batch: TelemetryBatch): Promise<void> {
    try {
      // Ensure log directory exists
      await fs.mkdir(this.logDir, { recursive: true });

      // Write events
      if (batch.events.length > 0) {
        const eventsFile = path.join(this.logDir, `events_${batch.timestamp.toISOString().split('T')[0]}.jsonl`);
        const eventsData = batch.events.map(event => JSON.stringify(event)).join('\n') + '\n';
        await fs.appendFile(eventsFile, eventsData);
      }

      // Write metrics
      if (batch.metrics.length > 0) {
        const metricsFile = path.join(this.logDir, `metrics_${batch.timestamp.toISOString().split('T')[0]}.jsonl`);
        const metricsData = batch.metrics.map(metric => JSON.stringify(metric)).join('\n') + '\n';
        await fs.appendFile(metricsFile, metricsData);
      }

    } catch (error) {
      throw new Error(`Failed to write telemetry to file: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async flush(): Promise<void> {
    // File provider doesn't need explicit flushing
  }

  public async close(): Promise<void> {
    // Cleanup old files
    await this.cleanupOldFiles();
  }

  private async cleanupOldFiles(): Promise<void> {
    try {
      const files = await fs.readdir(this.logDir);
      const now = Date.now();
      
      for (const file of files) {
        const filePath = path.join(this.logDir, file);
        const stats = await fs.stat(filePath);
        
        // Remove files older than retention period
        if (now - stats.mtime.getTime() > RETENTION_PERIODS.EVENTS) {
          await fs.unlink(filePath);
          logger.debug('Removed old telemetry file', { file });
        }
      }
    } catch (error) {
      logger.error('Failed to cleanup old telemetry files', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}

export class ConsoleTelemetryProvider implements TelemetryProvider {
  public readonly name = 'console';
  private config: TelemetryConfig = DEFAULT_TELEMETRY_CONFIG;

  public configure(config: Partial<TelemetryConfig>): void {
    this.config = { ...this.config, ...config };
  }

  public async send(batch: TelemetryBatch): Promise<void> {
    if (this.config.debug) {
      console.log('=== Telemetry Batch ===');
      console.log(`Batch ID: ${batch.batchId}`);
      console.log(`Timestamp: ${batch.timestamp.toISOString()}`);
      console.log(`Events: ${batch.events.length}`);
      console.log(`Metrics: ${batch.metrics.length}`);
      
      if (batch.events.length > 0) {
        console.log('\nEvents:');
        batch.events.forEach(event => {
          console.log(`  ${event.name}: ${JSON.stringify(event.properties || {})}`);
        });
      }
      
      if (batch.metrics.length > 0) {
        console.log('\nMetrics:');
        batch.metrics.forEach(metric => {
          console.log(`  ${metric.name}: ${metric.value} ${metric.unit || ''}`);
        });
      }
      
      console.log('=====================\n');
    }
  }

  public async flush(): Promise<void> {
    // Console provider doesn't need explicit flushing
  }

  public async close(): Promise<void> {
    // Console provider doesn't need cleanup
  }
}

// Global telemetry logger instance
let globalTelemetryLogger: TelemetryLogger | null = null;

export function getTelemetryLogger(): TelemetryLogger {
  if (!globalTelemetryLogger) {
    globalTelemetryLogger = new TelemetryLogger();
  }
  return globalTelemetryLogger;
}

export function initTelemetryLogger(config: Partial<TelemetryConfig> = {}): TelemetryLogger {
  globalTelemetryLogger = new TelemetryLogger(config);
  return globalTelemetryLogger;
}

export async function shutdownTelemetry(): Promise<void> {
  if (globalTelemetryLogger) {
    await globalTelemetryLogger.shutdown();
    globalTelemetryLogger = null;
  }
}
