// Theme selection dialog component
import React, { useState } from 'react';
import { Box, Text, Newline } from 'ink';
import SelectInput from 'ink-select-input';
import { themeManager } from '../themes/theme-manager.js';

interface ThemeDialogProps {
  onComplete: () => void;
  onCancel: () => void;
}

interface ThemeOption {
  label: string;
  value: string;
  description: string;
}

export const ThemeDialog: React.FC<ThemeDialogProps> = ({ onComplete, onCancel }) => {
  const [selectedTheme, setSelectedTheme] = useState<string | null>(null);

  const currentTheme = themeManager.getCurrentTheme();
  const availableThemes = themeManager.getAvailableThemes();

  const themeOptions: ThemeOption[] = availableThemes.map(themeName => {
    const descriptions: Record<string, string> = {
      'default': 'Classic terminal colors with blue accents',
      'dracula': 'Dark theme with purple and pink highlights',
      'github-dark': 'GitHub\'s dark theme with blue accents'
    };

    return {
      label: themeName.charAt(0).toUpperCase() + themeName.slice(1).replace('-', ' '),
      value: themeName,
      description: descriptions[themeName] || 'Custom theme'
    };
  });

  const handleThemeSelect = (item: ThemeOption) => {
    setSelectedTheme(item.value);
    themeManager.setTheme(item.value);
    onComplete();
  };

  return (
    <Box flexDirection="column">
      <Text bold color={currentTheme.colors.primary}>
        🎨 Theme Selection
      </Text>
      <Newline />
      <Text>
        Current theme: <Text bold>{currentTheme.name}</Text>
      </Text>
      <Text>Select a new theme:</Text>
      <Newline />
      
      <SelectInput
        items={themeOptions}
        onSelect={handleThemeSelect}
        itemComponent={({ item, isSelected }) => (
          <Box flexDirection="column">
            <Text color={isSelected ? currentTheme.colors.primary : currentTheme.colors.foreground}>
              {isSelected ? '❯ ' : '  '}
              {item.label}
              {item.value === currentTheme.name && ' (current)'}
            </Text>
            {isSelected && (
              <Box marginLeft={4}>
                <Text color={currentTheme.colors.muted}>{item.description}</Text>
              </Box>
            )}
          </Box>
        )}
      />
      
      <Newline />
      <Text color={currentTheme.colors.muted}>
        Press Enter to select, Ctrl+C to cancel
      </Text>
    </Box>
  );
};
