// Code diff visualization component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../../hooks/useTheme.js';

export interface DiffLine {
  type: 'add' | 'remove' | 'context' | 'header';
  content: string;
  lineNumber?: {
    old?: number;
    new?: number;
  };
  highlight?: boolean;
}

export interface DiffHunk {
  header: string;
  lines: DiffLine[];
  oldStart: number;
  oldCount: number;
  newStart: number;
  newCount: number;
}

export interface DiffFile {
  filename: string;
  oldFilename?: string;
  hunks: DiffHunk[];
  isNew?: boolean;
  isDeleted?: boolean;
  isBinary?: boolean;
  stats?: {
    additions: number;
    deletions: number;
    changes: number;
  };
}

interface DiffRendererProps {
  diff: DiffFile | DiffFile[];
  showLineNumbers?: boolean;
  showStats?: boolean;
  maxLines?: number;
  allowCollapse?: boolean;
  highlightSyntax?: boolean;
}

export const DiffRenderer: React.FC<DiffRendererProps> = ({
  diff,
  showLineNumbers = true,
  showStats = true,
  maxLines = 50,
  allowCollapse = true,
  highlightSyntax = false
}) => {
  const theme = useTheme();
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set());
  const [selectedFile, setSelectedFile] = useState<string | null>(null);

  const files = Array.isArray(diff) ? diff : [diff];

  useInput((input, key) => {
    if (key.return && selectedFile) {
      setExpandedFiles(prev => {
        const newSet = new Set(prev);
        if (newSet.has(selectedFile)) {
          newSet.delete(selectedFile);
        } else {
          newSet.add(selectedFile);
        }
        return newSet;
      });
    }
  });

  const getLineTypeColor = (type: DiffLine['type']) => {
    switch (type) {
      case 'add': return theme.colors.success;
      case 'remove': return theme.colors.error;
      case 'context': return theme.colors.text;
      case 'header': return theme.colors.info;
      default: return theme.colors.text;
    }
  };

  const getLineTypeIcon = (type: DiffLine['type']) => {
    switch (type) {
      case 'add': return '+';
      case 'remove': return '-';
      case 'context': return ' ';
      case 'header': return '@';
      default: return ' ';
    }
  };

  const formatLineNumber = (num: number | undefined, width: number) => {
    if (num === undefined) return ' '.repeat(width);
    return num.toString().padStart(width, ' ');
  };

  const renderFileHeader = (file: DiffFile) => {
    const isExpanded = expandedFiles.has(file.filename);
    const totalLines = file.hunks.reduce((sum, hunk) => sum + hunk.lines.length, 0);
    const isSelected = selectedFile === file.filename;

    return (
      <Box
        flexDirection="column"
        marginBottom={1}
        borderStyle={isSelected ? 'single' : undefined}
        borderColor={isSelected ? theme.colors.primary : undefined}
        paddingX={isSelected ? 1 : 0}
      >
        {/* File name and status */}
        <Box flexDirection="row">
          <Text color={theme.colors.primary}>
            {isExpanded ? '📂' : '📄'} {file.filename}
          </Text>
          {file.oldFilename && file.oldFilename !== file.filename && (
            <Text color={theme.colors.muted}>
              {' '}(renamed from {file.oldFilename})
            </Text>
          )}
          {file.isNew && (
            <Text color={theme.colors.success}> [NEW]</Text>
          )}
          {file.isDeleted && (
            <Text color={theme.colors.error}> [DELETED]</Text>
          )}
          {file.isBinary && (
            <Text color={theme.colors.warning}> [BINARY]</Text>
          )}
        </Box>

        {/* Stats */}
        {showStats && file.stats && (
          <Box marginLeft={2}>
            <Text color={theme.colors.success}>
              +{file.stats.additions}
            </Text>
            <Text color={theme.colors.error}>
              {' '}-{file.stats.deletions}
            </Text>
            <Text color={theme.colors.muted}>
              {' '}({file.stats.changes} changes)
            </Text>
          </Box>
        )}

        {/* Collapse indicator */}
        {allowCollapse && totalLines > maxLines && (
          <Box marginLeft={2}>
            <Text color={theme.colors.muted}>
              {isExpanded 
                ? `Showing all ${totalLines} lines` 
                : `${totalLines} lines (Press Enter to expand)`}
            </Text>
          </Box>
        )}
      </Box>
    );
  };

  const renderHunk = (hunk: DiffHunk, fileIndex: number, hunkIndex: number) => {
    const maxOldLineNum = Math.max(...hunk.lines.map(l => l.lineNumber?.old || 0));
    const maxNewLineNum = Math.max(...hunk.lines.map(l => l.lineNumber?.new || 0));
    const lineNumWidth = Math.max(
      maxOldLineNum.toString().length,
      maxNewLineNum.toString().length,
      3
    );

    return (
      <Box key={`${fileIndex}-${hunkIndex}`} flexDirection="column" marginBottom={1}>
        {/* Hunk header */}
        <Box>
          <Text color={theme.colors.info}>
            @@ -{hunk.oldStart},{hunk.oldCount} +{hunk.newStart},{hunk.newCount} @@
          </Text>
          <Text color={theme.colors.muted}> {hunk.header}</Text>
        </Box>

        {/* Hunk lines */}
        <Box flexDirection="column">
          {hunk.lines.map((line, lineIndex) => (
            <Box key={lineIndex} flexDirection="row">
              {/* Line numbers */}
              {showLineNumbers && (
                <Box flexDirection="row" marginRight={1}>
                  <Text color={theme.colors.muted}>
                    {formatLineNumber(line.lineNumber?.old, lineNumWidth)}
                  </Text>
                  <Text color={theme.colors.muted}> </Text>
                  <Text color={theme.colors.muted}>
                    {formatLineNumber(line.lineNumber?.new, lineNumWidth)}
                  </Text>
                </Box>
              )}

              {/* Line type indicator */}
              <Text color={getLineTypeColor(line.type)}>
                {getLineTypeIcon(line.type)}
              </Text>

              {/* Line content */}
              <Text 
                color={getLineTypeColor(line.type)}
                backgroundColor={line.highlight ? theme.colors.warning : undefined}
              >
                {line.content}
              </Text>
            </Box>
          ))}
        </Box>
      </Box>
    );
  };

  const renderFile = (file: DiffFile, index: number) => {
    const isExpanded = expandedFiles.has(file.filename);
    const totalLines = file.hunks.reduce((sum, hunk) => sum + hunk.lines.length, 0);
    const shouldTruncate = !isExpanded && totalLines > maxLines;

    return (
      <Box key={file.filename} flexDirection="column" marginBottom={2}>
        {renderFileHeader(file)}

        {/* Binary file notice */}
        {file.isBinary && (
          <Box marginLeft={2}>
            <Text color={theme.colors.warning}>
              Binary file - cannot show diff
            </Text>
          </Box>
        )}

        {/* Hunks */}
        {!file.isBinary && (
          <Box flexDirection="column" marginLeft={2}>
            {file.hunks.map((hunk, hunkIndex) => {
              if (shouldTruncate && hunkIndex > 0) {
                return null;
              }
              return renderHunk(hunk, index, hunkIndex);
            })}

            {/* Truncation notice */}
            {shouldTruncate && file.hunks.length > 1 && (
              <Box>
                <Text color={theme.colors.muted}>
                  ... {file.hunks.length - 1} more hunks (Press Enter to expand)
                </Text>
              </Box>
            )}
          </Box>
        )}
      </Box>
    );
  };

  if (files.length === 0) {
    return (
      <Box>
        <Text color={theme.colors.muted}>No changes to display</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {/* Summary */}
      {files.length > 1 && (
        <Box marginBottom={1}>
          <Text color={theme.colors.primary}>
            📊 {files.length} files changed
          </Text>
          {showStats && (
            <Text color={theme.colors.muted}>
              {' '}(
              {files.reduce((sum, f) => sum + (f.stats?.additions || 0), 0)} additions, 
              {files.reduce((sum, f) => sum + (f.stats?.deletions || 0), 0)} deletions)
            </Text>
          )}
        </Box>
      )}

      {/* Files */}
      {files.map((file, index) => renderFile(file, index))}

      {/* Navigation hint */}
      {allowCollapse && (
        <Box marginTop={1}>
          <Text color={theme.colors.muted}>
            Use Enter to expand/collapse files
          </Text>
        </Box>
      )}
    </Box>
  );
};
