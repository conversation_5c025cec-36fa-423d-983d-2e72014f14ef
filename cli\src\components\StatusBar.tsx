// Status bar component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../hooks/useTheme.js';
import { LoadingSpinner } from './LoadingSpinner.js';

export interface StatusBarProps {
  isLoading?: boolean;
  error?: Error | null;
  messageCount?: number;
  provider?: string;
  model?: string;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  isLoading = false,
  error = null,
  messageCount = 0,
  provider = 'deepseek',
  model = 'deepseek-chat'
}) => {
  const theme = useTheme();

  const getStatusColor = () => {
    if (error) return theme.colors.error;
    if (isLoading) return theme.colors.warning;
    return theme.colors.success;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isLoading) return 'Processing';
    return 'Ready';
  };

  const getStatusIcon = () => {
    if (error) return '✗';
    if (isLoading) return '⟳';
    return '✓';
  };

  return (
    <Box
      borderStyle="single"
      borderColor={theme.colors.border}
      paddingX={1}
      paddingY={0}
    >
      {/* Status indicator */}
      <Box>
        {isLoading ? (
          <LoadingSpinner size="small" />
        ) : (
          <Text color={getStatusColor()}>
            {getStatusIcon()}
          </Text>
        )}
        <Text color={getStatusColor()} marginLeft={1}>
          {getStatusText()}
        </Text>
      </Box>

      {/* Message count */}
      <Box marginLeft={2}>
        <Text color={theme.colors.muted}>
          Messages: {messageCount}
        </Text>
      </Box>

      {/* Provider and model info */}
      <Box marginLeft="auto">
        <Text color={theme.colors.muted}>
          {provider}/{model}
        </Text>
      </Box>

      {/* Error details */}
      {error && (
        <Box marginLeft={2}>
          <Text color={theme.colors.error}>
            {error.message.length > 50 
              ? error.message.substring(0, 50) + '...'
              : error.message
            }
          </Text>
        </Box>
      )}
    </Box>
  );
};
