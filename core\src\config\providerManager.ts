// Provider management system
import { Provider, PROVIDERS, Model, getModelsByProvider, getDefaultModelForProvider } from './models.js';
import { AuthMethod, AuthValidator, AuthManager } from './auth.js';
import { getCredentialStore } from './credentialStore.js';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface ProviderConfig {
  provider: Provider;
  authMethod: string;
  defaultModel?: string;
  baseUrl?: string;
  timeout?: number;
  maxRetries?: number;
  rateLimit?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
  features?: {
    streaming: boolean;
    functionCalling: boolean;
    imageGeneration: boolean;
    embeddings: boolean;
  };
  metadata?: Record<string, any>;
}

export interface ProviderStatus {
  provider: Provider;
  available: boolean;
  authenticated: boolean;
  lastChecked: Date;
  error?: string;
  capabilities: string[];
  models: Model[];
  usage?: {
    requestsToday: number;
    tokensToday: number;
    lastRequest: Date;
  };
}

export class ProviderManager {
  private authManager: AuthManager;
  private providerConfigs = new Map<Provider, ProviderConfig>();
  private providerStatus = new Map<Provider, ProviderStatus>();

  constructor(authManager: AuthManager) {
    this.authManager = authManager;
  }

  public async configureProvider(
    provider: Provider,
    credentials: Record<string, string>,
    options: {
      authType?: 'api_key' | 'oauth' | 'service_account';
      defaultModel?: string;
      baseUrl?: string;
      timeout?: number;
      maxRetries?: number;
    } = {}
  ): Promise<void> {
    try {
      const authType = options.authType || 'api_key';
      
      // Create auth method
      const authMethod = {
        type: authType,
        provider,
        credentials,
        metadata: {
          baseUrl: options.baseUrl,
          configuredAt: new Date().toISOString()
        }
      } as AuthMethod;

      // Validate authentication
      const validation = await AuthValidator.validateAuth(authMethod);
      if (!validation.valid) {
        throw new ArienError(
          ErrorCode.AUTHENTICATION_ERROR,
          `Provider authentication failed: ${validation.errors.join(', ')}`,
          { provider, errors: validation.errors }
        );
      }

      // Store credentials securely
      const credentialStore = getCredentialStore();
      const credentialId = `${provider}_${authType}`;
      await credentialStore.storeCredential(
        credentialId,
        provider,
        authType,
        credentials,
        { baseUrl: options.baseUrl }
      );

      // Add to auth manager
      await this.authManager.addAuthMethod(credentialId, authMethod);

      // Create provider configuration
      const config: ProviderConfig = {
        provider,
        authMethod: credentialId,
        defaultModel: options.defaultModel || getDefaultModelForProvider(provider)?.id,
        baseUrl: options.baseUrl,
        timeout: options.timeout || 30000,
        maxRetries: options.maxRetries || 3,
        rateLimit: this.getDefaultRateLimit(provider),
        features: this.getProviderFeatures(provider),
        metadata: {
          configuredAt: new Date(),
          version: '1.0.0'
        }
      };

      this.providerConfigs.set(provider, config);

      // Update provider status
      await this.updateProviderStatus(provider);

      logger.info('Provider configured successfully', {
        provider,
        authType,
        defaultModel: config.defaultModel
      });

    } catch (error) {
      logger.error('Failed to configure provider', { provider, error });
      throw error;
    }
  }

  public async removeProvider(provider: Provider): Promise<void> {
    try {
      const config = this.providerConfigs.get(provider);
      if (!config) {
        throw new ArienError(
          ErrorCode.CONFIGURATION_ERROR,
          `Provider ${provider} is not configured`
        );
      }

      // Remove from auth manager
      this.authManager.removeAuthMethod(config.authMethod);

      // Remove credentials
      const credentialStore = getCredentialStore();
      await credentialStore.deleteCredential(config.authMethod);

      // Remove configuration
      this.providerConfigs.delete(provider);
      this.providerStatus.delete(provider);

      logger.info('Provider removed successfully', { provider });

    } catch (error) {
      logger.error('Failed to remove provider', { provider, error });
      throw error;
    }
  }

  public async getProviderCredentials(provider: Provider): Promise<Record<string, string> | null> {
    try {
      const config = this.providerConfigs.get(provider);
      if (!config) {
        return null;
      }

      const credentialStore = getCredentialStore();
      return await credentialStore.retrieveCredential(config.authMethod);

    } catch (error) {
      logger.error('Failed to get provider credentials', { provider, error });
      return null;
    }
  }

  public getProviderConfig(provider: Provider): ProviderConfig | null {
    return this.providerConfigs.get(provider) || null;
  }

  public getProviderStatus(provider: Provider): ProviderStatus | null {
    return this.providerStatus.get(provider) || null;
  }

  public getConfiguredProviders(): Provider[] {
    return Array.from(this.providerConfigs.keys());
  }

  public async updateProviderStatus(provider: Provider): Promise<void> {
    try {
      const config = this.providerConfigs.get(provider);
      if (!config) {
        return;
      }

      const credentials = await this.getProviderCredentials(provider);
      if (!credentials) {
        this.providerStatus.set(provider, {
          provider,
          available: false,
          authenticated: false,
          lastChecked: new Date(),
          error: 'No credentials available',
          capabilities: [],
          models: []
        });
        return;
      }

      // Test provider availability
      const isAvailable = await this.testProviderConnection(provider, credentials);
      const models = isAvailable ? getModelsByProvider(provider) : [];
      const capabilities = isAvailable ? this.getProviderCapabilities(provider) : [];

      this.providerStatus.set(provider, {
        provider,
        available: isAvailable,
        authenticated: isAvailable,
        lastChecked: new Date(),
        capabilities,
        models,
        usage: this.getProviderUsage(provider)
      });

      logger.debug('Provider status updated', {
        provider,
        available: isAvailable,
        modelCount: models.length
      });

    } catch (error) {
      logger.error('Failed to update provider status', { provider, error });
      
      this.providerStatus.set(provider, {
        provider,
        available: false,
        authenticated: false,
        lastChecked: new Date(),
        error: error instanceof Error ? error.message : String(error),
        capabilities: [],
        models: []
      });
    }
  }

  public async updateAllProviderStatus(): Promise<void> {
    const providers = this.getConfiguredProviders();
    const updatePromises = providers.map(provider => this.updateProviderStatus(provider));
    await Promise.allSettled(updatePromises);
  }

  public async switchProvider(provider: Provider): Promise<void> {
    const config = this.providerConfigs.get(provider);
    if (!config) {
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        `Provider ${provider} is not configured`
      );
    }

    const status = await this.getProviderStatus(provider);
    if (!status?.available) {
      throw new ArienError(
        ErrorCode.PROVIDER_ERROR,
        `Provider ${provider} is not available`
      );
    }

    // This would update the global configuration to use this provider
    logger.info('Switched to provider', { provider });
  }

  public async testProviderConnection(provider: Provider, credentials: Record<string, string>): Promise<boolean> {
    try {
      // This would make actual API calls to test the connection
      // For now, we'll do basic validation
      switch (provider) {
        case PROVIDERS.DEEPSEEK:
          return !!(credentials.apiKey && credentials.apiKey.length > 20);
        
        case PROVIDERS.OPENAI:
          return !!(credentials.apiKey && credentials.apiKey.startsWith('sk-'));
        
        case PROVIDERS.ANTHROPIC:
          return !!(credentials.apiKey && credentials.apiKey.startsWith('sk-ant-'));
        
        case PROVIDERS.GOOGLE:
          return !!(credentials.apiKey && credentials.apiKey.length > 30);
        
        default:
          return false;
      }
    } catch (error) {
      logger.debug('Provider connection test failed', { provider, error });
      return false;
    }
  }

  public getProviderCapabilities(provider: Provider): string[] {
    const capabilities: string[] = [];

    switch (provider) {
      case PROVIDERS.OPENAI:
        capabilities.push('chat', 'completions', 'embeddings', 'images', 'audio');
        break;
      case PROVIDERS.ANTHROPIC:
        capabilities.push('chat', 'completions', 'function_calling');
        break;
      case PROVIDERS.DEEPSEEK:
        capabilities.push('chat', 'completions', 'function_calling');
        break;
      case PROVIDERS.GOOGLE:
        capabilities.push('chat', 'completions', 'embeddings', 'multimodal');
        break;
    }

    return capabilities;
  }

  public getProviderFeatures(provider: Provider): ProviderConfig['features'] {
    switch (provider) {
      case PROVIDERS.OPENAI:
        return {
          streaming: true,
          functionCalling: true,
          imageGeneration: true,
          embeddings: true
        };
      case PROVIDERS.ANTHROPIC:
        return {
          streaming: true,
          functionCalling: true,
          imageGeneration: false,
          embeddings: false
        };
      case PROVIDERS.DEEPSEEK:
        return {
          streaming: true,
          functionCalling: true,
          imageGeneration: false,
          embeddings: false
        };
      case PROVIDERS.GOOGLE:
        return {
          streaming: true,
          functionCalling: true,
          imageGeneration: false,
          embeddings: true
        };
      default:
        return {
          streaming: false,
          functionCalling: false,
          imageGeneration: false,
          embeddings: false
        };
    }
  }

  private getDefaultRateLimit(provider: Provider): ProviderConfig['rateLimit'] {
    switch (provider) {
      case PROVIDERS.OPENAI:
        return { requestsPerMinute: 3000, tokensPerMinute: 90000 };
      case PROVIDERS.ANTHROPIC:
        return { requestsPerMinute: 1000, tokensPerMinute: 40000 };
      case PROVIDERS.DEEPSEEK:
        return { requestsPerMinute: 1000, tokensPerMinute: 50000 };
      case PROVIDERS.GOOGLE:
        return { requestsPerMinute: 1500, tokensPerMinute: 60000 };
      default:
        return { requestsPerMinute: 100, tokensPerMinute: 10000 };
    }
  }

  private getProviderUsage(provider: Provider): ProviderStatus['usage'] {
    // This would track actual usage statistics
    // For now, return placeholder data
    return {
      requestsToday: 0,
      tokensToday: 0,
      lastRequest: new Date()
    };
  }

  public async exportConfiguration(): Promise<{
    providers: Array<{
      provider: Provider;
      authType: string;
      defaultModel?: string;
      baseUrl?: string;
      configuredAt: Date;
    }>;
    exportedAt: Date;
  }> {
    const providers = [];

    for (const [provider, config] of this.providerConfigs) {
      providers.push({
        provider,
        authType: config.authMethod.split('_').pop() || 'api_key',
        defaultModel: config.defaultModel,
        baseUrl: config.baseUrl,
        configuredAt: config.metadata?.configuredAt || new Date()
      });
    }

    return {
      providers,
      exportedAt: new Date()
    };
  }

  public async importConfiguration(configData: {
    providers: Array<{
      provider: Provider;
      credentials: Record<string, string>;
      authType?: string;
      defaultModel?: string;
      baseUrl?: string;
    }>;
  }): Promise<void> {
    for (const providerData of configData.providers) {
      try {
        await this.configureProvider(
          providerData.provider,
          providerData.credentials,
          {
            authType: providerData.authType as any,
            defaultModel: providerData.defaultModel,
            baseUrl: providerData.baseUrl
          }
        );
      } catch (error) {
        logger.error('Failed to import provider configuration', {
          provider: providerData.provider,
          error
        });
      }
    }
  }
}

// Global provider manager instance
let globalProviderManager: ProviderManager | null = null;

export function getProviderManager(): ProviderManager {
  if (!globalProviderManager) {
    const authManager = new AuthManager();
    globalProviderManager = new ProviderManager(authManager);
  }
  return globalProviderManager;
}

export function initProviderManager(authManager: AuthManager): ProviderManager {
  globalProviderManager = new ProviderManager(authManager);
  return globalProviderManager;
}
