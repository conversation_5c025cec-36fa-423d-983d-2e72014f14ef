// Application header component
import React from 'react';
import { Box, Text } from 'ink';
import { getConfig } from '@arien/core';

export const Header: React.FC = () => {
  const config = getConfig();
  const currentConfig = config.getConfig();
  const currentModel = config.getDefaultModel();

  return (
    <Box borderStyle="round" borderColor="blue" padding={1} marginBottom={1}>
      <Box flexDirection="column" width="100%">
        <Box justifyContent="space-between">
          <Text bold color="blue">
            🤖 Arien CLI
          </Text>
          <Text dimColor>
            {currentConfig.defaultProvider} • {currentModel}
          </Text>
        </Box>
        
        <Box marginTop={1}>
          <Text dimColor>
            Type your message and press Enter. Use Ctrl+C to exit.
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
