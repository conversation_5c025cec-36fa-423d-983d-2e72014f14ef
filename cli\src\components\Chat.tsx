// Main chat interface component
import React, { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import { ArienChat, ChatMessage } from '@arien/core';
import { MessageList } from './MessageList.js';
import { InputBox } from './InputBox.js';
import { StatusBar } from './StatusBar.js';
import { ToolApprovalDialog } from './ToolApprovalDialog.js';
import { useChat } from '../hooks/useChat.js';
import { useTheme } from '../hooks/useTheme.js';

export interface ChatProps {
  initialMessages?: ChatMessage[];
  onExit?: () => void;
}

export const Chat: React.FC<ChatProps> = ({ initialMessages = [], onExit }) => {
  const { exit } = useApp();
  const theme = useTheme();
  const {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    abortCurrentRequest,
    pendingApproval,
    approveAction,
    denyAction
  } = useChat(initialMessages);

  const [inputValue, setInputValue] = useState('');
  const [showHelp, setShowHelp] = useState(false);
  const messagesEndRef = useRef<any>();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView?.();
    }
  }, [messages]);

  // Handle keyboard shortcuts
  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      if (isLoading) {
        abortCurrentRequest();
      } else {
        onExit?.();
        exit();
      }
      return;
    }

    if (key.ctrl && input === 'l') {
      clearMessages();
      return;
    }

    if (key.ctrl && input === 'h') {
      setShowHelp(!showHelp);
      return;
    }

    if (key.escape) {
      if (pendingApproval) {
        denyAction();
      } else if (showHelp) {
        setShowHelp(false);
      }
      return;
    }
  });

  const handleSendMessage = async (message: string) => {
    if (message.trim() && !isLoading) {
      setInputValue('');
      await sendMessage(message);
    }
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
  };

  if (showHelp) {
    return (
      <Box flexDirection="column" height="100%">
        <Box borderStyle="round" borderColor={theme.colors.border} padding={1}>
          <Box flexDirection="column">
            <Text color={theme.colors.primary} bold>
              Arien CLI Help
            </Text>
            <Text> </Text>
            <Text color={theme.colors.text}>Keyboard Shortcuts:</Text>
            <Text color={theme.colors.muted}>  Ctrl+C  - Exit (or abort current request)</Text>
            <Text color={theme.colors.muted}>  Ctrl+L  - Clear chat history</Text>
            <Text color={theme.colors.muted}>  Ctrl+H  - Toggle this help</Text>
            <Text color={theme.colors.muted}>  Escape  - Close dialogs/help</Text>
            <Text> </Text>
            <Text color={theme.colors.text}>Commands:</Text>
            <Text color={theme.colors.muted}>  /help     - Show this help</Text>
            <Text color={theme.colors.muted}>  /clear    - Clear chat history</Text>
            <Text color={theme.colors.muted}>  /status   - Show system status</Text>
            <Text color={theme.colors.muted}>  /config   - Show configuration</Text>
            <Text color={theme.colors.muted}>  /exit     - Exit the application</Text>
            <Text> </Text>
            <Text color={theme.colors.success}>Press Ctrl+H or Escape to close this help</Text>
          </Box>
        </Box>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Box borderStyle="round" borderColor={theme.colors.border} paddingX={2} paddingY={0}>
        <Text color={theme.colors.primary} bold>
          Arien AI Assistant
        </Text>
        <Box marginLeft="auto">
          <Text color={theme.colors.muted}>
            Ctrl+H for help
          </Text>
        </Box>
      </Box>

      {/* Messages area */}
      <Box flexGrow={1} flexDirection="column" paddingX={1}>
        <MessageList 
          messages={messages} 
          isLoading={isLoading}
          error={error}
        />
        <Box ref={messagesEndRef} />
      </Box>

      {/* Tool approval dialog */}
      {pendingApproval && (
        <ToolApprovalDialog
          request={pendingApproval}
          onApprove={approveAction}
          onDeny={denyAction}
        />
      )}

      {/* Input area */}
      <Box flexDirection="column">
        <StatusBar 
          isLoading={isLoading}
          error={error}
          messageCount={messages.length}
        />
        <InputBox
          value={inputValue}
          onChange={handleInputChange}
          onSubmit={handleSendMessage}
          disabled={isLoading || !!pendingApproval}
          placeholder={
            pendingApproval 
              ? "Waiting for approval..." 
              : isLoading 
                ? "AI is thinking..." 
                : "Type your message..."
          }
        />
      </Box>
    </Box>
  );
};
