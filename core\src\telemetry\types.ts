// Telemetry type definitions
export interface TelemetryEvent {
  name: string;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  properties?: Record<string, any>;
  metrics?: Record<string, number>;
  context?: TelemetryContext;
}

export interface TelemetryContext {
  version: string;
  platform: string;
  arch: string;
  nodeVersion: string;
  provider?: string;
  model?: string;
  approvalLevel?: string;
  sandboxEnabled?: boolean;
  extensionsCount?: number;
}

export interface TelemetryMetric {
  name: string;
  value: number;
  unit?: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

export interface TelemetryConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  batchSize: number;
  flushInterval: number;
  maxRetries: number;
  timeout: number;
  debug: boolean;
  anonymize: boolean;
  collectSystemInfo: boolean;
  collectUsageStats: boolean;
  collectErrorReports: boolean;
  collectPerformanceMetrics: boolean;
}

export interface TelemetryBatch {
  events: TelemetryEvent[];
  metrics: TelemetryMetric[];
  timestamp: Date;
  batchId: string;
}

export interface TelemetryProvider {
  name: string;
  send: (batch: TelemetryBatch) => Promise<void>;
  configure: (config: Partial<TelemetryConfig>) => void;
  flush: () => Promise<void>;
  close: () => Promise<void>;
}

export interface PerformanceMetrics {
  responseTime: number;
  tokenCount: number;
  requestSize: number;
  responseSize: number;
  errorCount: number;
  retryCount: number;
  cacheHitRate: number;
  memoryUsage: number;
  cpuUsage: number;
}

export interface UsageStats {
  commandsExecuted: number;
  toolsUsed: Record<string, number>;
  providersUsed: Record<string, number>;
  modelsUsed: Record<string, number>;
  filesAccessed: number;
  networkRequests: number;
  errorsEncountered: number;
  sessionDuration: number;
  featuresUsed: string[];
}

export interface ErrorReport {
  errorType: string;
  errorMessage: string;
  errorCode?: string;
  stackTrace?: string;
  context: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
  version: string;
  platform: string;
  reproducible?: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SystemInfo {
  platform: string;
  arch: string;
  nodeVersion: string;
  arienVersion: string;
  totalMemory: number;
  freeMemory: number;
  cpuCount: number;
  uptime: number;
  loadAverage?: number[];
  diskSpace?: {
    total: number;
    free: number;
    used: number;
  };
}

export type TelemetryEventName = 
  | 'app_started'
  | 'app_stopped'
  | 'command_executed'
  | 'tool_used'
  | 'provider_switched'
  | 'model_changed'
  | 'file_accessed'
  | 'network_request'
  | 'error_occurred'
  | 'approval_requested'
  | 'approval_granted'
  | 'approval_denied'
  | 'extension_loaded'
  | 'extension_unloaded'
  | 'config_changed'
  | 'theme_changed'
  | 'sandbox_violation'
  | 'performance_issue'
  | 'memory_warning'
  | 'rate_limit_hit'
  | 'authentication_failed'
  | 'session_started'
  | 'session_ended';

export type TelemetryMetricName =
  | 'response_time'
  | 'token_count'
  | 'request_size'
  | 'response_size'
  | 'error_count'
  | 'retry_count'
  | 'cache_hit_rate'
  | 'memory_usage'
  | 'cpu_usage'
  | 'disk_usage'
  | 'network_latency'
  | 'commands_per_minute'
  | 'tools_per_session'
  | 'files_per_session'
  | 'session_duration'
  | 'approval_rate'
  | 'extension_count'
  | 'config_changes'
  | 'sandbox_violations'
  | 'authentication_attempts';

export interface TelemetryFilter {
  eventNames?: TelemetryEventName[];
  metricNames?: TelemetryMetricName[];
  userId?: string;
  sessionId?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  properties?: Record<string, any>;
  severity?: ErrorReport['severity'][];
}

export interface TelemetryQuery {
  filter?: TelemetryFilter;
  aggregation?: {
    type: 'count' | 'sum' | 'avg' | 'min' | 'max';
    field?: string;
    groupBy?: string[];
  };
  limit?: number;
  offset?: number;
  orderBy?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}

export interface TelemetryResult {
  events: TelemetryEvent[];
  metrics: TelemetryMetric[];
  aggregations?: Record<string, number>;
  totalCount: number;
  hasMore: boolean;
}

export interface TelemetryStorage {
  store: (batch: TelemetryBatch) => Promise<void>;
  query: (query: TelemetryQuery) => Promise<TelemetryResult>;
  cleanup: (olderThan: Date) => Promise<number>;
  getStats: () => Promise<{
    totalEvents: number;
    totalMetrics: number;
    oldestEvent: Date;
    newestEvent: Date;
    storageSize: number;
  }>;
}

export interface TelemetryCollector {
  collect: () => Promise<{
    events: TelemetryEvent[];
    metrics: TelemetryMetric[];
  }>;
  start: () => void;
  stop: () => void;
  isRunning: () => boolean;
}

export interface TelemetryProcessor {
  process: (batch: TelemetryBatch) => Promise<TelemetryBatch>;
  configure: (config: any) => void;
}

export interface TelemetryExporter {
  export: (query: TelemetryQuery) => Promise<string>;
  format: 'json' | 'csv' | 'xml' | 'yaml';
}

export interface TelemetryDashboard {
  generateReport: (timeRange: { start: Date; end: Date }) => Promise<{
    summary: {
      totalEvents: number;
      totalMetrics: number;
      errorRate: number;
      averageResponseTime: number;
      topCommands: Array<{ name: string; count: number }>;
      topTools: Array<{ name: string; count: number }>;
      topProviders: Array<{ name: string; count: number }>;
    };
    charts: Array<{
      type: 'line' | 'bar' | 'pie' | 'area';
      title: string;
      data: any[];
    }>;
  }>;
}

export interface TelemetryAlert {
  id: string;
  name: string;
  description: string;
  condition: {
    metric: TelemetryMetricName;
    operator: '>' | '<' | '>=' | '<=' | '==' | '!=';
    threshold: number;
    timeWindow: number; // in seconds
  };
  actions: Array<{
    type: 'email' | 'webhook' | 'log';
    config: Record<string, any>;
  }>;
  enabled: boolean;
  lastTriggered?: Date;
}

export interface TelemetryAnomalyDetection {
  detect: (metrics: TelemetryMetric[]) => Promise<Array<{
    metric: TelemetryMetric;
    anomalyScore: number;
    expectedValue: number;
    deviation: number;
    timestamp: Date;
  }>>;
  train: (historicalData: TelemetryMetric[]) => Promise<void>;
  configure: (config: {
    sensitivity: number;
    windowSize: number;
    algorithm: 'zscore' | 'isolation_forest' | 'moving_average';
  }) => void;
}
