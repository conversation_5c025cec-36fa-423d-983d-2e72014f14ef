// Individual history item renderer component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface HistoryItem {
  id: string;
  timestamp: Date;
  userMessage: string;
  assistantResponse: string;
  metadata?: {
    model?: string;
    provider?: string;
    tokens?: {
      input: number;
      output: number;
      total: number;
    };
    duration?: number;
    toolsUsed?: string[];
    success: boolean;
    error?: string;
  };
  context?: {
    workingDirectory: string;
    gitBranch?: string;
    filesModified?: string[];
  };
}

interface HistoryItemDisplayProps {
  item: HistoryItem;
  isSelected?: boolean;
  isExpanded?: boolean;
  showMetadata?: boolean;
  showContext?: boolean;
  onToggleExpand?: () => void;
  onSelect?: () => void;
  compact?: boolean;
}

export const HistoryItemDisplay: React.FC<HistoryItemDisplayProps> = ({
  item,
  isSelected = false,
  isExpanded = false,
  showMetadata = true,
  showContext = true,
  onToggleExpand,
  onSelect,
  compact = false
}) => {
  const theme = useTheme();

  useInput((input, key) => {
    if (!isSelected) return;

    if (key.return && onToggleExpand) {
      onToggleExpand();
    } else if (input === ' ' && onSelect) {
      onSelect();
    }
  });

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatTokens = (tokens: { input: number; output: number; total: number }) => {
    return `${tokens.input}→${tokens.output} (${tokens.total})`;
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  };

  const getStatusIcon = () => {
    if (item.metadata?.error) return '❌';
    if (item.metadata?.success === false) return '⚠️';
    return '✅';
  };

  const getStatusColor = () => {
    if (item.metadata?.error) return theme.colors.error;
    if (item.metadata?.success === false) return theme.colors.warning;
    return theme.colors.success;
  };

  if (compact) {
    return (
      <Box
        flexDirection="column"
        paddingX={isSelected ? 1 : 0}
        borderStyle={isSelected ? 'single' : undefined}
        borderColor={isSelected ? theme.colors.primary : undefined}
      >
        <Box>
          <Text color={getStatusColor()}>{getStatusIcon()}</Text>
          <Text color={theme.colors.muted}>
            {' '}[{item.timestamp.toLocaleTimeString()}]
          </Text>
          <Text color={theme.colors.user}>
            {' '}{truncateText(item.userMessage, 40)}
          </Text>
        </Box>
        {isExpanded && (
          <Box marginLeft={2} marginTop={0}>
            <Text color={theme.colors.assistant}>
              {truncateText(item.assistantResponse, 60)}
            </Text>
          </Box>
        )}
      </Box>
    );
  }

  return (
    <Box
      flexDirection="column"
      marginBottom={1}
      paddingX={isSelected ? 1 : 0}
      borderStyle={isSelected ? 'single' : undefined}
      borderColor={isSelected ? theme.colors.primary : undefined}
    >
      {/* Header */}
      <Box>
        <Text color={getStatusColor()}>{getStatusIcon()}</Text>
        <Text color={theme.colors.muted}>
          {' '}[{item.timestamp.toLocaleString()}]
        </Text>
        {item.metadata?.model && (
          <Text color={theme.colors.secondary}>
            {' '}({item.metadata.model})
          </Text>
        )}
        {isSelected && onToggleExpand && (
          <Text color={theme.colors.muted}>
            {' '}Press Enter to {isExpanded ? 'collapse' : 'expand'}
          </Text>
        )}
      </Box>

      {/* User Message */}
      <Box marginLeft={2} marginTop={0}>
        <Text color={theme.colors.user}>👤 </Text>
        <Text color={theme.colors.text}>
          {isExpanded ? item.userMessage : truncateText(item.userMessage, 80)}
        </Text>
      </Box>

      {/* Assistant Response */}
      {(isExpanded || !compact) && (
        <Box marginLeft={2} marginTop={0}>
          <Text color={theme.colors.assistant}>🤖 </Text>
          <Text color={theme.colors.text}>
            {isExpanded 
              ? item.assistantResponse 
              : truncateText(item.assistantResponse, 80)}
          </Text>
        </Box>
      )}

      {/* Error */}
      {item.metadata?.error && (
        <Box marginLeft={2} marginTop={0}>
          <Text color={theme.colors.error}>
            ❌ Error: {item.metadata.error}
          </Text>
        </Box>
      )}

      {/* Metadata */}
      {isExpanded && showMetadata && item.metadata && (
        <Box marginLeft={2} marginTop={1} flexDirection="column">
          <Text color={theme.colors.muted}>Metadata:</Text>
          <Box marginLeft={2}>
            {item.metadata.provider && (
              <Text color={theme.colors.muted}>
                Provider: {item.metadata.provider}
              </Text>
            )}
            {item.metadata.tokens && (
              <Text color={theme.colors.muted}>
                {' '}Tokens: {formatTokens(item.metadata.tokens)}
              </Text>
            )}
            {item.metadata.duration && (
              <Text color={theme.colors.muted}>
                {' '}Duration: {formatDuration(item.metadata.duration)}
              </Text>
            )}
          </Box>
          {item.metadata.toolsUsed && item.metadata.toolsUsed.length > 0 && (
            <Box marginLeft={2}>
              <Text color={theme.colors.muted}>
                Tools used: {item.metadata.toolsUsed.join(', ')}
              </Text>
            </Box>
          )}
        </Box>
      )}

      {/* Context */}
      {isExpanded && showContext && item.context && (
        <Box marginLeft={2} marginTop={1} flexDirection="column">
          <Text color={theme.colors.muted}>Context:</Text>
          <Box marginLeft={2}>
            <Text color={theme.colors.muted}>
              Directory: {item.context.workingDirectory}
            </Text>
            {item.context.gitBranch && (
              <Text color={theme.colors.muted}>
                {' '}Branch: {item.context.gitBranch}
              </Text>
            )}
          </Box>
          {item.context.filesModified && item.context.filesModified.length > 0 && (
            <Box marginLeft={2}>
              <Text color={theme.colors.muted}>
                Files modified: {item.context.filesModified.join(', ')}
              </Text>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};
