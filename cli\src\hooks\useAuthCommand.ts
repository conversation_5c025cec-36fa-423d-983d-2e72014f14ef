// Authentication command handling hook
import { useState, useCallback } from 'react';
import { getConfig, Provider, PROVIDERS } from '@arien/core';

export interface AuthProvider {
  id: Provider;
  name: string;
  description: string;
  authMethods: string[];
  isConfigured: boolean;
  lastUsed?: Date;
}

export interface AuthState {
  isAuthenticated: boolean;
  currentProvider?: Provider;
  availableProviders: AuthProvider[];
  isConfiguring: boolean;
  error?: string;
}

export interface UseAuthCommandReturn {
  authState: AuthState;
  startAuthentication: (provider?: Provider) => Promise<void>;
  completeAuthentication: (provider: Provider, credentials: Record<string, string>) => Promise<boolean>;
  testAuthentication: (provider: Provider) => Promise<boolean>;
  removeAuthentication: (provider: Provider) => Promise<void>;
  refreshAuthState: () => Promise<void>;
  getProviderStatus: (provider: Provider) => 'configured' | 'partial' | 'missing';
}

export function useAuthCommand(): UseAuthCommandReturn {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    availableProviders: [],
    isConfiguring: false
  });

  const getProviderInfo = useCallback((provider: Provider): AuthProvider => {
    const config = getConfig();
    const providerConfig = config.get(`providers.${provider}`) || {};
    
    const providerDetails = {
      [PROVIDERS.DEEPSEEK]: {
        name: 'DeepSeek',
        description: 'DeepSeek AI models with competitive pricing',
        authMethods: ['api_key']
      },
      [PROVIDERS.OPENAI]: {
        name: 'OpenAI',
        description: 'GPT models from OpenAI',
        authMethods: ['api_key']
      },
      [PROVIDERS.ANTHROPIC]: {
        name: 'Anthropic',
        description: 'Claude models from Anthropic',
        authMethods: ['api_key']
      },
      [PROVIDERS.GOOGLE]: {
        name: 'Google',
        description: 'Gemini models from Google',
        authMethods: ['api_key', 'oauth']
      }
    };

    const details = providerDetails[provider];
    
    return {
      id: provider,
      name: details.name,
      description: details.description,
      authMethods: details.authMethods,
      isConfigured: !!providerConfig.apiKey,
      lastUsed: providerConfig.lastUsed ? new Date(providerConfig.lastUsed) : undefined
    };
  }, []);

  const refreshAuthState = useCallback(async () => {
    try {
      const config = getConfig();
      const validation = config.validateConfig();
      
      const availableProviders = Object.values(PROVIDERS).map(provider => 
        getProviderInfo(provider)
      );

      const currentProvider = config.get('defaultProvider');
      const isAuthenticated = validation.valid && availableProviders.some(p => p.isConfigured);

      setAuthState({
        isAuthenticated,
        currentProvider,
        availableProviders,
        isConfiguring: false
      });
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error),
        isConfiguring: false
      }));
    }
  }, [getProviderInfo]);

  const startAuthentication = useCallback(async (provider?: Provider) => {
    setAuthState(prev => ({
      ...prev,
      isConfiguring: true,
      error: undefined
    }));

    await refreshAuthState();
  }, [refreshAuthState]);

  const completeAuthentication = useCallback(async (
    provider: Provider,
    credentials: Record<string, string>
  ): Promise<boolean> => {
    try {
      const config = getConfig();
      
      // Store credentials
      const providerConfig = {
        apiKey: credentials.apiKey,
        baseUrl: credentials.baseUrl,
        lastUsed: new Date().toISOString()
      };

      config.set(`providers.${provider}`, providerConfig);
      
      // Set as default if it's the first configured provider
      const currentDefault = config.get('defaultProvider');
      if (!currentDefault || !config.get(`providers.${currentDefault}.apiKey`)) {
        config.set('defaultProvider', provider);
      }

      // Test the authentication
      const isValid = await testAuthentication(provider);
      
      if (isValid) {
        await refreshAuthState();
        return true;
      } else {
        // Remove invalid credentials
        config.set(`providers.${provider}`, {});
        setAuthState(prev => ({
          ...prev,
          error: 'Authentication test failed. Please check your credentials.',
          isConfiguring: false
        }));
        return false;
      }
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error),
        isConfiguring: false
      }));
      return false;
    }
  }, [refreshAuthState]);

  const testAuthentication = useCallback(async (provider: Provider): Promise<boolean> => {
    try {
      const config = getConfig();
      const providerConfig = config.get(`providers.${provider}`);
      
      if (!providerConfig?.apiKey) {
        return false;
      }

      // Simple test - try to create a client and make a basic request
      // In a real implementation, you would make an actual API call
      const { ArienClient } = await import('@arien/core');
      const client = new ArienClient({ provider });
      
      // For now, just check if we can create the client
      // In production, you'd want to make a test API call
      return true;
    } catch (error) {
      console.error(`Authentication test failed for ${provider}:`, error);
      return false;
    }
  }, []);

  const removeAuthentication = useCallback(async (provider: Provider) => {
    try {
      const config = getConfig();
      config.set(`providers.${provider}`, {});
      
      // If this was the default provider, clear it
      const currentDefault = config.get('defaultProvider');
      if (currentDefault === provider) {
        // Find another configured provider to set as default
        const otherProviders = Object.values(PROVIDERS).filter(p => p !== provider);
        const newDefault = otherProviders.find(p => 
          config.get(`providers.${p}.apiKey`)
        );
        
        if (newDefault) {
          config.set('defaultProvider', newDefault);
        } else {
          config.set('defaultProvider', PROVIDERS.DEEPSEEK);
        }
      }

      await refreshAuthState();
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : String(error)
      }));
    }
  }, [refreshAuthState]);

  const getProviderStatus = useCallback((provider: Provider): 'configured' | 'partial' | 'missing' => {
    const config = getConfig();
    const providerConfig = config.get(`providers.${provider}`) || {};
    
    if (providerConfig.apiKey) {
      return 'configured';
    } else if (Object.keys(providerConfig).length > 0) {
      return 'partial';
    } else {
      return 'missing';
    }
  }, []);

  return {
    authState,
    startAuthentication,
    completeAuthentication,
    testAuthentication,
    removeAuthentication,
    refreshAuthState,
    getProviderStatus
  };
}
