// Chat hook for managing conversation state
import { useState, useCallback, useRef } from 'react';
import { ArienChat, ChatMessage, ToolApprovalRequest } from '@arien/core';
import { useConfig } from './useConfig.js';

export interface UseChatOptions {
  autoSave?: boolean;
  maxMessages?: number;
}

export interface UseChatReturn {
  messages: ChatMessage[];
  isLoading: boolean;
  error: Error | null;
  sendMessage: (message: string) => Promise<void>;
  clearMessages: () => void;
  abortCurrentRequest: () => void;
  pendingApproval: ToolApprovalRequest | null;
  approveAction: () => void;
  denyAction: () => void;
}

export function useChat(
  initialMessages: ChatMessage[] = [],
  options: UseChatOptions = {}
): UseChatReturn {
  const { autoSave = true, maxMessages = 1000 } = options;
  const config = useConfig();
  
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [pendingApproval, setPendingApproval] = useState<ToolApprovalRequest | null>(null);
  
  const chatRef = useRef<ArienChat | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const approvalResolverRef = useRef<{
    resolve: (approved: boolean) => void;
    reject: (error: Error) => void;
  } | null>(null);

  // Initialize chat instance
  const getChat = useCallback(async (): Promise<ArienChat> => {
    if (!chatRef.current) {
      chatRef.current = new ArienChat({
        provider: config.defaultProvider,
        model: config.defaultModel,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        systemPrompt: config.systemPrompt,
        toolApprovalHandler: async (request: ToolApprovalRequest) => {
          return new Promise((resolve, reject) => {
            setPendingApproval(request);
            approvalResolverRef.current = { resolve, reject };
          });
        }
      });
    }
    return chatRef.current;
  }, [config]);

  const sendMessage = useCallback(async (message: string) => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      // Handle commands
      if (message.startsWith('/')) {
        await handleCommand(message);
        return;
      }

      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: message,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);

      // Create abort controller
      abortControllerRef.current = new AbortController();

      // Get chat instance and send message
      const chat = await getChat();
      const response = await chat.sendMessage(message, {
        signal: abortControllerRef.current.signal
      });

      // Add assistant response
      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        toolCalls: response.toolCalls,
        toolResults: response.toolResults
      };

      setMessages(prev => {
        const newMessages = [...prev, assistantMessage];
        
        // Limit message history
        if (newMessages.length > maxMessages) {
          return newMessages.slice(-maxMessages);
        }
        
        return newMessages;
      });

      // Auto-save if enabled
      if (autoSave) {
        // TODO: Implement message persistence
      }

    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        // Request was aborted, don't show error
        return;
      }
      
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      // Add error message to chat
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: `Error: ${error.message}`,
        timestamp: new Date(),
        error: error.message
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [isLoading, getChat, maxMessages, autoSave]);

  const handleCommand = useCallback(async (command: string) => {
    const [cmd, ...args] = command.slice(1).split(' ');
    
    switch (cmd.toLowerCase()) {
      case 'help':
        const helpMessage: ChatMessage = {
          role: 'system',
          content: `Available commands:
/help - Show this help
/clear - Clear chat history
/status - Show system status
/config - Show configuration
/exit - Exit the application`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, helpMessage]);
        break;
        
      case 'clear':
        clearMessages();
        break;
        
      case 'status':
        const statusMessage: ChatMessage = {
          role: 'system',
          content: `System Status:
Provider: ${config.defaultProvider}
Model: ${config.defaultModel}
Messages: ${messages.length}
Loading: ${isLoading}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, statusMessage]);
        break;
        
      case 'config':
        const configMessage: ChatMessage = {
          role: 'system',
          content: `Configuration:
${JSON.stringify(config, null, 2)}`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, configMessage]);
        break;
        
      case 'exit':
        process.exit(0);
        break;
        
      default:
        const unknownMessage: ChatMessage = {
          role: 'system',
          content: `Unknown command: ${cmd}. Type /help for available commands.`,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, unknownMessage]);
    }
  }, [config, messages.length, isLoading]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    setError(null);
    
    // Reset chat instance to clear context
    if (chatRef.current) {
      chatRef.current = null;
    }
  }, []);

  const abortCurrentRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsLoading(false);
  }, []);

  const approveAction = useCallback(() => {
    if (approvalResolverRef.current) {
      approvalResolverRef.current.resolve(true);
      approvalResolverRef.current = null;
    }
    setPendingApproval(null);
  }, []);

  const denyAction = useCallback(() => {
    if (approvalResolverRef.current) {
      approvalResolverRef.current.resolve(false);
      approvalResolverRef.current = null;
    }
    setPendingApproval(null);
  }, []);

  return {
    messages,
    isLoading,
    error,
    sendMessage,
    clearMessages,
    abortCurrentRequest,
    pendingApproval,
    approveAction,
    denyAction
  };
}
