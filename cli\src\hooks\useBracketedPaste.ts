// Bracketed paste mode handling hook
import { useState, useEffect, useCallback, useRef } from 'react';
import { useStdin } from 'ink';

export interface PasteEvent {
  content: string;
  timestamp: Date;
  lineCount: number;
  charCount: number;
  containsCode: boolean;
  language?: string;
}

export interface UseBracketedPasteReturn {
  isPasteMode: boolean;
  lastPaste: PasteEvent | null;
  pasteBuffer: string;
  enableBracketedPaste: () => void;
  disableBracketedPaste: () => void;
  clearPasteBuffer: () => void;
  onPaste?: (event: PasteEvent) => void;
}

export function useBracketedPaste(
  onPaste?: (event: PasteEvent) => void
): UseBracketedPasteReturn {
  const { stdin } = useStdin();
  const [isPasteMode, setIsPasteMode] = useState(false);
  const [lastPaste, setLastPaste] = useState<PasteEvent | null>(null);
  const [pasteBuffer, setPasteBuffer] = useState('');
  const [isEnabled, setIsEnabled] = useState(false);
  
  const pasteBufferRef = useRef('');
  const isPasteModeRef = useRef(false);

  // Detect if content looks like code
  const detectCodeContent = useCallback((content: string): { containsCode: boolean; language?: string } => {
    const codeIndicators = [
      // Common programming patterns
      /function\s+\w+\s*\(/,
      /class\s+\w+/,
      /import\s+.*from/,
      /export\s+(default\s+)?/,
      /const\s+\w+\s*=/,
      /let\s+\w+\s*=/,
      /var\s+\w+\s*=/,
      /if\s*\(/,
      /for\s*\(/,
      /while\s*\(/,
      /\{\s*$/m,
      /^\s*\/\//m,
      /^\s*\/\*/m,
      /^\s*#/m,
      /^\s*</m, // HTML/XML
      /\$\w+/,   // Variables
      /\w+\(\)/  // Function calls
    ];

    const containsCode = codeIndicators.some(pattern => pattern.test(content));

    // Simple language detection
    let language: string | undefined;
    if (containsCode) {
      if (content.includes('import React') || content.includes('useState')) {
        language = 'javascript';
      } else if (content.includes('def ') || content.includes('import ')) {
        language = 'python';
      } else if (content.includes('package ') || content.includes('public class')) {
        language = 'java';
      } else if (content.includes('#include') || content.includes('int main')) {
        language = 'c';
      } else if (content.includes('fn ') || content.includes('let mut')) {
        language = 'rust';
      } else if (content.includes('<html') || content.includes('<!DOCTYPE')) {
        language = 'html';
      } else if (content.includes('{') && content.includes('}')) {
        language = 'json';
      }
    }

    return { containsCode, language };
  }, []);

  // Handle paste completion
  const completePaste = useCallback(() => {
    const content = pasteBufferRef.current;
    if (content.length === 0) return;

    const { containsCode, language } = detectCodeContent(content);
    
    const pasteEvent: PasteEvent = {
      content,
      timestamp: new Date(),
      lineCount: content.split('\n').length,
      charCount: content.length,
      containsCode,
      language
    };

    setLastPaste(pasteEvent);
    onPaste?.(pasteEvent);
    
    // Clear buffer
    pasteBufferRef.current = '';
    setPasteBuffer('');
    setIsPasteMode(false);
    isPasteModeRef.current = false;
  }, [detectCodeContent, onPaste]);

  // Handle raw input data
  const handleRawData = useCallback((data: Buffer) => {
    if (!isEnabled) return;

    const str = data.toString();
    
    // Check for bracketed paste start sequence
    if (str.includes('\x1b[200~')) {
      setIsPasteMode(true);
      isPasteModeRef.current = true;
      pasteBufferRef.current = '';
      setPasteBuffer('');
      
      // Extract content after the start sequence
      const startIndex = str.indexOf('\x1b[200~') + 6;
      const remaining = str.slice(startIndex);
      
      // Check if end sequence is also in this chunk
      if (remaining.includes('\x1b[201~')) {
        const endIndex = remaining.indexOf('\x1b[201~');
        const content = remaining.slice(0, endIndex);
        pasteBufferRef.current = content;
        setPasteBuffer(content);
        completePaste();
      } else {
        pasteBufferRef.current = remaining;
        setPasteBuffer(remaining);
      }
      return;
    }

    // Check for bracketed paste end sequence
    if (str.includes('\x1b[201~') && isPasteModeRef.current) {
      const endIndex = str.indexOf('\x1b[201~');
      const content = str.slice(0, endIndex);
      pasteBufferRef.current += content;
      setPasteBuffer(pasteBufferRef.current);
      completePaste();
      return;
    }

    // If we're in paste mode, accumulate data
    if (isPasteModeRef.current) {
      pasteBufferRef.current += str;
      setPasteBuffer(pasteBufferRef.current);
    }
  }, [isEnabled, completePaste]);

  const enableBracketedPaste = useCallback(() => {
    if (!stdin) return;
    
    setIsEnabled(true);
    
    // Send escape sequence to enable bracketed paste mode
    process.stdout.write('\x1b[?2004h');
    
    // Set up raw mode to capture escape sequences
    if (stdin.isTTY) {
      stdin.setRawMode(true);
      stdin.on('data', handleRawData);
    }
  }, [stdin, handleRawData]);

  const disableBracketedPaste = useCallback(() => {
    if (!stdin) return;
    
    setIsEnabled(false);
    
    // Send escape sequence to disable bracketed paste mode
    process.stdout.write('\x1b[?2004l');
    
    // Clean up raw mode
    if (stdin.isTTY) {
      stdin.setRawMode(false);
      stdin.off('data', handleRawData);
    }
    
    // Clear any pending paste
    setIsPasteMode(false);
    isPasteModeRef.current = false;
    pasteBufferRef.current = '';
    setPasteBuffer('');
  }, [stdin, handleRawData]);

  const clearPasteBuffer = useCallback(() => {
    pasteBufferRef.current = '';
    setPasteBuffer('');
    setIsPasteMode(false);
    isPasteModeRef.current = false;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disableBracketedPaste();
    };
  }, [disableBracketedPaste]);

  return {
    isPasteMode,
    lastPaste,
    pasteBuffer,
    enableBracketedPaste,
    disableBracketedPaste,
    clearPasteBuffer,
    onPaste
  };
}
