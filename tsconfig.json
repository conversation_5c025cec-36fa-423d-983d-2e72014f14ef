{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022", "DOM"]}, "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "./core"}, {"path": "./cli"}]}