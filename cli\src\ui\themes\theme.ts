// Base theme definitions
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  syntax: {
    keyword: string;
    string: string;
    number: string;
    comment: string;
    function: string;
    variable: string;
    type: string;
    operator: string;
  };
  ui: {
    border: string;
    selection: string;
    cursor: string;
    highlight: string;
    disabled: string;
  };
}

export const defaultTheme: Theme = {
  name: 'default',
  colors: {
    primary: 'blue',
    secondary: 'cyan',
    accent: 'magenta',
    background: 'black',
    foreground: 'white',
    muted: 'gray',
    success: 'green',
    warning: 'yellow',
    error: 'red',
    info: 'blue'
  },
  syntax: {
    keyword: 'blue',
    string: 'green',
    number: 'yellow',
    comment: 'gray',
    function: 'cyan',
    variable: 'white',
    type: 'magenta',
    operator: 'red'
  },
  ui: {
    border: 'gray',
    selection: 'blue',
    cursor: 'white',
    highlight: 'yellow',
    disabled: 'gray'
  }
};

export const draculaTheme: Theme = {
  name: 'dracula',
  colors: {
    primary: '#bd93f9',
    secondary: '#8be9fd',
    accent: '#ff79c6',
    background: '#282a36',
    foreground: '#f8f8f2',
    muted: '#6272a4',
    success: '#50fa7b',
    warning: '#f1fa8c',
    error: '#ff5555',
    info: '#8be9fd'
  },
  syntax: {
    keyword: '#ff79c6',
    string: '#f1fa8c',
    number: '#bd93f9',
    comment: '#6272a4',
    function: '#50fa7b',
    variable: '#f8f8f2',
    type: '#8be9fd',
    operator: '#ff79c6'
  },
  ui: {
    border: '#6272a4',
    selection: '#44475a',
    cursor: '#f8f8f2',
    highlight: '#f1fa8c',
    disabled: '#6272a4'
  }
};

export const githubDarkTheme: Theme = {
  name: 'github-dark',
  colors: {
    primary: '#58a6ff',
    secondary: '#79c0ff',
    accent: '#d2a8ff',
    background: '#0d1117',
    foreground: '#c9d1d9',
    muted: '#8b949e',
    success: '#3fb950',
    warning: '#d29922',
    error: '#f85149',
    info: '#58a6ff'
  },
  syntax: {
    keyword: '#ff7b72',
    string: '#a5d6ff',
    number: '#79c0ff',
    comment: '#8b949e',
    function: '#d2a8ff',
    variable: '#c9d1d9',
    type: '#ffa657',
    operator: '#ff7b72'
  },
  ui: {
    border: '#30363d',
    selection: '#264f78',
    cursor: '#c9d1d9',
    highlight: '#d29922',
    disabled: '#484f58'
  }
};

export const githubLightTheme: Theme = {
  name: 'github-light',
  colors: {
    primary: '#0969da',
    secondary: '#218bff',
    accent: '#8250df',
    background: '#ffffff',
    foreground: '#24292f',
    muted: '#656d76',
    success: '#1a7f37',
    warning: '#9a6700',
    error: '#cf222e',
    info: '#0969da'
  },
  syntax: {
    keyword: '#cf222e',
    string: '#032f62',
    number: '#0550ae',
    comment: '#6e7781',
    function: '#8250df',
    variable: '#24292f',
    type: '#953800',
    operator: '#cf222e'
  },
  ui: {
    border: '#d0d7de',
    selection: '#0969da20',
    cursor: '#24292f',
    highlight: '#fff8c5',
    disabled: '#8c959f'
  }
};

export const ayuTheme: Theme = {
  name: 'ayu',
  colors: {
    primary: '#ffb454',
    secondary: '#73d0ff',
    accent: '#d4bfff',
    background: '#0a0e14',
    foreground: '#b3b1ad',
    muted: '#4d5566',
    success: '#91b362',
    warning: '#e6b450',
    error: '#ea6c73',
    info: '#73d0ff'
  },
  syntax: {
    keyword: '#ff8f40',
    string: '#c2d94c',
    number: '#ffb454',
    comment: '#626a73',
    function: '#ffb454',
    variable: '#b3b1ad',
    type: '#73d0ff',
    operator: '#f29668'
  },
  ui: {
    border: '#1f2430',
    selection: '#253340',
    cursor: '#b3b1ad',
    highlight: '#e6b450',
    disabled: '#4d5566'
  }
};

export const ayuLightTheme: Theme = {
  name: 'ayu-light',
  colors: {
    primary: '#ff9940',
    secondary: '#399ee6',
    accent: '#a37acc',
    background: '#fafafa',
    foreground: '#5c6166',
    muted: '#828c99',
    success: '#86b300',
    warning: '#f2ae49',
    error: '#f51818',
    info: '#399ee6'
  },
  syntax: {
    keyword: '#fa8d3e',
    string: '#86b300',
    number: '#ff9940',
    comment: '#abb0b6',
    function: '#f2ae49',
    variable: '#5c6166',
    type: '#399ee6',
    operator: '#ed9366'
  },
  ui: {
    border: '#e7eaed',
    selection: '#399ee620',
    cursor: '#5c6166',
    highlight: '#f2ae49',
    disabled: '#828c99'
  }
};

export const atomOneDarkTheme: Theme = {
  name: 'atom-one-dark',
  colors: {
    primary: '#61afef',
    secondary: '#56b6c2',
    accent: '#c678dd',
    background: '#282c34',
    foreground: '#abb2bf',
    muted: '#5c6370',
    success: '#98c379',
    warning: '#e5c07b',
    error: '#e06c75',
    info: '#61afef'
  },
  syntax: {
    keyword: '#c678dd',
    string: '#98c379',
    number: '#d19a66',
    comment: '#5c6370',
    function: '#61afef',
    variable: '#abb2bf',
    type: '#e5c07b',
    operator: '#56b6c2'
  },
  ui: {
    border: '#3e4451',
    selection: '#3e4451',
    cursor: '#abb2bf',
    highlight: '#e5c07b',
    disabled: '#5c6370'
  }
};

export const xcodeTheme: Theme = {
  name: 'xcode',
  colors: {
    primary: '#0f68a0',
    secondary: '#326d74',
    accent: '#ad3da4',
    background: '#ffffff',
    foreground: '#000000',
    muted: '#6c7986',
    success: '#1b7c83',
    warning: '#78492a',
    error: '#d12f1b',
    info: '#0f68a0'
  },
  syntax: {
    keyword: '#ad3da4',
    string: '#d12f1b',
    number: '#272ad8',
    comment: '#6c7986',
    function: '#326d74',
    variable: '#000000',
    type: '#0f68a0',
    operator: '#ad3da4'
  },
  ui: {
    border: '#e5e5e5',
    selection: '#b3d7ff',
    cursor: '#000000',
    highlight: '#fff3a0',
    disabled: '#a0a0a0'
  }
};

export const noColorTheme: Theme = {
  name: 'no-color',
  colors: {
    primary: 'white',
    secondary: 'white',
    accent: 'white',
    background: 'black',
    foreground: 'white',
    muted: 'white',
    success: 'white',
    warning: 'white',
    error: 'white',
    info: 'white'
  },
  syntax: {
    keyword: 'white',
    string: 'white',
    number: 'white',
    comment: 'white',
    function: 'white',
    variable: 'white',
    type: 'white',
    operator: 'white'
  },
  ui: {
    border: 'white',
    selection: 'white',
    cursor: 'white',
    highlight: 'white',
    disabled: 'white'
  }
};

export const availableThemes: Record<string, Theme> = {
  default: defaultTheme,
  dracula: draculaTheme,
  'github-dark': githubDarkTheme,
  'github-light': githubLightTheme,
  ayu: ayuTheme,
  'ayu-light': ayuLightTheme,
  'atom-one-dark': atomOneDarkTheme,
  xcode: xcodeTheme,
  'no-color': noColorTheme
};

export function getTheme(themeName: string): Theme {
  return availableThemes[themeName] || defaultTheme;
}

export function getAvailableThemes(): string[] {
  return Object.keys(availableThemes);
}
