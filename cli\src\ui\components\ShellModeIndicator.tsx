// Shell mode visual indicator
import React from 'react';
import { Box, Text } from 'ink';

interface ShellModeIndicatorProps {
  enabled: boolean;
  sandboxLevel?: 'strict' | 'permissive' | 'disabled';
  approvalLevel?: 'default' | 'auto-edit' | 'yolo';
}

export const ShellModeIndicator: React.FC<ShellModeIndicatorProps> = ({
  enabled,
  sandboxLevel = 'strict',
  approvalLevel = 'default'
}) => {
  if (!enabled) {
    return null;
  }

  const getSandboxColor = () => {
    switch (sandboxLevel) {
      case 'strict':
        return 'green';
      case 'permissive':
        return 'yellow';
      case 'disabled':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getApprovalColor = () => {
    switch (approvalLevel) {
      case 'default':
        return 'blue';
      case 'auto-edit':
        return 'yellow';
      case 'yolo':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getSandboxIcon = () => {
    switch (sandboxLevel) {
      case 'strict':
        return '🔒';
      case 'permissive':
        return '🔓';
      case 'disabled':
        return '⚠️';
      default:
        return '🛡️';
    }
  };

  const getApprovalIcon = () => {
    switch (approvalLevel) {
      case 'default':
        return '✋';
      case 'auto-edit':
        return '🔧';
      case 'yolo':
        return '🚀';
      default:
        return '❓';
    }
  };

  return (
    <Box 
      borderStyle="round" 
      borderColor="blue"
      paddingX={1}
      marginBottom={1}
    >
      <Box flexDirection="row" alignItems="center">
        <Box marginRight={2}>
          <Text color="blue" bold>
            🖥️ SHELL MODE
          </Text>
        </Box>
        
        <Box flexDirection="row" alignItems="center" marginRight={2}>
          <Text color={getSandboxColor()}>
            {getSandboxIcon()} {sandboxLevel.toUpperCase()}
          </Text>
        </Box>
        
        <Box flexDirection="row" alignItems="center">
          <Text color={getApprovalColor()}>
            {getApprovalIcon()} {approvalLevel.toUpperCase()}
          </Text>
        </Box>
      </Box>
      
      <Box marginTop={1}>
        <Text dimColor fontSize={12}>
          Shell commands enabled with {sandboxLevel} sandbox and {approvalLevel} approval
        </Text>
      </Box>
    </Box>
  );
};
