// Console output capture and display
import React, { useEffect, useState, useRef } from 'react';
import { Box, Text } from 'ink';

interface ConsoleMessage {
  id: string;
  type: 'log' | 'warn' | 'error' | 'info' | 'debug';
  message: string;
  timestamp: Date;
  args: any[];
}

interface ConsolePatcherProps {
  onMessage?: (message: ConsoleMessage) => void;
  maxMessages?: number;
  showTimestamp?: boolean;
  showType?: boolean;
}

export const ConsolePatcher: React.FC<ConsolePatcherProps> = ({
  onMessage,
  maxMessages = 100,
  showTimestamp = false,
  showType = true
}) => {
  const [messages, setMessages] = useState<ConsoleMessage[]>([]);
  const originalConsole = useRef<{
    log: typeof console.log;
    warn: typeof console.warn;
    error: typeof console.error;
    info: typeof console.info;
    debug: typeof console.debug;
  }>();

  useEffect(() => {
    // Store original console methods
    originalConsole.current = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    const createPatchedMethod = (type: ConsoleMessage['type']) => {
      return (...args: any[]) => {
        const message: ConsoleMessage = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          type,
          message: args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '),
          timestamp: new Date(),
          args
        };

        setMessages(prev => {
          const newMessages = [...prev, message];
          if (newMessages.length > maxMessages) {
            return newMessages.slice(-maxMessages);
          }
          return newMessages;
        });

        onMessage?.(message);

        // Call original console method
        originalConsole.current?.[type]?.(...args);
      };
    };

    // Patch console methods
    console.log = createPatchedMethod('log');
    console.warn = createPatchedMethod('warn');
    console.error = createPatchedMethod('error');
    console.info = createPatchedMethod('info');
    console.debug = createPatchedMethod('debug');

    // Cleanup function
    return () => {
      if (originalConsole.current) {
        console.log = originalConsole.current.log;
        console.warn = originalConsole.current.warn;
        console.error = originalConsole.current.error;
        console.info = originalConsole.current.info;
        console.debug = originalConsole.current.debug;
      }
    };
  }, [onMessage, maxMessages]);

  const getTypeColor = (type: ConsoleMessage['type']) => {
    switch (type) {
      case 'error':
        return 'red';
      case 'warn':
        return 'yellow';
      case 'info':
        return 'blue';
      case 'debug':
        return 'gray';
      case 'log':
      default:
        return 'white';
    }
  };

  const getTypeIcon = (type: ConsoleMessage['type']) => {
    switch (type) {
      case 'error':
        return '❌';
      case 'warn':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'debug':
        return '🐛';
      case 'log':
      default:
        return '📝';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  };

  if (messages.length === 0) {
    return null;
  }

  return (
    <Box flexDirection="column" marginTop={1}>
      <Box>
        <Text bold color="gray">
          Console Output:
        </Text>
      </Box>
      
      <Box flexDirection="column" marginTop={1} paddingX={1}>
        {messages.slice(-10).map((message) => (
          <Box key={message.id} flexDirection="row" marginBottom={0}>
            {showTimestamp && (
              <Text color="gray" dimColor>
                [{formatTimestamp(message.timestamp)}]{' '}
              </Text>
            )}
            
            {showType && (
              <Text color={getTypeColor(message.type)}>
                {getTypeIcon(message.type)} {message.type.toUpperCase()}:{' '}
              </Text>
            )}
            
            <Text color={getTypeColor(message.type)}>
              {message.message}
            </Text>
          </Box>
        ))}
        
        {messages.length > 10 && (
          <Box marginTop={1}>
            <Text dimColor>
              ... and {messages.length - 10} more messages
            </Text>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export const useConsoleCapture = (
  maxMessages: number = 100
): {
  messages: ConsoleMessage[];
  clearMessages: () => void;
  getMessagesByType: (type: ConsoleMessage['type']) => ConsoleMessage[];
} => {
  const [messages, setMessages] = useState<ConsoleMessage[]>([]);

  const clearMessages = () => {
    setMessages([]);
  };

  const getMessagesByType = (type: ConsoleMessage['type']) => {
    return messages.filter(message => message.type === type);
  };

  return {
    messages,
    clearMessages,
    getMessagesByType
  };
};
