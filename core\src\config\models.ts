// AI model definitions and constants

export interface ModelConfig {
  id: string;
  name: string;
  provider: string;
  maxTokens: number;
  supportsTools: boolean;
  supportsStreaming: boolean;
  costPer1kTokens: {
    input: number;
    output: number;
  };
}

export const PROVIDERS = {
  DEEPSEEK: 'deepseek',
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  GOOGLE: 'google'
} as const;

export type Provider = typeof PROVIDERS[keyof typeof PROVIDERS];

export const MODELS: Record<string, ModelConfig> = {
  // DeepSeek Models
  'deepseek-chat': {
    id: 'deepseek-chat',
    name: 'DeepSeek Chat',
    provider: PROVIDERS.DEEPSEEK,
    maxTokens: 32768,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.14,
      output: 0.28
    }
  },
  'deepseek-coder': {
    id: 'deepseek-coder',
    name: 'DeepSeek Coder',
    provider: PROVIDERS.DEEPSEEK,
    maxTokens: 16384,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.14,
      output: 0.28
    }
  },

  // OpenAI Models
  'gpt-4': {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: PROVIDERS.OPENAI,
    maxTokens: 8192,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 30.0,
      output: 60.0
    }
  },
  'gpt-4-turbo': {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: PROVIDERS.OPENAI,
    maxTokens: 128000,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 10.0,
      output: 30.0
    }
  },
  'gpt-3.5-turbo': {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: PROVIDERS.OPENAI,
    maxTokens: 16384,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.5,
      output: 1.5
    }
  },

  // Anthropic Models
  'claude-3-opus': {
    id: 'claude-3-opus-20240229',
    name: 'Claude 3 Opus',
    provider: PROVIDERS.ANTHROPIC,
    maxTokens: 200000,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 15.0,
      output: 75.0
    }
  },
  'claude-3-sonnet': {
    id: 'claude-3-sonnet-20240229',
    name: 'Claude 3 Sonnet',
    provider: PROVIDERS.ANTHROPIC,
    maxTokens: 200000,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 3.0,
      output: 15.0
    }
  },
  'claude-3-haiku': {
    id: 'claude-3-haiku-20240307',
    name: 'Claude 3 Haiku',
    provider: PROVIDERS.ANTHROPIC,
    maxTokens: 200000,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.25,
      output: 1.25
    }
  },

  // Google Models
  'gemini-pro': {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    provider: PROVIDERS.GOOGLE,
    maxTokens: 32768,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.5,
      output: 1.5
    }
  },
  'gemini-pro-vision': {
    id: 'gemini-pro-vision',
    name: 'Gemini Pro Vision',
    provider: PROVIDERS.GOOGLE,
    maxTokens: 16384,
    supportsTools: true,
    supportsStreaming: true,
    costPer1kTokens: {
      input: 0.5,
      output: 1.5
    }
  }
};

export const DEFAULT_MODELS: Record<Provider, string> = {
  [PROVIDERS.DEEPSEEK]: 'deepseek-chat',
  [PROVIDERS.OPENAI]: 'gpt-4-turbo',
  [PROVIDERS.ANTHROPIC]: 'claude-3-sonnet',
  [PROVIDERS.GOOGLE]: 'gemini-pro'
};

export function getModelConfig(modelId: string): ModelConfig | undefined {
  return MODELS[modelId];
}

export function getModelsByProvider(provider: Provider): ModelConfig[] {
  return Object.values(MODELS).filter(model => model.provider === provider);
}

export function isValidProvider(provider: string): provider is Provider {
  return Object.values(PROVIDERS).includes(provider as Provider);
}

export function getDefaultModelForProvider(provider: Provider): string {
  return DEFAULT_MODELS[provider];
}
