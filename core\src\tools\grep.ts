// Text search tool with regex support
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { BaseTool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const GrepSchema = z.object({
  pattern: z.string().describe('Search pattern (can be regex if regex option is true)'),
  path: z.string().optional().describe('File or directory path to search in (defaults to current directory)'),
  recursive: z.boolean().optional().default(false).describe('Search recursively in directories'),
  regex: z.boolean().optional().default(false).describe('Treat pattern as regular expression'),
  ignoreCase: z.boolean().optional().default(false).describe('Case insensitive search'),
  wholeWord: z.boolean().optional().default(false).describe('Match whole words only'),
  lineNumbers: z.boolean().optional().default(true).describe('Include line numbers in results'),
  context: z.number().optional().default(0).describe('Number of context lines to show around matches'),
  maxMatches: z.number().optional().default(1000).describe('Maximum number of matches to return'),
  includeHidden: z.boolean().optional().default(false).describe('Include hidden files in search'),
  filePattern: z.string().optional().describe('File pattern to filter search (e.g., "*.ts", "*.js")'),
  exclude: z.array(z.string()).optional().describe('Patterns to exclude from search'),
  binaryFiles: z.enum(['skip', 'text', 'binary']).optional().default('skip').describe('How to handle binary files')
});

export interface GrepMatch {
  file: string;
  lineNumber: number;
  line: string;
  matchStart: number;
  matchEnd: number;
  context?: {
    before: string[];
    after: string[];
  };
}

export interface GrepResult {
  matches: GrepMatch[];
  totalMatches: number;
  filesSearched: number;
  filesWithMatches: number;
  searchTime: number;
  pattern: string;
  searchPath: string;
}

export class GrepTool extends BaseTool {
  constructor() {
    super({
      name: 'grep',
      description: 'Search for text patterns in files with regex support',
      parameters: GrepSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const startTime = Date.now();

      const searchPath = validatedParams.path 
        ? path.resolve(context.workingDirectory, validatedParams.path)
        : context.workingDirectory;

      // Security check - ensure search path is within working directory
      if (!searchPath.startsWith(context.workingDirectory)) {
        throw new ToolExecutionError(
          this.definition.name,
          'Search path is outside the allowed working directory'
        );
      }

      logger.debug('Starting grep search', {
        pattern: validatedParams.pattern,
        searchPath,
        options: this.sanitizeParams(validatedParams)
      });

      const result = await this.performSearch(searchPath, validatedParams);
      result.searchTime = Date.now() - startTime;

      logger.info('Grep search completed', {
        pattern: validatedParams.pattern,
        totalMatches: result.totalMatches,
        filesSearched: result.filesSearched,
        searchTime: result.searchTime
      });

      return this.createSuccessResult(
        `Found ${result.totalMatches} matches in ${result.filesWithMatches} files`,
        result
      );

    } catch (error) {
      logger.error('Grep search failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async performSearch(searchPath: string, params: any): Promise<GrepResult> {
    const matches: GrepMatch[] = [];
    const searchedFiles = new Set<string>();
    const filesWithMatches = new Set<string>();

    // Create regex pattern
    const regex = this.createRegex(params.pattern, params);

    // Check if searchPath is a file or directory
    const stat = await fs.stat(searchPath);
    
    if (stat.isFile()) {
      await this.searchFile(searchPath, regex, params, matches, searchedFiles, filesWithMatches);
    } else if (stat.isDirectory()) {
      await this.searchDirectory(searchPath, regex, params, matches, searchedFiles, filesWithMatches);
    }

    // Limit matches if necessary
    if (matches.length > params.maxMatches) {
      matches.splice(params.maxMatches);
    }

    return {
      matches,
      totalMatches: matches.length,
      filesSearched: searchedFiles.size,
      filesWithMatches: filesWithMatches.size,
      searchTime: 0, // Will be set by caller
      pattern: params.pattern,
      searchPath
    };
  }

  private async searchDirectory(
    dirPath: string,
    regex: RegExp,
    params: any,
    matches: GrepMatch[],
    searchedFiles: Set<string>,
    filesWithMatches: Set<string>
  ): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);

        // Skip hidden files if not requested
        if (!params.includeHidden && entry.name.startsWith('.')) {
          continue;
        }

        // Check if this path should be excluded
        if (this.isExcluded(fullPath, params.exclude || [])) {
          continue;
        }

        if (entry.isFile()) {
          // Check file pattern filter
          if (params.filePattern && !this.matchesFilePattern(entry.name, params.filePattern)) {
            continue;
          }

          await this.searchFile(fullPath, regex, params, matches, searchedFiles, filesWithMatches);
        } else if (entry.isDirectory() && params.recursive) {
          await this.searchDirectory(fullPath, regex, params, matches, searchedFiles, filesWithMatches);
        }

        // Stop if we've reached max matches
        if (matches.length >= params.maxMatches) {
          break;
        }
      }
    } catch (error) {
      // Log but don't fail on permission errors
      logger.debug('Directory search failed', {
        directory: dirPath,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private async searchFile(
    filePath: string,
    regex: RegExp,
    params: any,
    matches: GrepMatch[],
    searchedFiles: Set<string>,
    filesWithMatches: Set<string>
  ): Promise<void> {
    try {
      searchedFiles.add(filePath);

      // Check if file is binary
      if (params.binaryFiles === 'skip' && await this.isBinaryFile(filePath)) {
        return;
      }

      const content = await fs.readFile(filePath, 'utf-8');
      const lines = content.split('\n');
      let fileHasMatches = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const match = regex.exec(line);

        if (match) {
          fileHasMatches = true;
          
          const grepMatch: GrepMatch = {
            file: filePath,
            lineNumber: i + 1,
            line: line,
            matchStart: match.index,
            matchEnd: match.index + match[0].length
          };

          // Add context if requested
          if (params.context > 0) {
            grepMatch.context = {
              before: this.getContextLines(lines, i, -params.context, 0),
              after: this.getContextLines(lines, i, 1, params.context + 1)
            };
          }

          matches.push(grepMatch);

          // Stop if we've reached max matches
          if (matches.length >= params.maxMatches) {
            break;
          }
        }

        // Reset regex for global searches
        if (regex.global) {
          regex.lastIndex = 0;
        }
      }

      if (fileHasMatches) {
        filesWithMatches.add(filePath);
      }

    } catch (error) {
      // Log but don't fail on file read errors
      logger.debug('File search failed', {
        file: filePath,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private createRegex(pattern: string, params: any): RegExp {
    let regexPattern = pattern;
    let flags = 'g'; // Global search

    if (!params.regex) {
      // Escape special regex characters for literal search
      regexPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    if (params.wholeWord) {
      regexPattern = `\\b${regexPattern}\\b`;
    }

    if (params.ignoreCase) {
      flags += 'i';
    }

    try {
      return new RegExp(regexPattern, flags);
    } catch (error) {
      throw new ToolExecutionError(
        this.definition.name,
        `Invalid regex pattern: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private getContextLines(lines: string[], currentIndex: number, start: number, end: number): string[] {
    const contextLines: string[] = [];
    const startIndex = Math.max(0, currentIndex + start);
    const endIndex = Math.min(lines.length, currentIndex + end);

    for (let i = startIndex; i < endIndex; i++) {
      if (i !== currentIndex) {
        contextLines.push(lines[i]);
      }
    }

    return contextLines;
  }

  private async isBinaryFile(filePath: string): Promise<boolean> {
    try {
      // Read first 1024 bytes to check for binary content
      const buffer = Buffer.alloc(1024);
      const fd = await fs.open(filePath, 'r');
      const { bytesRead } = await fd.read(buffer, 0, 1024, 0);
      await fd.close();

      // Check for null bytes (common indicator of binary files)
      for (let i = 0; i < bytesRead; i++) {
        if (buffer[i] === 0) {
          return true;
        }
      }

      return false;
    } catch {
      // If we can't read the file, assume it's not binary
      return false;
    }
  }

  private matchesFilePattern(fileName: string, pattern: string): boolean {
    // Simple glob pattern matching
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(fileName);
  }

  private isExcluded(filePath: string, excludePatterns: string[]): boolean {
    return excludePatterns.some(pattern => {
      const regexPattern = pattern
        .replace(/\./g, '\\.')
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');
      
      const regex = new RegExp(regexPattern, 'i');
      return regex.test(filePath);
    });
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Limit pattern length for logging
    if (sanitized.pattern && sanitized.pattern.length > 100) {
      sanitized.pattern = sanitized.pattern.substring(0, 100) + '...';
    }
    
    return sanitized;
  }
}
