// Authentication progress indicator component
import React from 'react';
import { Box, Text, Newline } from 'ink';
import Spinner from 'ink-spinner';

interface AuthInProgressProps {
  message?: string;
  step?: string;
  progress?: number;
  total?: number;
}

export const AuthInProgress: React.FC<AuthInProgressProps> = ({
  message = 'Authenticating...',
  step,
  progress,
  total
}) => {
  const renderProgressBar = () => {
    if (progress === undefined || total === undefined) return null;
    
    const percentage = Math.round((progress / total) * 100);
    const barLength = 20;
    const filledLength = Math.round((percentage / 100) * barLength);
    const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
    
    return (
      <Box flexDirection="column">
        <Text>
          Progress: [{bar}] {percentage}%
        </Text>
        <Text dimColor>
          {progress} of {total} steps completed
        </Text>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" padding={1}>
      <Box>
        <Text color="blue">
          <Spinner type="dots" />
        </Text>
        <Text> {message}</Text>
      </Box>
      
      {step && (
        <>
          <Newline />
          <Text dimColor>Current step: {step}</Text>
        </>
      )}
      
      {renderProgressBar() && (
        <>
          <Newline />
          {renderProgressBar()}
        </>
      )}
      
      <Newline />
      <Text dimColor>Please wait...</Text>
    </Box>
  );
};
