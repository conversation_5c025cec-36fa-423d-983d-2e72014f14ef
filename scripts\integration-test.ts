#!/usr/bin/env tsx
// Integration test script to verify all components work together
import { promises as fs } from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  duration: number;
}

class IntegrationTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Running Arien CLI Integration Tests\n');

    await this.testFileStructure();
    await this.testPackageFiles();
    await this.testTypeScriptCompilation();
    await this.testCoreExports();
    await this.testCLIExports();
    await this.testConfigurationSystem();
    await this.testSecuritySystem();
    await this.testToolSystem();
    await this.testTelemetrySystem();
    await this.testAuthenticationSystem();
    await this.testServiceIntegration();
    await this.testUIComponents();
    await this.testThemeSystem();

    this.printResults();
  }

  private async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    try {
      await testFn();
      this.results.push({
        name,
        passed: true,
        duration: Date.now() - startTime
      });
      console.log(`✅ ${name}`);
    } catch (error) {
      this.results.push({
        name,
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      });
      console.log(`❌ ${name}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async testFileStructure(): Promise<void> {
    await this.runTest('File Structure', async () => {
      const requiredPaths = [
        'cli/src/index.ts',
        'cli/src/ui/App.tsx',
        'cli/src/ui/components/AuthDialog.tsx',
        'cli/package.json',
        'core/src/index.ts',
        'core/src/core/client.ts',
        'core/src/tools/tool-registry.ts',
        'core/src/config/config.ts',
        'core/src/security/approval.ts',
        'core/src/telemetry/sdk.ts',
        'core/package.json',
        'package.json',
        'README.md'
      ];

      for (const filePath of requiredPaths) {
        const fullPath = path.join(rootDir, filePath);
        try {
          await fs.access(fullPath);
        } catch {
          throw new Error(`Missing required file: ${filePath}`);
        }
      }
    });
  }

  private async testPackageFiles(): Promise<void> {
    await this.runTest('Package Configuration', async () => {
      // Test root package.json
      const rootPkg = JSON.parse(await fs.readFile(path.join(rootDir, 'package.json'), 'utf-8'));
      if (!rootPkg.workspaces) {
        throw new Error('Root package.json missing workspaces configuration');
      }

      // Test CLI package.json
      const cliPkg = JSON.parse(await fs.readFile(path.join(rootDir, 'cli/package.json'), 'utf-8'));
      if (!cliPkg.dependencies['@arien/core']) {
        throw new Error('CLI package missing core dependency');
      }
      if (!cliPkg.dependencies['ink']) {
        throw new Error('CLI package missing ink dependency');
      }

      // Test Core package.json
      const corePkg = JSON.parse(await fs.readFile(path.join(rootDir, 'core/package.json'), 'utf-8'));
      if (!corePkg.dependencies['ai']) {
        throw new Error('Core package missing AI SDK dependency');
      }
      if (!corePkg.dependencies['zod']) {
        throw new Error('Core package missing zod dependency');
      }
    });
  }

  private async testTypeScriptCompilation(): Promise<void> {
    await this.runTest('TypeScript Configuration', async () => {
      // Check TypeScript config files exist
      const tsConfigPaths = [
        'tsconfig.json',
        'cli/tsconfig.json',
        'core/tsconfig.json'
      ];

      for (const configPath of tsConfigPaths) {
        const fullPath = path.join(rootDir, configPath);
        try {
          const config = JSON.parse(await fs.readFile(fullPath, 'utf-8'));
          if (!config.compilerOptions) {
            throw new Error(`Invalid TypeScript config: ${configPath}`);
          }
        } catch (error) {
          if (error instanceof SyntaxError) {
            throw new Error(`Invalid JSON in TypeScript config: ${configPath}`);
          }
          throw error;
        }
      }
    });
  }

  private async testCoreExports(): Promise<void> {
    await this.runTest('Core Module Exports', async () => {
      const coreIndexPath = path.join(rootDir, 'core/src/index.ts');
      const coreIndex = await fs.readFile(coreIndexPath, 'utf-8');

      const requiredExports = [
        'client.js',
        'config.js',
        'tools.js',
        'security',
        'telemetry'
      ];

      for (const exportName of requiredExports) {
        if (!coreIndex.includes(exportName)) {
          throw new Error(`Missing export in core index: ${exportName}`);
        }
      }
    });
  }

  private async testCLIExports(): Promise<void> {
    await this.runTest('CLI Module Structure', async () => {
      const cliIndexPath = path.join(rootDir, 'cli/src/index.ts');
      const cliIndex = await fs.readFile(cliIndexPath, 'utf-8');

      if (!cliIndex.includes('commander')) {
        throw new Error('CLI index missing commander import');
      }

      // Check App.tsx exists and has proper structure
      const appPath = path.join(rootDir, 'cli/src/ui/App.tsx');
      const appContent = await fs.readFile(appPath, 'utf-8');

      if (!appContent.includes('React') || !appContent.includes('AuthDialog')) {
        throw new Error('App.tsx missing required imports');
      }
    });
  }

  private async testConfigurationSystem(): Promise<void> {
    await this.runTest('Configuration System', async () => {
      const configFiles = [
        'core/src/config/config.ts',
        'core/src/config/models.ts',
        'core/src/config/auth.ts',
        'core/src/config/settings.ts',
        'core/src/config/extension.ts'
      ];

      for (const configFile of configFiles) {
        const fullPath = path.join(rootDir, configFile);
        const content = await fs.readFile(fullPath, 'utf-8');
        
        if (!content.includes('export')) {
          throw new Error(`Configuration file missing exports: ${configFile}`);
        }
      }
    });
  }

  private async testSecuritySystem(): Promise<void> {
    await this.runTest('Security System', async () => {
      const securityFiles = [
        'core/src/security/approval.ts',
        'core/src/security/sandbox.ts',
        'core/src/security/index.ts'
      ];

      for (const securityFile of securityFiles) {
        const fullPath = path.join(rootDir, securityFile);
        const content = await fs.readFile(fullPath, 'utf-8');
        
        if (!content.includes('export')) {
          throw new Error(`Security file missing exports: ${securityFile}`);
        }
      }
    });
  }

  private async testToolSystem(): Promise<void> {
    await this.runTest('Tool System', async () => {
      const toolFiles = [
        'core/src/tools/tool-registry.ts',
        'core/src/tools/edit.ts',
        'core/src/tools/shell.ts',
        'core/src/tools/read-file.ts',
        'core/src/tools/write-file.ts',
        'core/src/tools/mcp-client.ts',
        'core/src/tools/mcp-tool.ts'
      ];

      for (const toolFile of toolFiles) {
        const fullPath = path.join(rootDir, toolFile);
        const content = await fs.readFile(fullPath, 'utf-8');
        
        if (!content.includes('export')) {
          throw new Error(`Tool file missing exports: ${toolFile}`);
        }
      }
    });
  }

  private async testTelemetrySystem(): Promise<void> {
    await this.runTest('Telemetry System', async () => {
      const telemetryFiles = [
        'core/src/telemetry/sdk.ts',
        'core/src/telemetry/metrics.ts',
        'core/src/telemetry/loggers.ts',
        'core/src/telemetry/types.ts',
        'core/src/telemetry/constants.ts'
      ];

      for (const telemetryFile of telemetryFiles) {
        const fullPath = path.join(rootDir, telemetryFile);
        const content = await fs.readFile(fullPath, 'utf-8');

        if (!content.includes('export')) {
          throw new Error(`Telemetry file missing exports: ${telemetryFile}`);
        }
      }
    });
  }

  private async testAuthenticationSystem(): Promise<void> {
    await this.runTest('Authentication System', async () => {
      const authFiles = [
        'core/src/config/auth.ts',
        'core/src/config/credentialStore.ts',
        'core/src/config/providerManager.ts',
        'cli/src/ui/components/AuthDialog.tsx'
      ];

      for (const authFile of authFiles) {
        const fullPath = path.join(rootDir, authFile);
        const content = await fs.readFile(fullPath, 'utf-8');

        if (!content.includes('export')) {
          throw new Error(`Auth file missing exports: ${authFile}`);
        }
      }

      // Check for provider support
      const authContent = await fs.readFile(path.join(rootDir, 'core/src/config/auth.ts'), 'utf-8');
      const providers = ['DEEPSEEK', 'OPENAI', 'ANTHROPIC', 'GOOGLE'];

      for (const provider of providers) {
        if (!authContent.includes(provider)) {
          throw new Error(`Missing provider support: ${provider}`);
        }
      }
    });
  }

  private async testServiceIntegration(): Promise<void> {
    await this.runTest('Service Integration', async () => {
      const serviceFiles = [
        'core/src/services/fileDiscoveryService.ts',
        'core/src/services/gitService.ts',
        'core/src/services/apiService.ts'
      ];

      for (const serviceFile of serviceFiles) {
        const fullPath = path.join(rootDir, serviceFile);
        const content = await fs.readFile(fullPath, 'utf-8');

        if (!content.includes('export')) {
          throw new Error(`Service file missing exports: ${serviceFile}`);
        }
      }
    });
  }

  private async testUIComponents(): Promise<void> {
    await this.runTest('UI Components', async () => {
      const uiComponents = [
        'cli/src/ui/components/AboutBox.tsx',
        'cli/src/ui/components/AuthDialog.tsx',
        'cli/src/ui/components/AutoAcceptIndicator.tsx',
        'cli/src/ui/components/ConsolePatcher.tsx',
        'cli/src/ui/components/ArienRespondingSpinner.tsx',
        'cli/src/ui/components/MemoryUsageDisplay.tsx',
        'cli/src/ui/components/ShellModeIndicator.tsx'
      ];

      for (const component of uiComponents) {
        const fullPath = path.join(rootDir, component);
        const content = await fs.readFile(fullPath, 'utf-8');

        if (!content.includes('React') || !content.includes('export')) {
          throw new Error(`UI component missing React or exports: ${component}`);
        }
      }
    });
  }

  private async testThemeSystem(): Promise<void> {
    await this.runTest('Theme System', async () => {
      const themePath = path.join(rootDir, 'cli/src/ui/themes/theme.ts');
      const themeContent = await fs.readFile(themePath, 'utf-8');

      const expectedThemes = [
        'default',
        'dracula',
        'github-dark',
        'github-light',
        'ayu',
        'ayu-light',
        'atom-one-dark',
        'xcode',
        'no-color'
      ];

      for (const theme of expectedThemes) {
        if (!themeContent.includes(theme)) {
          throw new Error(`Missing theme: ${theme}`);
        }
      }
    });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary\n');
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    console.log(`📈 Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => !r.passed)
        .forEach(r => {
          console.log(`  • ${r.name}: ${r.error}`);
        });
    }

    console.log('\n🎉 Integration test completed!');
    
    if (failed === 0) {
      console.log('🚀 All systems are ready for deployment!');
    } else {
      console.log('⚠️  Please fix the failed tests before deployment.');
      process.exit(1);
    }
  }
}

// Run the integration tests
const tester = new IntegrationTester();
tester.runAllTests().catch(error => {
  console.error('❌ Integration test runner failed:', error);
  process.exit(1);
});
