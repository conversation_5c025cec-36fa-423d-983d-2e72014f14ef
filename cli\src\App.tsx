// Main CLI application component
import React, { useState, useEffect } from 'react';
import { Box, Text, useApp, useStdin } from 'ink';
import { Chat } from './components/Chat.js';
import { LoadingSpinner } from './components/LoadingSpinner.js';
import { ErrorDisplay } from './components/ErrorDisplay.js';
import { useConfig } from './hooks/useConfig.js';
import { useTheme } from './hooks/useTheme.js';

export interface AppProps {
  initialMessage?: string;
  configPath?: string;
  theme?: string;
  debug?: boolean;
}

export const App: React.FC<AppProps> = ({
  initialMessage,
  configPath,
  theme: themeName,
  debug = false
}) => {
  const { exit } = useApp();
  const { isRawModeSupported } = useStdin();
  const config = useConfig();
  const theme = useTheme(themeName);
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<Error | null>(null);

  // Initialize the application
  useEffect(() => {
    const initialize = async () => {
      try {
        // Check if raw mode is supported (required for interactive input)
        if (!isRawModeSupported) {
          throw new Error('Raw mode is not supported in this terminal. Please use a compatible terminal.');
        }

        // Wait for config to load
        if (config.isLoading) {
          return;
        }

        if (config.error) {
          throw config.error;
        }

        // Additional initialization steps could go here
        // - Validate API keys
        // - Check tool availability
        // - Load extensions
        // - etc.

        setIsInitialized(true);
      } catch (error) {
        setInitError(error instanceof Error ? error : new Error(String(error)));
      }
    };

    initialize();
  }, [config.isLoading, config.error, isRawModeSupported]);

  // Handle initialization errors
  if (initError) {
    return (
      <Box flexDirection="column" padding={2}>
        <Text color={theme.colors.error} bold>
          Failed to initialize Arien CLI
        </Text>
        <Box marginTop={1}>
          <ErrorDisplay 
            error={initError} 
            showDetails={debug}
            onRetry={() => {
              setInitError(null);
              setIsInitialized(false);
            }}
          />
        </Box>
        <Box marginTop={2}>
          <Text color={theme.colors.muted}>
            Press Ctrl+C to exit
          </Text>
        </Box>
      </Box>
    );
  }

  // Show loading while initializing
  if (!isInitialized || config.isLoading) {
    return (
      <Box 
        flexDirection="column" 
        justifyContent="center" 
        alignItems="center" 
        height="100%"
      >
        <LoadingSpinner text="Initializing Arien CLI..." />
        <Box marginTop={1}>
          <Text color={theme.colors.muted}>
            Loading configuration and checking dependencies...
          </Text>
        </Box>
      </Box>
    );
  }

  // Show terminal compatibility warning
  if (!isRawModeSupported) {
    return (
      <Box flexDirection="column" padding={2}>
        <Text color={theme.colors.warning} bold>
          Terminal Compatibility Warning
        </Text>
        <Box marginTop={1}>
          <Text color={theme.colors.text}>
            Your terminal does not support raw mode, which is required for interactive input.
          </Text>
          <Text color={theme.colors.text}>
            Please use a compatible terminal such as:
          </Text>
          <Text color={theme.colors.muted}>
            • Terminal.app (macOS)
          </Text>
          <Text color={theme.colors.muted}>
            • iTerm2 (macOS)
          </Text>
          <Text color={theme.colors.muted}>
            • Windows Terminal (Windows)
          </Text>
          <Text color={theme.colors.muted}>
            • GNOME Terminal (Linux)
          </Text>
          <Text color={theme.colors.muted}>
            • Konsole (Linux)
          </Text>
        </Box>
        <Box marginTop={2}>
          <Text color={theme.colors.muted}>
            Press Ctrl+C to exit
          </Text>
        </Box>
      </Box>
    );
  }

  // Main application
  return (
    <Box flexDirection="column" height="100%">
      {/* Debug info */}
      {debug && (
        <Box 
          borderStyle="single" 
          borderColor={theme.colors.muted}
          paddingX={1}
          marginBottom={1}
        >
          <Text color={theme.colors.muted} dimColor>
            Debug Mode | Theme: {theme.name} | Provider: {config.defaultProvider} | Model: {config.defaultModel}
          </Text>
        </Box>
      )}

      {/* Main chat interface */}
      <Chat
        initialMessages={initialMessage ? [{
          role: 'user',
          content: initialMessage,
          timestamp: new Date()
        }] : []}
        onExit={() => exit()}
      />
    </Box>
  );
};
