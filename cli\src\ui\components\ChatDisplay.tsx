// Chat message display component
import React from 'react';
import { Box, Text, Newline } from 'ink';
import Spinner from 'ink-spinner';

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface ChatDisplayProps {
  messages: ChatMessage[];
  isProcessing: boolean;
  currentStreamingMessage?: string;
}

export const ChatDisplay: React.FC<ChatDisplayProps> = ({
  messages,
  isProcessing,
  currentStreamingMessage
}) => {
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    
    return (
      <Box key={message.id} flexDirection="column" marginBottom={1}>
        <Box>
          <Text color={isUser ? 'blue' : isSystem ? 'yellow' : 'green'} bold>
            {isUser ? '👤 You' : isSystem ? '⚙️  System' : '🤖 Assistant'}
          </Text>
          <Text dimColor> • {formatTime(message.timestamp)}</Text>
          {message.isStreaming && (
            <Text color="yellow">
              {' '}
              <Spinner type="dots" />
            </Text>
          )}
        </Box>
        
        <Box marginLeft={2} marginTop={1}>
          <Text>{message.content}</Text>
        </Box>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" padding={1} flexGrow={1}>
      {messages.length === 0 ? (
        <Box justifyContent="center" alignItems="center" height="100%">
          <Box flexDirection="column" alignItems="center">
            <Text bold color="blue">
              Welcome to Arien CLI! 🚀
            </Text>
            <Newline />
            <Text dimColor>
              Start a conversation by typing your message below.
            </Text>
            <Text dimColor>
              You can ask questions, request code help, or get assistance with any task.
            </Text>
          </Box>
        </Box>
      ) : (
        <Box flexDirection="column">
          {messages.map(renderMessage)}
          
          {isProcessing && !currentStreamingMessage && (
            <Box marginTop={1}>
              <Text color="yellow">
                <Spinner type="dots" />
              </Text>
              <Text> Thinking...</Text>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};
