// Tool call display component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../hooks/useTheme.js';

export interface ToolCall {
  id: string;
  name: string;
  parameters: any;
  status?: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
}

export interface ToolCallDisplayProps {
  toolCall: ToolCall;
  showParameters?: boolean;
  showResult?: boolean;
}

export const ToolCallDisplay: React.FC<ToolCallDisplayProps> = ({
  toolCall,
  showParameters = false,
  showResult = false
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  useInput((input, key) => {
    if (key.return && input === '') {
      setExpanded(!expanded);
    }
  });

  const getStatusIcon = (status?: string): string => {
    switch (status) {
      case 'pending': return '⏳';
      case 'running': return '⟳';
      case 'completed': return '✓';
      case 'failed': return '✗';
      default: return '○';
    }
  };

  const getStatusColor = (status?: string): string => {
    switch (status) {
      case 'pending': return theme.colors.warning;
      case 'running': return theme.colors.primary;
      case 'completed': return theme.colors.success;
      case 'failed': return theme.colors.error;
      default: return theme.colors.muted;
    }
  };

  const formatParameters = (params: any): string => {
    if (!params) return '{}';
    
    try {
      return JSON.stringify(params, null, 2);
    } catch {
      return String(params);
    }
  };

  const formatResult = (result: any): string => {
    if (!result) return 'No result';
    
    if (typeof result === 'string') {
      return result.length > 200 ? result.substring(0, 200) + '...' : result;
    }
    
    try {
      const jsonStr = JSON.stringify(result, null, 2);
      return jsonStr.length > 200 ? jsonStr.substring(0, 200) + '...' : jsonStr;
    } catch {
      return String(result);
    }
  };

  return (
    <Box flexDirection="column" marginLeft={2} marginY={1}>
      {/* Tool call header */}
      <Box>
        <Text color={getStatusColor(toolCall.status)}>
          {getStatusIcon(toolCall.status)}
        </Text>
        <Text color={theme.colors.text} marginLeft={1} bold>
          {toolCall.name}
        </Text>
        <Text color={theme.colors.muted} marginLeft={1}>
          ({toolCall.id})
        </Text>
        <Box marginLeft="auto">
          <Text color={theme.colors.muted} dimColor>
            Press Enter to {expanded ? 'collapse' : 'expand'}
          </Text>
        </Box>
      </Box>

      {/* Expanded details */}
      {(expanded || showParameters || showResult) && (
        <Box flexDirection="column" marginLeft={2} marginTop={1}>
          {/* Parameters */}
          {(expanded || showParameters) && (
            <Box flexDirection="column">
              <Text color={theme.colors.muted} bold>
                Parameters:
              </Text>
              <Box
                borderStyle="round"
                borderColor={theme.colors.border}
                paddingX={1}
                paddingY={0}
                marginTop={1}
              >
                <Text color={theme.colors.text}>
                  {formatParameters(toolCall.parameters)}
                </Text>
              </Box>
            </Box>
          )}

          {/* Result */}
          {(expanded || showResult) && toolCall.result && (
            <Box flexDirection="column" marginTop={1}>
              <Text color={theme.colors.muted} bold>
                Result:
              </Text>
              <Box
                borderStyle="round"
                borderColor={theme.colors.success}
                paddingX={1}
                paddingY={0}
                marginTop={1}
              >
                <Text color={theme.colors.text}>
                  {formatResult(toolCall.result)}
                </Text>
              </Box>
            </Box>
          )}

          {/* Error */}
          {(expanded || showResult) && toolCall.error && (
            <Box flexDirection="column" marginTop={1}>
              <Text color={theme.colors.error} bold>
                Error:
              </Text>
              <Box
                borderStyle="round"
                borderColor={theme.colors.error}
                paddingX={1}
                paddingY={0}
                marginTop={1}
              >
                <Text color={theme.colors.error}>
                  {toolCall.error}
                </Text>
              </Box>
            </Box>
          )}

          {/* Status details */}
          {expanded && toolCall.status && (
            <Box marginTop={1}>
              <Text color={theme.colors.muted}>
                Status: 
              </Text>
              <Text color={getStatusColor(toolCall.status)} marginLeft={1}>
                {toolCall.status}
              </Text>
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};
