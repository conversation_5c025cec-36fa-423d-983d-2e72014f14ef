// Loading spinner component
import React, { useState, useEffect } from 'react';
import { Text } from 'ink';
import { useTheme } from '../hooks/useTheme.js';

export interface LoadingSpinnerProps {
  text?: string;
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  text,
  size = 'medium',
  color
}) => {
  const theme = useTheme();
  const [frame, setFrame] = useState(0);

  const spinners = {
    small: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
    medium: ['◐', '◓', '◑', '◒'],
    large: ['⣾', '⣽', '⣻', '⢿', '⡿', '⣟', '⣯', '⣷']
  };

  const spinner = spinners[size];
  const spinnerColor = color || theme.colors.primary;

  useEffect(() => {
    const interval = setInterval(() => {
      setFrame(prev => (prev + 1) % spinner.length);
    }, size === 'small' ? 80 : size === 'medium' ? 200 : 120);

    return () => clearInterval(interval);
  }, [spinner.length, size]);

  return (
    <Text color={spinnerColor}>
      {spinner[frame]}
      {text && ` ${text}`}
    </Text>
  );
};
