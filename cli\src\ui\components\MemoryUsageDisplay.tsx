// Memory usage statistics display
import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';

interface MemoryUsageDisplayProps {
  showDetails?: boolean;
  updateInterval?: number;
}

interface MemoryStats {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  arrayBuffers: number;
}

export const MemoryUsageDisplay: React.FC<MemoryUsageDisplayProps> = ({
  showDetails = false,
  updateInterval = 5000
}) => {
  const [memoryStats, setMemoryStats] = useState<MemoryStats | null>(null);

  useEffect(() => {
    const updateMemoryStats = () => {
      const usage = process.memoryUsage();
      setMemoryStats({
        heapUsed: usage.heapUsed,
        heapTotal: usage.heapTotal,
        external: usage.external,
        rss: usage.rss,
        arrayBuffers: usage.arrayBuffers
      });
    };

    // Initial update
    updateMemoryStats();

    // Set up interval
    const timer = setInterval(updateMemoryStats, updateInterval);

    return () => clearInterval(timer);
  }, [updateInterval]);

  const formatBytes = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const getMemoryUsageColor = (used: number, total: number): string => {
    const percentage = (used / total) * 100;
    if (percentage > 80) return 'red';
    if (percentage > 60) return 'yellow';
    return 'green';
  };

  const getMemoryBar = (used: number, total: number, width: number = 20): string => {
    const percentage = (used / total) * 100;
    const filled = Math.floor((percentage / 100) * width);
    const empty = width - filled;
    
    return '█'.repeat(filled) + '░'.repeat(empty);
  };

  if (!memoryStats) {
    return (
      <Box>
        <Text dimColor>Loading memory stats...</Text>
      </Box>
    );
  }

  const heapPercentage = ((memoryStats.heapUsed / memoryStats.heapTotal) * 100).toFixed(1);
  const memoryColor = getMemoryUsageColor(memoryStats.heapUsed, memoryStats.heapTotal);

  if (!showDetails) {
    return (
      <Box flexDirection="row" alignItems="center">
        <Text color="gray">Memory: </Text>
        <Text color={memoryColor}>
          {formatBytes(memoryStats.heapUsed)} / {formatBytes(memoryStats.heapTotal)}
        </Text>
        <Text color="gray"> ({heapPercentage}%)</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      <Box flexDirection="row" alignItems="center">
        <Text bold color="blue">Memory Usage</Text>
      </Box>
      
      <Box flexDirection="column" marginTop={1} marginLeft={2}>
        <Box flexDirection="row">
          <Text color="gray">Heap: </Text>
          <Text color={memoryColor}>
            {formatBytes(memoryStats.heapUsed)} / {formatBytes(memoryStats.heapTotal)}
          </Text>
          <Text color="gray"> ({heapPercentage}%)</Text>
        </Box>
        
        <Box marginTop={1}>
          <Text color={memoryColor}>
            {getMemoryBar(memoryStats.heapUsed, memoryStats.heapTotal)}
          </Text>
        </Box>
        
        <Box flexDirection="column" marginTop={1}>
          <Box flexDirection="row">
            <Text color="gray">RSS: </Text>
            <Text>{formatBytes(memoryStats.rss)}</Text>
          </Box>
          
          <Box flexDirection="row">
            <Text color="gray">External: </Text>
            <Text>{formatBytes(memoryStats.external)}</Text>
          </Box>
          
          <Box flexDirection="row">
            <Text color="gray">Array Buffers: </Text>
            <Text>{formatBytes(memoryStats.arrayBuffers)}</Text>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
