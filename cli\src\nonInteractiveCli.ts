// Non-interactive mode handler for piped input
import { ArienClient, logger } from '@arien/core';

interface NonInteractiveOptions {
  provider?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  autoApprove?: boolean;
  stream?: boolean;
  web?: boolean;
  debug?: boolean;
}

export async function nonInteractiveCli(message: string, options: NonInteractiveOptions = {}): Promise<void> {
  try {
    logger.info('Starting non-interactive mode', { message: message.substring(0, 100), options });

    // Initialize AI client
    const client = new ArienClient({
      provider: options.provider as any,
      model: options.model,
      temperature: options.temperature,
      maxTokens: options.maxTokens,
      stream: options.stream
    });

    // Prepare messages
    const messages = [
      {
        role: 'user' as const,
        content: message
      }
    ];

    // Handle streaming vs non-streaming
    if (options.stream) {
      // Streaming mode
      const stream = client.streamChat(messages, undefined, {
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });

      for await (const chunk of stream) {
        process.stdout.write(chunk);
      }
      
      console.log(); // Add newline at the end
    } else {
      // Non-streaming mode
      const response = await client.chat(messages, undefined, {
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });

      console.log(response.content);

      // Show usage information if available
      if (response.usage && options.debug) {
        console.error(`\nUsage: ${response.usage.totalTokens} tokens (${response.usage.promptTokens} prompt + ${response.usage.completionTokens} completion)`);
      }
    }

    logger.info('Non-interactive mode completed successfully');
  } catch (error) {
    logger.error('Non-interactive mode failed', { error, message, options });
    
    if (error instanceof Error) {
      console.error(`❌ Error: ${error.message}`);
    } else {
      console.error('❌ An unknown error occurred');
    }
    
    process.exit(1);
  }
}
