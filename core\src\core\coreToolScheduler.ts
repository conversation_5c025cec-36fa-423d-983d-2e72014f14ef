// Tool execution scheduling and management
import { ToolRegistry, ToolApprovalRequest, ToolApprovalResponse } from '../tools/tool-registry.js';
import { ToolContext, ToolResult, ToolExecutionError } from '../tools/tools.js';
import { logger } from './logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { retryWithBackoff } from '../utils/retry.js';

export interface ScheduledTool {
  id: string;
  toolName: string;
  parameters: any;
  context: ToolContext;
  priority: number;
  scheduledAt: Date;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  result?: ToolResult;
  error?: Error;
  retryCount: number;
  maxRetries: number;
}

export interface ToolExecutionOptions {
  priority?: number;
  maxRetries?: number;
  timeout?: number;
  requiresApproval?: boolean;
  approvalHandler?: (request: ToolApprovalRequest) => Promise<ToolApprovalResponse>;
}

export interface SchedulerMetrics {
  totalScheduled: number;
  completed: number;
  failed: number;
  pending: number;
  running: number;
  averageExecutionTime: number;
}

export class CoreToolScheduler {
  private toolRegistry: ToolRegistry;
  private scheduledTools = new Map<string, ScheduledTool>();
  private runningTools = new Set<string>();
  private maxConcurrentTools: number;
  private isRunning = false;
  private processingInterval?: NodeJS.Timeout;

  constructor(
    toolRegistry: ToolRegistry,
    maxConcurrentTools: number = 5
  ) {
    this.toolRegistry = toolRegistry;
    this.maxConcurrentTools = maxConcurrentTools;
  }

  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.processingInterval = setInterval(() => {
      this.processScheduledTools();
    }, 100); // Check every 100ms

    logger.info('Tool scheduler started', {
      maxConcurrentTools: this.maxConcurrentTools
    });
  }

  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    // Cancel all pending tools
    for (const [id, tool] of this.scheduledTools.entries()) {
      if (tool.status === 'pending') {
        tool.status = 'cancelled';
      }
    }

    logger.info('Tool scheduler stopped');
  }

  public async scheduleToolExecution(
    toolName: string,
    parameters: any,
    context: ToolContext,
    options: ToolExecutionOptions = {}
  ): Promise<string> {
    const toolId = this.generateToolId();
    
    const scheduledTool: ScheduledTool = {
      id: toolId,
      toolName,
      parameters,
      context,
      priority: options.priority || 0,
      scheduledAt: new Date(),
      status: 'pending',
      retryCount: 0,
      maxRetries: options.maxRetries || 3
    };

    this.scheduledTools.set(toolId, scheduledTool);

    logger.debug('Tool execution scheduled', {
      toolId,
      toolName,
      priority: scheduledTool.priority,
      requiresApproval: options.requiresApproval
    });

    // If scheduler is not running, start it
    if (!this.isRunning) {
      this.start();
    }

    return toolId;
  }

  public async executeToolImmediately(
    toolName: string,
    parameters: any,
    context: ToolContext,
    options: ToolExecutionOptions = {}
  ): Promise<ToolResult> {
    const toolId = this.generateToolId();
    
    logger.debug('Executing tool immediately', {
      toolId,
      toolName,
      parameters: this.sanitizeParameters(parameters)
    });

    try {
      // Handle approval if required
      if (options.requiresApproval && options.approvalHandler) {
        const approvalRequest: ToolApprovalRequest = {
          toolName,
          parameters,
          description: `Execute ${toolName}`,
          riskLevel: this.assessRiskLevel(toolName, parameters),
          context
        };

        const approval = await options.approvalHandler(approvalRequest);
        if (!approval.approved) {
          throw new ArienError(
            ErrorCode.TOOL_EXECUTION_DENIED,
            approval.reason || 'Tool execution denied by user'
          );
        }
      }

      // Execute with retry logic
      const result = await retryWithBackoff(
        async () => {
          return await this.toolRegistry.executeTool(toolName, parameters, context);
        },
        {
          maxRetries: options.maxRetries || 3,
          baseDelay: 1000,
          maxDelay: 10000,
          shouldRetry: (error) => this.shouldRetryToolExecution(error)
        }
      );

      logger.debug('Tool executed successfully', {
        toolId,
        toolName,
        resultType: typeof result.data
      });

      return result;
    } catch (error) {
      logger.error('Tool execution failed', {
        toolId,
        toolName,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  public getToolStatus(toolId: string): ScheduledTool | undefined {
    return this.scheduledTools.get(toolId);
  }

  public async waitForToolCompletion(
    toolId: string,
    timeout: number = 30000
  ): Promise<ToolResult> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const tool = this.scheduledTools.get(toolId);
      if (!tool) {
        throw new ArienError(ErrorCode.TOOL_NOT_FOUND, `Tool ${toolId} not found`);
      }

      if (tool.status === 'completed') {
        return tool.result!;
      }

      if (tool.status === 'failed') {
        throw tool.error || new ArienError(ErrorCode.TOOL_EXECUTION_FAILED, 'Tool execution failed');
      }

      if (tool.status === 'cancelled') {
        throw new ArienError(ErrorCode.TOOL_EXECUTION_CANCELLED, 'Tool execution was cancelled');
      }

      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    throw new ArienError(ErrorCode.TOOL_EXECUTION_TIMEOUT, `Tool ${toolId} execution timed out`);
  }

  public cancelToolExecution(toolId: string): boolean {
    const tool = this.scheduledTools.get(toolId);
    if (!tool) {
      return false;
    }

    if (tool.status === 'pending') {
      tool.status = 'cancelled';
      logger.debug('Tool execution cancelled', { toolId });
      return true;
    }

    return false;
  }

  public getSchedulerMetrics(): SchedulerMetrics {
    const tools = Array.from(this.scheduledTools.values());
    
    const completed = tools.filter(t => t.status === 'completed');
    const failed = tools.filter(t => t.status === 'failed');
    const pending = tools.filter(t => t.status === 'pending');
    const running = tools.filter(t => t.status === 'running');

    const totalExecutionTime = completed.reduce((sum, tool) => {
      if (tool.result?.executionTime) {
        return sum + tool.result.executionTime;
      }
      return sum;
    }, 0);

    const averageExecutionTime = completed.length > 0 ? totalExecutionTime / completed.length : 0;

    return {
      totalScheduled: tools.length,
      completed: completed.length,
      failed: failed.length,
      pending: pending.length,
      running: running.length,
      averageExecutionTime
    };
  }

  public clearCompletedTools(olderThan?: Date): number {
    const cutoff = olderThan || new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    let cleared = 0;

    for (const [id, tool] of this.scheduledTools.entries()) {
      if ((tool.status === 'completed' || tool.status === 'failed') && 
          tool.scheduledAt < cutoff) {
        this.scheduledTools.delete(id);
        cleared++;
      }
    }

    if (cleared > 0) {
      logger.info('Cleared completed tools', { cleared, cutoff });
    }

    return cleared;
  }

  private async processScheduledTools(): Promise<void> {
    if (this.runningTools.size >= this.maxConcurrentTools) {
      return;
    }

    // Get pending tools sorted by priority (higher priority first)
    const pendingTools = Array.from(this.scheduledTools.values())
      .filter(tool => tool.status === 'pending')
      .sort((a, b) => b.priority - a.priority);

    const availableSlots = this.maxConcurrentTools - this.runningTools.size;
    const toolsToExecute = pendingTools.slice(0, availableSlots);

    for (const tool of toolsToExecute) {
      this.executeScheduledTool(tool);
    }
  }

  private async executeScheduledTool(tool: ScheduledTool): Promise<void> {
    tool.status = 'running';
    this.runningTools.add(tool.id);

    const startTime = Date.now();

    try {
      logger.debug('Executing scheduled tool', {
        toolId: tool.id,
        toolName: tool.toolName,
        attempt: tool.retryCount + 1
      });

      const result = await this.toolRegistry.executeTool(
        tool.toolName,
        tool.parameters,
        tool.context
      );

      tool.result = {
        ...result,
        executionTime: Date.now() - startTime
      };
      tool.status = 'completed';

      logger.debug('Scheduled tool completed', {
        toolId: tool.id,
        toolName: tool.toolName,
        executionTime: tool.result.executionTime
      });
    } catch (error) {
      tool.error = error instanceof Error ? error : new Error(String(error));
      
      // Check if we should retry
      if (tool.retryCount < tool.maxRetries && this.shouldRetryToolExecution(error)) {
        tool.retryCount++;
        tool.status = 'pending';
        
        logger.debug('Retrying scheduled tool', {
          toolId: tool.id,
          toolName: tool.toolName,
          attempt: tool.retryCount + 1,
          maxRetries: tool.maxRetries
        });
      } else {
        tool.status = 'failed';
        
        logger.error('Scheduled tool failed', {
          toolId: tool.id,
          toolName: tool.toolName,
          error: tool.error.message,
          retryCount: tool.retryCount
        });
      }
    } finally {
      this.runningTools.delete(tool.id);
    }
  }

  private shouldRetryToolExecution(error: any): boolean {
    // Don't retry user-denied operations or validation errors
    if (error instanceof ArienError) {
      return ![
        ErrorCode.TOOL_EXECUTION_DENIED,
        ErrorCode.VALIDATION_ERROR,
        ErrorCode.PERMISSION_DENIED
      ].includes(error.code);
    }

    // Retry on network errors and temporary failures
    return error?.code === 'ECONNRESET' || 
           error?.code === 'ENOTFOUND' ||
           error?.message?.includes('timeout');
  }

  private assessRiskLevel(toolName: string, parameters: any): 'low' | 'medium' | 'high' {
    // Assess risk based on tool name and parameters
    const highRiskTools = ['shell', 'write-file', 'delete-file'];
    const mediumRiskTools = ['edit-file', 'move-file', 'copy-file'];

    if (highRiskTools.includes(toolName)) {
      return 'high';
    }

    if (mediumRiskTools.includes(toolName)) {
      return 'medium';
    }

    return 'low';
  }

  private sanitizeParameters(parameters: any): any {
    // Remove sensitive information from parameters for logging
    const sanitized = { ...parameters };
    
    const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth'];
    for (const key of Object.keys(sanitized)) {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  private generateToolId(): string {
    return `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
