// Auto-accept indicator hook
import { useState, useEffect, useCallback } from 'react';
import { getConfig } from '@arien/core';

export interface AutoAcceptState {
  isEnabled: boolean;
  level: 'default' | 'auto-edit' | 'yolo';
  actionsAutoApproved: number;
  lastAutoApprovedAction?: {
    action: string;
    timestamp: Date;
    riskLevel: 'low' | 'medium' | 'high';
  };
  sessionStartTime: Date;
}

export interface UseAutoAcceptIndicatorReturn {
  autoAcceptState: AutoAcceptState;
  toggleAutoAccept: () => void;
  setAutoAcceptLevel: (level: AutoAcceptState['level']) => void;
  recordAutoApprovedAction: (action: string, riskLevel: 'low' | 'medium' | 'high') => void;
  resetStats: () => void;
  getAutoAcceptDescription: () => string;
  isActionAutoApproved: (riskLevel: 'low' | 'medium' | 'high') => boolean;
}

export function useAutoAcceptIndicator(): UseAutoAcceptIndicatorReturn {
  const [autoAcceptState, setAutoAcceptState] = useState<AutoAcceptState>(() => {
    const config = getConfig();
    const approvalLevel = config.get('security.approvalLevel') || 'default';
    
    return {
      isEnabled: approvalLevel !== 'default',
      level: approvalLevel,
      actionsAutoApproved: 0,
      sessionStartTime: new Date()
    };
  });

  // Load initial state from config
  useEffect(() => {
    const config = getConfig();
    const approvalLevel = config.get('security.approvalLevel') || 'default';
    
    setAutoAcceptState(prev => ({
      ...prev,
      isEnabled: approvalLevel !== 'default',
      level: approvalLevel
    }));
  }, []);

  const toggleAutoAccept = useCallback(() => {
    setAutoAcceptState(prev => {
      const newLevel = prev.isEnabled ? 'default' : 'auto-edit';
      const config = getConfig();
      config.set('security.approvalLevel', newLevel);
      
      return {
        ...prev,
        isEnabled: !prev.isEnabled,
        level: newLevel
      };
    });
  }, []);

  const setAutoAcceptLevel = useCallback((level: AutoAcceptState['level']) => {
    setAutoAcceptState(prev => ({
      ...prev,
      isEnabled: level !== 'default',
      level
    }));

    const config = getConfig();
    config.set('security.approvalLevel', level);
  }, []);

  const recordAutoApprovedAction = useCallback((
    action: string, 
    riskLevel: 'low' | 'medium' | 'high'
  ) => {
    setAutoAcceptState(prev => ({
      ...prev,
      actionsAutoApproved: prev.actionsAutoApproved + 1,
      lastAutoApprovedAction: {
        action,
        timestamp: new Date(),
        riskLevel
      }
    }));
  }, []);

  const resetStats = useCallback(() => {
    setAutoAcceptState(prev => ({
      ...prev,
      actionsAutoApproved: 0,
      lastAutoApprovedAction: undefined,
      sessionStartTime: new Date()
    }));
  }, []);

  const getAutoAcceptDescription = useCallback(() => {
    switch (autoAcceptState.level) {
      case 'default':
        return 'Manual approval required for all operations';
      case 'auto-edit':
        return 'Auto-approve file edits, manual approval for other operations';
      case 'yolo':
        return 'Auto-approve all operations (dangerous!)';
      default:
        return 'Unknown approval level';
    }
  }, [autoAcceptState.level]);

  const isActionAutoApproved = useCallback((riskLevel: 'low' | 'medium' | 'high') => {
    if (!autoAcceptState.isEnabled) return false;

    switch (autoAcceptState.level) {
      case 'default':
        return false;
      case 'auto-edit':
        // Auto-approve low and medium risk file operations
        return riskLevel === 'low' || riskLevel === 'medium';
      case 'yolo':
        // Auto-approve everything
        return true;
      default:
        return false;
    }
  }, [autoAcceptState.isEnabled, autoAcceptState.level]);

  return {
    autoAcceptState,
    toggleAutoAccept,
    setAutoAcceptLevel,
    recordAutoApprovedAction,
    resetStats,
    getAutoAcceptDescription,
    isActionAutoApproved
  };
}
