// Editor configuration dialog component
import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import SelectInput from 'ink-select-input';
import TextInput from 'ink-text-input';
import { useTheme } from '../../hooks/useTheme.js';

export interface EditorSettings {
  editor: string;
  customCommand?: string;
  openInNewWindow: boolean;
  waitForClose: boolean;
  lineNumberSupport: boolean;
  projectSupport: boolean;
}

interface EditorSettingsDialogProps {
  currentSettings: EditorSettings;
  onSave: (settings: EditorSettings) => void;
  onCancel: () => void;
  isVisible: boolean;
}

const POPULAR_EDITORS = [
  { label: 'Visual Studio Code', value: 'code', command: 'code' },
  { label: 'Vim', value: 'vim', command: 'vim' },
  { label: 'Neovim', value: 'nvim', command: 'nvim' },
  { label: 'Emacs', value: 'emacs', command: 'emacs' },
  { label: 'Sublime Text', value: 'subl', command: 'subl' },
  { label: 'Atom', value: 'atom', command: 'atom' },
  { label: 'Nano', value: 'nano', command: 'nano' },
  { label: 'Custom', value: 'custom', command: '' }
];

export const EditorSettingsDialog: React.FC<EditorSettingsDialogProps> = ({
  currentSettings,
  onSave,
  onCancel,
  isVisible
}) => {
  const theme = useTheme();
  const [step, setStep] = useState<'editor' | 'custom' | 'options' | 'confirm'>('editor');
  const [settings, setSettings] = useState<EditorSettings>(currentSettings);
  const [customCommand, setCustomCommand] = useState(currentSettings.customCommand || '');

  useEffect(() => {
    setSettings(currentSettings);
    setCustomCommand(currentSettings.customCommand || '');
  }, [currentSettings]);

  useInput((input, key) => {
    if (!isVisible) return;

    if (key.escape) {
      onCancel();
    }
  });

  if (!isVisible) return null;

  const handleEditorSelect = (item: any) => {
    const selectedEditor = POPULAR_EDITORS.find(e => e.value === item.value);
    if (!selectedEditor) return;

    if (selectedEditor.value === 'custom') {
      setStep('custom');
    } else {
      setSettings(prev => ({
        ...prev,
        editor: selectedEditor.value,
        customCommand: selectedEditor.command
      }));
      setStep('options');
    }
  };

  const handleCustomCommandSubmit = () => {
    if (customCommand.trim()) {
      setSettings(prev => ({
        ...prev,
        editor: 'custom',
        customCommand: customCommand.trim()
      }));
      setStep('options');
    }
  };

  const handleOptionsSelect = (item: any) => {
    switch (item.value) {
      case 'newWindow':
        setSettings(prev => ({ ...prev, openInNewWindow: !prev.openInNewWindow }));
        break;
      case 'waitForClose':
        setSettings(prev => ({ ...prev, waitForClose: !prev.waitForClose }));
        break;
      case 'lineNumbers':
        setSettings(prev => ({ ...prev, lineNumberSupport: !prev.lineNumberSupport }));
        break;
      case 'projectSupport':
        setSettings(prev => ({ ...prev, projectSupport: !prev.projectSupport }));
        break;
      case 'save':
        setStep('confirm');
        break;
      case 'back':
        setStep('editor');
        break;
    }
  };

  const handleConfirmSelect = (item: any) => {
    if (item.value === 'save') {
      onSave(settings);
    } else {
      onCancel();
    }
  };

  const renderEditorStep = () => (
    <Box flexDirection="column">
      <Text color={theme.colors.primary}>Select Your Preferred Editor</Text>
      <Text color={theme.colors.muted}>Choose the editor you want to use for file editing:</Text>
      <Box marginTop={1}>
        <SelectInput
          items={POPULAR_EDITORS.map(editor => ({
            label: editor.label,
            value: editor.value
          }))}
          onSelect={handleEditorSelect}
        />
      </Box>
    </Box>
  );

  const renderCustomStep = () => (
    <Box flexDirection="column">
      <Text color={theme.colors.primary}>Custom Editor Command</Text>
      <Text color={theme.colors.muted}>Enter the command to launch your editor:</Text>
      <Box marginTop={1}>
        <Text color={theme.colors.text}>Command: </Text>
        <TextInput
          value={customCommand}
          onChange={setCustomCommand}
          onSubmit={handleCustomCommandSubmit}
          placeholder="e.g., /usr/local/bin/myeditor"
        />
      </Box>
      <Box marginTop={1}>
        <Text color={theme.colors.muted}>Press Enter to continue, Escape to cancel</Text>
      </Box>
    </Box>
  );

  const renderOptionsStep = () => {
    const options = [
      {
        label: `Open in new window: ${settings.openInNewWindow ? '✅' : '❌'}`,
        value: 'newWindow'
      },
      {
        label: `Wait for editor to close: ${settings.waitForClose ? '✅' : '❌'}`,
        value: 'waitForClose'
      },
      {
        label: `Line number support: ${settings.lineNumberSupport ? '✅' : '❌'}`,
        value: 'lineNumbers'
      },
      {
        label: `Project support: ${settings.projectSupport ? '✅' : '❌'}`,
        value: 'projectSupport'
      },
      { label: '💾 Save Settings', value: 'save' },
      { label: '← Back to Editor Selection', value: 'back' }
    ];

    return (
      <Box flexDirection="column">
        <Text color={theme.colors.primary}>Editor Options</Text>
        <Text color={theme.colors.muted}>
          Current editor: {settings.editor} ({settings.customCommand || 'default'})
        </Text>
        <Box marginTop={1}>
          <SelectInput items={options} onSelect={handleOptionsSelect} />
        </Box>
      </Box>
    );
  };

  const renderConfirmStep = () => {
    const confirmOptions = [
      { label: '✅ Save and Apply', value: 'save' },
      { label: '❌ Cancel', value: 'cancel' }
    ];

    return (
      <Box flexDirection="column">
        <Text color={theme.colors.primary}>Confirm Editor Settings</Text>
        <Box flexDirection="column" marginTop={1} marginBottom={1}>
          <Text color={theme.colors.text}>Editor: {settings.editor}</Text>
          <Text color={theme.colors.text}>Command: {settings.customCommand}</Text>
          <Text color={theme.colors.text}>
            Open in new window: {settings.openInNewWindow ? 'Yes' : 'No'}
          </Text>
          <Text color={theme.colors.text}>
            Wait for close: {settings.waitForClose ? 'Yes' : 'No'}
          </Text>
          <Text color={theme.colors.text}>
            Line numbers: {settings.lineNumberSupport ? 'Yes' : 'No'}
          </Text>
          <Text color={theme.colors.text}>
            Project support: {settings.projectSupport ? 'Yes' : 'No'}
          </Text>
        </Box>
        <SelectInput items={confirmOptions} onSelect={handleConfirmSelect} />
      </Box>
    );
  };

  return (
    <Box
      flexDirection="column"
      borderStyle="single"
      borderColor={theme.colors.primary}
      padding={1}
      width={60}
    >
      {step === 'editor' && renderEditorStep()}
      {step === 'custom' && renderCustomStep()}
      {step === 'options' && renderOptionsStep()}
      {step === 'confirm' && renderConfirmStep()}
    </Box>
  );
};
