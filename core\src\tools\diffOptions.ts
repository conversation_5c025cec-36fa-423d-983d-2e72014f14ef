// Diff display options and utilities
export interface DiffOptions {
  showLineNumbers: boolean;
  contextLines: number;
  highlightChanges: boolean;
  showWhitespace: boolean;
  wordDiff: boolean;
  ignoreCase: boolean;
  ignoreWhitespace: boolean;
  format: 'unified' | 'side-by-side' | 'inline';
  colorOutput: boolean;
}

export interface DiffLine {
  type: 'added' | 'removed' | 'unchanged' | 'context';
  content: string;
  lineNumber?: {
    old?: number;
    new?: number;
  };
}

export interface DiffChunk {
  oldStart: number;
  oldCount: number;
  newStart: number;
  newCount: number;
  lines: DiffLine[];
}

export interface DiffResult {
  chunks: DiffChunk[];
  stats: {
    additions: number;
    deletions: number;
    changes: number;
  };
  isBinary: boolean;
  isEmpty: boolean;
}

export class DiffGenerator {
  private options: DiffOptions;

  constructor(options: Partial<DiffOptions> = {}) {
    this.options = {
      showLineNumbers: true,
      contextLines: 3,
      highlightChanges: true,
      showWhitespace: false,
      wordDiff: false,
      ignoreCase: false,
      ignoreWhitespace: false,
      format: 'unified',
      colorOutput: true,
      ...options
    };
  }

  public generateDiff(oldContent: string, newContent: string): DiffResult {
    // Check if content is binary
    if (this.isBinaryContent(oldContent) || this.isBinaryContent(newContent)) {
      return {
        chunks: [],
        stats: { additions: 0, deletions: 0, changes: 0 },
        isBinary: true,
        isEmpty: false
      };
    }

    const oldLines = this.preprocessLines(oldContent.split('\n'));
    const newLines = this.preprocessLines(newContent.split('\n'));

    // Generate diff using Myers algorithm (simplified)
    const chunks = this.generateChunks(oldLines, newLines);
    const stats = this.calculateStats(chunks);

    return {
      chunks,
      stats,
      isBinary: false,
      isEmpty: chunks.length === 0
    };
  }

  public formatDiff(diffResult: DiffResult): string {
    if (diffResult.isBinary) {
      return 'Binary files differ';
    }

    if (diffResult.isEmpty) {
      return 'No differences found';
    }

    switch (this.options.format) {
      case 'unified':
        return this.formatUnified(diffResult);
      case 'side-by-side':
        return this.formatSideBySide(diffResult);
      case 'inline':
        return this.formatInline(diffResult);
      default:
        return this.formatUnified(diffResult);
    }
  }

  private preprocessLines(lines: string[]): string[] {
    let processed = lines;

    if (this.options.ignoreCase) {
      processed = processed.map(line => line.toLowerCase());
    }

    if (this.options.ignoreWhitespace) {
      processed = processed.map(line => line.replace(/\s+/g, ' ').trim());
    }

    return processed;
  }

  private generateChunks(oldLines: string[], newLines: string[]): DiffChunk[] {
    const chunks: DiffChunk[] = [];
    const lcs = this.longestCommonSubsequence(oldLines, newLines);
    
    let oldIndex = 0;
    let newIndex = 0;
    let currentChunk: DiffChunk | null = null;

    for (const match of lcs) {
      // Add removed lines
      while (oldIndex < match.oldIndex) {
        if (!currentChunk) {
          currentChunk = this.createNewChunk(oldIndex, newIndex);
        }
        currentChunk.lines.push({
          type: 'removed',
          content: oldLines[oldIndex],
          lineNumber: { old: oldIndex + 1 }
        });
        oldIndex++;
      }

      // Add added lines
      while (newIndex < match.newIndex) {
        if (!currentChunk) {
          currentChunk = this.createNewChunk(oldIndex, newIndex);
        }
        currentChunk.lines.push({
          type: 'added',
          content: newLines[newIndex],
          lineNumber: { new: newIndex + 1 }
        });
        newIndex++;
      }

      // Close current chunk if we have one
      if (currentChunk) {
        this.finalizeChunk(currentChunk, oldIndex, newIndex);
        chunks.push(currentChunk);
        currentChunk = null;
      }

      // Add context lines around changes
      const contextStart = Math.max(0, oldIndex - this.options.contextLines);
      const contextEnd = Math.min(oldLines.length, oldIndex + this.options.contextLines + 1);

      if (chunks.length > 0 || contextStart < oldIndex) {
        if (!currentChunk) {
          currentChunk = this.createNewChunk(contextStart, contextStart);
        }

        for (let i = contextStart; i < contextEnd && i < oldLines.length; i++) {
          if (i === oldIndex) {
            // This is the matching line
            currentChunk.lines.push({
              type: 'unchanged',
              content: oldLines[i],
              lineNumber: { old: i + 1, new: newIndex + 1 }
            });
            oldIndex++;
            newIndex++;
          } else if (i < oldIndex) {
            currentChunk.lines.push({
              type: 'context',
              content: oldLines[i],
              lineNumber: { old: i + 1, new: i + 1 }
            });
          }
        }
      } else {
        oldIndex++;
        newIndex++;
      }
    }

    // Handle remaining lines
    if (oldIndex < oldLines.length || newIndex < newLines.length) {
      if (!currentChunk) {
        currentChunk = this.createNewChunk(oldIndex, newIndex);
      }

      while (oldIndex < oldLines.length) {
        currentChunk.lines.push({
          type: 'removed',
          content: oldLines[oldIndex],
          lineNumber: { old: oldIndex + 1 }
        });
        oldIndex++;
      }

      while (newIndex < newLines.length) {
        currentChunk.lines.push({
          type: 'added',
          content: newLines[newIndex],
          lineNumber: { new: newIndex + 1 }
        });
        newIndex++;
      }

      this.finalizeChunk(currentChunk, oldIndex, newIndex);
      chunks.push(currentChunk);
    }

    return chunks;
  }

  private longestCommonSubsequence(oldLines: string[], newLines: string[]): Array<{ oldIndex: number; newIndex: number }> {
    const matches: Array<{ oldIndex: number; newIndex: number }> = [];
    
    // Simple LCS implementation - in production, use a more efficient algorithm
    for (let i = 0; i < oldLines.length; i++) {
      for (let j = 0; j < newLines.length; j++) {
        if (oldLines[i] === newLines[j]) {
          // Check if this match doesn't conflict with previous matches
          const lastMatch = matches[matches.length - 1];
          if (!lastMatch || (i > lastMatch.oldIndex && j > lastMatch.newIndex)) {
            matches.push({ oldIndex: i, newIndex: j });
          }
        }
      }
    }

    return matches;
  }

  private createNewChunk(oldStart: number, newStart: number): DiffChunk {
    return {
      oldStart: oldStart + 1,
      oldCount: 0,
      newStart: newStart + 1,
      newCount: 0,
      lines: []
    };
  }

  private finalizeChunk(chunk: DiffChunk, oldEnd: number, newEnd: number): void {
    chunk.oldCount = oldEnd - chunk.oldStart + 1;
    chunk.newCount = newEnd - chunk.newStart + 1;
  }

  private calculateStats(chunks: DiffChunk[]): { additions: number; deletions: number; changes: number } {
    let additions = 0;
    let deletions = 0;

    for (const chunk of chunks) {
      for (const line of chunk.lines) {
        if (line.type === 'added') {
          additions++;
        } else if (line.type === 'removed') {
          deletions++;
        }
      }
    }

    return {
      additions,
      deletions,
      changes: Math.max(additions, deletions)
    };
  }

  private formatUnified(diffResult: DiffResult): string {
    const lines: string[] = [];
    
    // Add header
    lines.push(`--- a/file`);
    lines.push(`+++ b/file`);

    for (const chunk of diffResult.chunks) {
      // Add chunk header
      lines.push(`@@ -${chunk.oldStart},${chunk.oldCount} +${chunk.newStart},${chunk.newCount} @@`);

      for (const line of chunk.lines) {
        let prefix = ' ';
        if (line.type === 'added') {
          prefix = '+';
        } else if (line.type === 'removed') {
          prefix = '-';
        }

        let formattedLine = `${prefix}${line.content}`;
        
        if (this.options.showLineNumbers && line.lineNumber) {
          const oldNum = line.lineNumber.old || '';
          const newNum = line.lineNumber.new || '';
          formattedLine = `${oldNum.toString().padStart(4)} ${newNum.toString().padStart(4)} ${formattedLine}`;
        }

        if (this.options.colorOutput) {
          formattedLine = this.colorize(formattedLine, line.type);
        }

        lines.push(formattedLine);
      }
    }

    // Add stats
    lines.push('');
    lines.push(`${diffResult.stats.additions} additions, ${diffResult.stats.deletions} deletions`);

    return lines.join('\n');
  }

  private formatSideBySide(diffResult: DiffResult): string {
    const lines: string[] = [];
    const maxWidth = 80;
    const halfWidth = Math.floor(maxWidth / 2) - 2;

    for (const chunk of diffResult.chunks) {
      lines.push(`@@ -${chunk.oldStart},${chunk.oldCount} +${chunk.newStart},${chunk.newCount} @@`);

      for (const line of chunk.lines) {
        let leftSide = '';
        let rightSide = '';
        let separator = ' | ';

        if (line.type === 'removed') {
          leftSide = line.content.substring(0, halfWidth);
          separator = ' - ';
        } else if (line.type === 'added') {
          rightSide = line.content.substring(0, halfWidth);
          separator = ' + ';
        } else {
          leftSide = line.content.substring(0, halfWidth);
          rightSide = line.content.substring(0, halfWidth);
        }

        const formattedLine = `${leftSide.padEnd(halfWidth)}${separator}${rightSide.padEnd(halfWidth)}`;
        lines.push(formattedLine);
      }
    }

    return lines.join('\n');
  }

  private formatInline(diffResult: DiffResult): string {
    const lines: string[] = [];

    for (const chunk of diffResult.chunks) {
      for (const line of chunk.lines) {
        let prefix = '';
        if (line.type === 'added') {
          prefix = '+ ';
        } else if (line.type === 'removed') {
          prefix = '- ';
        } else {
          prefix = '  ';
        }

        let formattedLine = `${prefix}${line.content}`;
        
        if (this.options.colorOutput) {
          formattedLine = this.colorize(formattedLine, line.type);
        }

        lines.push(formattedLine);
      }
    }

    return lines.join('\n');
  }

  private colorize(text: string, type: string): string {
    if (!this.options.colorOutput) {
      return text;
    }

    // ANSI color codes
    const colors = {
      added: '\x1b[32m',    // Green
      removed: '\x1b[31m',  // Red
      unchanged: '\x1b[37m', // White
      context: '\x1b[90m',  // Gray
      reset: '\x1b[0m'
    };

    const color = colors[type as keyof typeof colors] || colors.unchanged;
    return `${color}${text}${colors.reset}`;
  }

  private isBinaryContent(content: string): boolean {
    // Simple binary detection - check for null bytes
    return content.includes('\0');
  }
}
