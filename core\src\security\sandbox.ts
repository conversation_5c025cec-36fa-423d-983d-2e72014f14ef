// Security sandbox enforcement
import { promises as fs } from 'fs';
import * as path from 'path';
import { SandboxManager, SandboxConfig } from '../config/sandboxConfig.js';
import { logger } from '../core/logger.js';
import { SandboxViolationError, ErrorCode } from '../utils/errors.js';

export interface SandboxContext {
  userId: string;
  sessionId: string;
  workingDirectory: string;
  allowedCommands?: string[];
  allowedPaths?: string[];
  approvalLevel: 'default' | 'auto-edit' | 'yolo';
  sandboxEnabled: boolean;
}

export interface SecurityCheck {
  allowed: boolean;
  reason?: string;
  requiresApproval?: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

export class SecuritySandbox {
  private sandboxManager: SandboxManager;
  private approvalHandlers = new Map<string, (request: ApprovalRequest) => Promise<boolean>>();

  constructor(config?: Partial<SandboxConfig>) {
    this.sandboxManager = new SandboxManager(config);
  }

  public async checkFileAccess(
    filePath: string,
    operation: 'read' | 'write' | 'execute',
    context: SandboxContext
  ): Promise<SecurityCheck> {
    if (!context.sandboxEnabled) {
      return { allowed: true, riskLevel: 'low' };
    }

    try {
      // Resolve absolute path
      const absolutePath = path.resolve(context.workingDirectory, filePath);
      
      // Check path traversal
      if (!absolutePath.startsWith(context.workingDirectory)) {
        return {
          allowed: false,
          reason: 'Path traversal detected - file is outside working directory',
          riskLevel: 'high'
        };
      }

      // Check sandbox manager rules
      const allowed = this.sandboxManager.checkFileAccess(absolutePath, operation, context.userId);
      if (!allowed) {
        return {
          allowed: false,
          reason: 'File access denied by sandbox policy',
          riskLevel: this.assessFileRisk(absolutePath, operation)
        };
      }

      // Check for sensitive files
      const sensitiveCheck = this.checkSensitiveFile(absolutePath, operation);
      if (!sensitiveCheck.allowed) {
        return sensitiveCheck;
      }

      // Check file size limits for write operations
      if (operation === 'write') {
        const sizeCheck = await this.checkFileSize(absolutePath);
        if (!sizeCheck.allowed) {
          return sizeCheck;
        }
      }

      return {
        allowed: true,
        riskLevel: this.assessFileRisk(absolutePath, operation)
      };

    } catch (error) {
      logger.error('File access check failed', {
        filePath,
        operation,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        allowed: false,
        reason: 'Security check failed',
        riskLevel: 'high'
      };
    }
  }

  public async checkNetworkAccess(
    url: string,
    method: string,
    context: SandboxContext
  ): Promise<SecurityCheck> {
    if (!context.sandboxEnabled) {
      return { allowed: true, riskLevel: 'low' };
    }

    try {
      const parsedUrl = new URL(url);
      
      // Check sandbox manager rules
      const allowed = this.sandboxManager.checkNetworkAccess(url, method, context.userId);
      if (!allowed) {
        return {
          allowed: false,
          reason: 'Network access denied by sandbox policy',
          riskLevel: 'medium'
        };
      }

      // Check for dangerous URLs
      const dangerousCheck = this.checkDangerousUrl(parsedUrl);
      if (!dangerousCheck.allowed) {
        return dangerousCheck;
      }

      // Check protocol
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        return {
          allowed: false,
          reason: `Unsupported protocol: ${parsedUrl.protocol}`,
          riskLevel: 'high'
        };
      }

      return {
        allowed: true,
        riskLevel: this.assessNetworkRisk(parsedUrl, method)
      };

    } catch (error) {
      return {
        allowed: false,
        reason: 'Invalid URL or network access check failed',
        riskLevel: 'medium'
      };
    }
  }

  public async checkCommandExecution(
    command: string,
    args: string[],
    context: SandboxContext
  ): Promise<SecurityCheck> {
    if (!context.sandboxEnabled) {
      return { allowed: true, riskLevel: 'low' };
    }

    try {
      // Check sandbox manager rules
      const allowed = this.sandboxManager.checkProcessSpawning(command, args, context.userId);
      if (!allowed) {
        return {
          allowed: false,
          reason: 'Command execution denied by sandbox policy',
          riskLevel: 'high'
        };
      }

      // Check for dangerous commands
      const dangerousCheck = this.checkDangerousCommand(command, args);
      if (!dangerousCheck.allowed) {
        return dangerousCheck;
      }

      return {
        allowed: true,
        riskLevel: this.assessCommandRisk(command, args)
      };

    } catch (error) {
      return {
        allowed: false,
        reason: 'Command execution check failed',
        riskLevel: 'high'
      };
    }
  }

  public async requestApproval(
    request: ApprovalRequest,
    context: SandboxContext
  ): Promise<boolean> {
    // Auto-approve based on approval level
    if (context.approvalLevel === 'yolo') {
      logger.warn('Auto-approving due to yolo mode', { request: request.description });
      return true;
    }

    if (context.approvalLevel === 'auto-edit' && request.riskLevel === 'low') {
      logger.info('Auto-approving low-risk operation', { request: request.description });
      return true;
    }

    // Check for registered approval handler
    const handler = this.approvalHandlers.get(context.userId);
    if (handler) {
      try {
        return await handler(request);
      } catch (error) {
        logger.error('Approval handler failed', {
          userId: context.userId,
          error: error instanceof Error ? error.message : String(error)
        });
        return false;
      }
    }

    // Default: deny if no handler
    logger.warn('No approval handler registered, denying request', {
      userId: context.userId,
      request: request.description
    });
    return false;
  }

  public registerApprovalHandler(
    userId: string,
    handler: (request: ApprovalRequest) => Promise<boolean>
  ): void {
    this.approvalHandlers.set(userId, handler);
  }

  public unregisterApprovalHandler(userId: string): void {
    this.approvalHandlers.delete(userId);
  }

  private checkSensitiveFile(filePath: string, operation: string): SecurityCheck {
    const sensitivePatterns = [
      /\/etc\/passwd$/,
      /\/etc\/shadow$/,
      /\/etc\/sudoers$/,
      /\.ssh\/id_rsa$/,
      /\.ssh\/id_ed25519$/,
      /\.env$/,
      /\.secret$/,
      /password/i,
      /secret/i,
      /private.*key/i,
      /\.pem$/,
      /\.key$/
    ];

    const relativePath = path.relative(process.cwd(), filePath);
    const isSensitive = sensitivePatterns.some(pattern => pattern.test(relativePath));

    if (isSensitive && operation !== 'read') {
      return {
        allowed: false,
        reason: 'Access to sensitive file requires explicit approval',
        requiresApproval: true,
        riskLevel: 'high'
      };
    }

    return { allowed: true, riskLevel: isSensitive ? 'medium' : 'low' };
  }

  private async checkFileSize(filePath: string): Promise<SecurityCheck> {
    try {
      const stats = await fs.stat(filePath);
      const maxSize = 100 * 1024 * 1024; // 100MB

      if (stats.size > maxSize) {
        return {
          allowed: false,
          reason: `File too large: ${this.formatSize(stats.size)} (max: ${this.formatSize(maxSize)})`,
          riskLevel: 'medium'
        };
      }

      return { allowed: true, riskLevel: 'low' };
    } catch {
      // File doesn't exist, which is fine for write operations
      return { allowed: true, riskLevel: 'low' };
    }
  }

  private checkDangerousUrl(url: URL): SecurityCheck {
    // Check for localhost and private IPs
    const hostname = url.hostname.toLowerCase();
    
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
      return {
        allowed: false,
        reason: 'Access to localhost is not allowed',
        riskLevel: 'high'
      };
    }

    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^169\.254\./, // Link-local
    ];

    if (privateRanges.some(range => range.test(hostname))) {
      return {
        allowed: false,
        reason: 'Access to private IP addresses is not allowed',
        riskLevel: 'high'
      };
    }

    return { allowed: true, riskLevel: 'low' };
  }

  private checkDangerousCommand(command: string, args: string[]): SecurityCheck {
    const dangerousCommands = [
      'rm', 'del', 'format', 'fdisk', 'mkfs',
      'sudo', 'su', 'chmod', 'chown',
      'curl', 'wget', 'nc', 'netcat',
      'python', 'node', 'ruby', 'perl'
    ];

    const baseCommand = path.basename(command).toLowerCase();
    
    if (dangerousCommands.includes(baseCommand)) {
      // Check for dangerous arguments
      const fullCommand = `${command} ${args.join(' ')}`;
      
      if (fullCommand.includes('rm -rf') || fullCommand.includes('del /s')) {
        return {
          allowed: false,
          reason: 'Destructive file operations are not allowed',
          riskLevel: 'high'
        };
      }
    }

    return { allowed: true, riskLevel: 'low' };
  }

  private assessFileRisk(filePath: string, operation: string): 'low' | 'medium' | 'high' {
    const relativePath = path.relative(process.cwd(), filePath);
    
    // High risk: system files, executables, sensitive files
    if (relativePath.includes('/etc/') || 
        relativePath.includes('/sys/') ||
        relativePath.includes('/proc/') ||
        relativePath.endsWith('.exe') ||
        relativePath.endsWith('.sh') ||
        relativePath.endsWith('.bat')) {
      return 'high';
    }

    // Medium risk: write operations, hidden files
    if (operation === 'write' || operation === 'delete' || relativePath.startsWith('.')) {
      return 'medium';
    }

    return 'low';
  }

  private assessNetworkRisk(url: URL, method: string): 'low' | 'medium' | 'high' {
    // High risk: non-HTTPS, unusual ports
    if (url.protocol !== 'https:' || (url.port && !['80', '443', '8080', '8443'].includes(url.port))) {
      return 'high';
    }

    // Medium risk: POST/PUT requests
    if (['POST', 'PUT', 'DELETE'].includes(method.toUpperCase())) {
      return 'medium';
    }

    return 'low';
  }

  private assessCommandRisk(command: string, args: string[]): 'low' | 'medium' | 'high' {
    const baseCommand = path.basename(command).toLowerCase();
    const fullCommand = `${command} ${args.join(' ')}`;

    // High risk: system modification commands
    const highRiskCommands = ['sudo', 'su', 'chmod', 'chown', 'rm', 'del'];
    if (highRiskCommands.includes(baseCommand)) {
      return 'high';
    }

    // Medium risk: network commands, interpreters
    const mediumRiskCommands = ['curl', 'wget', 'python', 'node', 'ruby'];
    if (mediumRiskCommands.includes(baseCommand)) {
      return 'medium';
    }

    return 'low';
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)}${units[unitIndex]}`;
  }

  public getSandboxManager(): SandboxManager {
    return this.sandboxManager;
  }
}

export interface ApprovalRequest {
  description: string;
  operation: string;
  resource: string;
  riskLevel: 'low' | 'medium' | 'high';
  context: Record<string, any>;
}

// Global sandbox instance
let globalSandbox: SecuritySandbox | undefined;

export function getGlobalSandbox(): SecuritySandbox {
  if (!globalSandbox) {
    globalSandbox = new SecuritySandbox();
  }
  return globalSandbox;
}

export function setGlobalSandbox(sandbox: SecuritySandbox): void {
  globalSandbox = sandbox;
}
