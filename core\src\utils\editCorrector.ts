// Intelligent edit correction and validation
import { diffLines, diffChars, Change } from 'diff';
import { logger } from '../core/logger.js';

export interface EditValidationResult {
  isValid: boolean;
  confidence: number;
  issues: EditIssue[];
  suggestions: EditSuggestion[];
  correctedEdit?: CorrectedEdit;
}

export interface EditIssue {
  type: 'syntax' | 'logic' | 'formatting' | 'context' | 'safety';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  line?: number;
  column?: number;
  suggestion?: string;
}

export interface EditSuggestion {
  type: 'improvement' | 'alternative' | 'optimization' | 'safety';
  description: string;
  before: string;
  after: string;
  confidence: number;
}

export interface CorrectedEdit {
  originalContent: string;
  correctedContent: string;
  changes: Change[];
  explanation: string;
}

export interface EditContext {
  language?: string;
  filePath?: string;
  projectType?: string;
  surroundingLines?: string[];
  indentationStyle?: 'spaces' | 'tabs';
  indentationSize?: number;
}

export class EditCorrector {
  private languagePatterns: Record<string, RegExp[]> = {
    javascript: [
      /function\s+\w+\s*\(/,
      /const\s+\w+\s*=/,
      /let\s+\w+\s*=/,
      /var\s+\w+\s*=/,
      /class\s+\w+/,
      /import\s+.*from/,
      /export\s+(default\s+)?/
    ],
    typescript: [
      /interface\s+\w+/,
      /type\s+\w+\s*=/,
      /enum\s+\w+/,
      /:\s*\w+(\[\])?/,
      /function\s+\w+\s*\(/,
      /const\s+\w+\s*:/
    ],
    python: [
      /def\s+\w+\s*\(/,
      /class\s+\w+/,
      /import\s+\w+/,
      /from\s+\w+\s+import/,
      /if\s+__name__\s*==\s*['"']__main__['"']/
    ],
    java: [
      /public\s+class\s+\w+/,
      /private\s+\w+\s+\w+/,
      /public\s+static\s+void\s+main/,
      /package\s+[\w.]+/,
      /import\s+[\w.]+/
    ]
  };

  public async validateEdit(
    originalContent: string,
    modifiedContent: string,
    context: EditContext = {}
  ): Promise<EditValidationResult> {
    const issues: EditIssue[] = [];
    const suggestions: EditSuggestion[] = [];
    let confidence = 1.0;

    try {
      // Detect language if not provided
      const language = context.language || this.detectLanguage(originalContent, context.filePath);
      
      // Validate syntax
      const syntaxIssues = this.validateSyntax(modifiedContent, language);
      issues.push(...syntaxIssues);

      // Check formatting consistency
      const formattingIssues = this.validateFormatting(originalContent, modifiedContent, context);
      issues.push(...formattingIssues);

      // Validate context preservation
      const contextIssues = this.validateContext(originalContent, modifiedContent, context);
      issues.push(...contextIssues);

      // Check for potential safety issues
      const safetyIssues = this.validateSafety(modifiedContent, language);
      issues.push(...safetyIssues);

      // Generate suggestions
      const editSuggestions = this.generateSuggestions(originalContent, modifiedContent, language);
      suggestions.push(...editSuggestions);

      // Calculate confidence based on issues
      confidence = this.calculateConfidence(issues);

      // Generate corrected edit if needed
      let correctedEdit: CorrectedEdit | undefined;
      if (confidence < 0.8 || issues.some(i => i.severity === 'high' || i.severity === 'critical')) {
        correctedEdit = this.generateCorrectedEdit(originalContent, modifiedContent, issues, context);
      }

      return {
        isValid: confidence > 0.6 && !issues.some(i => i.severity === 'critical'),
        confidence,
        issues,
        suggestions,
        correctedEdit
      };
    } catch (error) {
      logger.error('Edit validation failed', { error });
      return {
        isValid: false,
        confidence: 0,
        issues: [{
          type: 'syntax',
          severity: 'critical',
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`
        }],
        suggestions: []
      };
    }
  }

  private detectLanguage(content: string, filePath?: string): string {
    // Try to detect from file extension
    if (filePath) {
      const ext = filePath.split('.').pop()?.toLowerCase();
      const extensionMap: Record<string, string> = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'py': 'python',
        'java': 'java',
        'cpp': 'cpp',
        'c': 'c',
        'cs': 'csharp',
        'go': 'go',
        'rs': 'rust',
        'php': 'php',
        'rb': 'ruby'
      };
      
      if (ext && extensionMap[ext]) {
        return extensionMap[ext];
      }
    }

    // Try to detect from content patterns
    for (const [language, patterns] of Object.entries(this.languagePatterns)) {
      const matches = patterns.filter(pattern => pattern.test(content)).length;
      if (matches > 0) {
        return language;
      }
    }

    return 'text';
  }

  private validateSyntax(content: string, language: string): EditIssue[] {
    const issues: EditIssue[] = [];

    try {
      switch (language) {
        case 'javascript':
        case 'typescript':
          issues.push(...this.validateJavaScriptSyntax(content));
          break;
        case 'python':
          issues.push(...this.validatePythonSyntax(content));
          break;
        case 'java':
          issues.push(...this.validateJavaSyntax(content));
          break;
      }
    } catch (error) {
      issues.push({
        type: 'syntax',
        severity: 'high',
        message: `Syntax validation failed: ${error instanceof Error ? error.message : String(error)}`
      });
    }

    return issues;
  }

  private validateJavaScriptSyntax(content: string): EditIssue[] {
    const issues: EditIssue[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // Check for common syntax issues
      if (line.includes('function') && !line.includes('(')) {
        issues.push({
          type: 'syntax',
          severity: 'medium',
          message: 'Function declaration missing parentheses',
          line: lineNum,
          suggestion: 'Add parentheses after function name'
        });
      }

      // Check for unmatched brackets
      const openBrackets = (line.match(/[{[(]/g) || []).length;
      const closeBrackets = (line.match(/[}\])]/g) || []).length;
      
      if (openBrackets !== closeBrackets && line.trim() !== '') {
        issues.push({
          type: 'syntax',
          severity: 'low',
          message: 'Potentially unmatched brackets',
          line: lineNum,
          suggestion: 'Check bracket matching'
        });
      }
    });

    return issues;
  }

  private validatePythonSyntax(content: string): EditIssue[] {
    const issues: EditIssue[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // Check indentation
      if (line.trim() && !line.startsWith(' ') && !line.startsWith('\t') && line.includes(':')) {
        const nextLine = lines[index + 1];
        if (nextLine && !nextLine.startsWith(' ') && !nextLine.startsWith('\t')) {
          issues.push({
            type: 'syntax',
            severity: 'medium',
            message: 'Missing indentation after colon',
            line: lineNum + 1,
            suggestion: 'Add proper indentation'
          });
        }
      }
    });

    return issues;
  }

  private validateJavaSyntax(content: string): EditIssue[] {
    const issues: EditIssue[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNum = index + 1;
      
      // Check for missing semicolons
      if (line.trim() && 
          !line.trim().endsWith(';') && 
          !line.trim().endsWith('{') && 
          !line.trim().endsWith('}') &&
          !line.trim().startsWith('//') &&
          !line.trim().startsWith('/*') &&
          !line.includes('class ') &&
          !line.includes('interface ') &&
          !line.includes('enum ')) {
        issues.push({
          type: 'syntax',
          severity: 'low',
          message: 'Potentially missing semicolon',
          line: lineNum,
          suggestion: 'Add semicolon at end of statement'
        });
      }
    });

    return issues;
  }

  private validateFormatting(
    originalContent: string,
    modifiedContent: string,
    context: EditContext
  ): EditIssue[] {
    const issues: EditIssue[] = [];

    // Check indentation consistency
    const originalIndent = this.detectIndentation(originalContent);
    const modifiedIndent = this.detectIndentation(modifiedContent);

    if (originalIndent.style !== modifiedIndent.style) {
      issues.push({
        type: 'formatting',
        severity: 'low',
        message: `Indentation style changed from ${originalIndent.style} to ${modifiedIndent.style}`,
        suggestion: `Use consistent ${originalIndent.style} indentation`
      });
    }

    if (originalIndent.size !== modifiedIndent.size) {
      issues.push({
        type: 'formatting',
        severity: 'low',
        message: `Indentation size changed from ${originalIndent.size} to ${modifiedIndent.size}`,
        suggestion: `Use consistent ${originalIndent.size}-character indentation`
      });
    }

    return issues;
  }

  private validateContext(
    originalContent: string,
    modifiedContent: string,
    context: EditContext
  ): EditIssue[] {
    const issues: EditIssue[] = [];

    // Check if important context was removed
    const originalLines = originalContent.split('\n');
    const modifiedLines = modifiedContent.split('\n');

    // Look for removed imports/includes
    const originalImports = originalLines.filter(line => 
      line.includes('import ') || line.includes('#include') || line.includes('require(')
    );
    const modifiedImports = modifiedLines.filter(line => 
      line.includes('import ') || line.includes('#include') || line.includes('require(')
    );

    if (originalImports.length > modifiedImports.length) {
      issues.push({
        type: 'context',
        severity: 'medium',
        message: 'Some imports/includes were removed',
        suggestion: 'Verify that removed imports are not needed'
      });
    }

    return issues;
  }

  private validateSafety(content: string, language: string): EditIssue[] {
    const issues: EditIssue[] = [];

    // Check for potentially dangerous patterns
    const dangerousPatterns = [
      { pattern: /eval\s*\(/, message: 'Use of eval() can be dangerous' },
      { pattern: /innerHTML\s*=/, message: 'Direct innerHTML assignment can lead to XSS' },
      { pattern: /document\.write\s*\(/, message: 'document.write() can be problematic' },
      { pattern: /exec\s*\(/, message: 'exec() can be dangerous with user input' },
      { pattern: /system\s*\(/, message: 'system() calls can be dangerous' }
    ];

    const lines = content.split('\n');
    lines.forEach((line, index) => {
      dangerousPatterns.forEach(({ pattern, message }) => {
        if (pattern.test(line)) {
          issues.push({
            type: 'safety',
            severity: 'medium',
            message,
            line: index + 1,
            suggestion: 'Consider safer alternatives'
          });
        }
      });
    });

    return issues;
  }

  private generateSuggestions(
    originalContent: string,
    modifiedContent: string,
    language: string
  ): EditSuggestion[] {
    const suggestions: EditSuggestion[] = [];

    // Analyze the diff to understand what changed
    const changes = diffLines(originalContent, modifiedContent);
    
    changes.forEach(change => {
      if (change.added && change.value.trim()) {
        // Suggest improvements for added content
        if (language === 'javascript' || language === 'typescript') {
          if (change.value.includes('var ')) {
            suggestions.push({
              type: 'improvement',
              description: 'Consider using const or let instead of var',
              before: change.value,
              after: change.value.replace(/var /g, 'const '),
              confidence: 0.8
            });
          }
        }
      }
    });

    return suggestions;
  }

  private calculateConfidence(issues: EditIssue[]): number {
    let confidence = 1.0;

    issues.forEach(issue => {
      switch (issue.severity) {
        case 'critical':
          confidence -= 0.5;
          break;
        case 'high':
          confidence -= 0.3;
          break;
        case 'medium':
          confidence -= 0.1;
          break;
        case 'low':
          confidence -= 0.05;
          break;
      }
    });

    return Math.max(0, confidence);
  }

  private generateCorrectedEdit(
    originalContent: string,
    modifiedContent: string,
    issues: EditIssue[],
    context: EditContext
  ): CorrectedEdit {
    let correctedContent = modifiedContent;
    const changes: Change[] = [];
    const explanations: string[] = [];

    // Apply corrections based on issues
    issues.forEach(issue => {
      if (issue.suggestion && issue.line) {
        const lines = correctedContent.split('\n');
        if (lines[issue.line - 1]) {
          // Apply simple corrections
          if (issue.type === 'syntax' && issue.message.includes('semicolon')) {
            lines[issue.line - 1] += ';';
            explanations.push(`Added missing semicolon on line ${issue.line}`);
          }
        }
        correctedContent = lines.join('\n');
      }
    });

    return {
      originalContent,
      correctedContent,
      changes: diffLines(modifiedContent, correctedContent),
      explanation: explanations.join('; ')
    };
  }

  private detectIndentation(content: string): { style: 'spaces' | 'tabs'; size: number } {
    const lines = content.split('\n');
    let spaceCount = 0;
    let tabCount = 0;
    let spaceSizes: number[] = [];

    lines.forEach(line => {
      if (line.startsWith(' ')) {
        spaceCount++;
        const leadingSpaces = line.match(/^ +/)?.[0].length || 0;
        if (leadingSpaces > 0) {
          spaceSizes.push(leadingSpaces);
        }
      } else if (line.startsWith('\t')) {
        tabCount++;
      }
    });

    const style = tabCount > spaceCount ? 'tabs' : 'spaces';
    const size = style === 'tabs' ? 1 : this.findCommonIndentSize(spaceSizes);

    return { style, size };
  }

  private findCommonIndentSize(sizes: number[]): number {
    if (sizes.length === 0) return 2;

    // Find the most common indent size
    const counts: Record<number, number> = {};
    sizes.forEach(size => {
      counts[size] = (counts[size] || 0) + 1;
    });

    let maxCount = 0;
    let commonSize = 2;
    Object.entries(counts).forEach(([size, count]) => {
      if (count > maxCount) {
        maxCount = count;
        commonSize = parseInt(size);
      }
    });

    return commonSize;
  }
}
