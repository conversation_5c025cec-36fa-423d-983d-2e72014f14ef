// Model Context Protocol client implementation
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { withRetry } from '../utils/retry.js';

export interface MCPServer {
  name: string;
  command: string;
  args?: string[];
  env?: Record<string, string>;
  cwd?: string;
  timeout?: number;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

export interface MCPPrompt {
  name: string;
  description: string;
  arguments?: any[];
}

export interface MCPCapabilities {
  tools?: {
    listChanged?: boolean;
  };
  resources?: {
    subscribe?: boolean;
    listChanged?: boolean;
  };
  prompts?: {
    listChanged?: boolean;
  };
  logging?: {};
}

export interface MCPClientOptions {
  timeout?: number;
  retries?: number;
  debug?: boolean;
}

export class MCPClient {
  private servers = new Map<string, MCPServerConnection>();
  private options: MCPClientOptions;

  constructor(options: MCPClientOptions = {}) {
    this.options = {
      timeout: 30000,
      retries: 3,
      debug: false,
      ...options
    };
  }

  public async connectToServer(serverConfig: MCPServer): Promise<void> {
    logger.info('Connecting to MCP server', { name: serverConfig.name });

    try {
      const connection = new MCPServerConnection(serverConfig, this.options);
      await connection.connect();
      
      this.servers.set(serverConfig.name, connection);
      
      logger.info('Successfully connected to MCP server', { name: serverConfig.name });
    } catch (error) {
      logger.error('Failed to connect to MCP server', { 
        name: serverConfig.name, 
        error 
      });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        `Failed to connect to MCP server: ${serverConfig.name}`,
        { serverConfig, error }
      );
    }
  }

  public async disconnectFromServer(serverName: string): Promise<void> {
    const connection = this.servers.get(serverName);
    if (connection) {
      await connection.disconnect();
      this.servers.delete(serverName);
      logger.info('Disconnected from MCP server', { name: serverName });
    }
  }

  public async disconnectAll(): Promise<void> {
    const disconnectPromises = Array.from(this.servers.entries()).map(
      ([name, connection]) => this.disconnectFromServer(name)
    );
    await Promise.all(disconnectPromises);
  }

  public async listTools(serverName?: string): Promise<MCPTool[]> {
    if (serverName) {
      const connection = this.servers.get(serverName);
      if (!connection) {
        throw new ArienError(
          ErrorCode.TOOL_ERROR,
          `MCP server not found: ${serverName}`
        );
      }
      return connection.listTools();
    }

    // List tools from all connected servers
    const allTools: MCPTool[] = [];
    for (const [name, connection] of this.servers) {
      try {
        const tools = await connection.listTools();
        allTools.push(...tools.map(tool => ({
          ...tool,
          name: `${name}:${tool.name}` // Prefix with server name
        })));
      } catch (error) {
        logger.warn('Failed to list tools from MCP server', { 
          serverName: name, 
          error 
        });
      }
    }
    return allTools;
  }

  public async callTool(
    toolName: string, 
    arguments_: any,
    serverName?: string
  ): Promise<any> {
    let targetServer: string;
    let actualToolName: string;

    if (serverName) {
      targetServer = serverName;
      actualToolName = toolName;
    } else {
      // Parse server:tool format
      const parts = toolName.split(':');
      if (parts.length === 2) {
        [targetServer, actualToolName] = parts;
      } else {
        // Try to find the tool in any server
        for (const [name, connection] of this.servers) {
          const tools = await connection.listTools();
          if (tools.some(tool => tool.name === toolName)) {
            targetServer = name;
            actualToolName = toolName;
            break;
          }
        }
        if (!targetServer!) {
          throw new ArienError(
            ErrorCode.TOOL_NOT_FOUND,
            `Tool not found: ${toolName}`
          );
        }
      }
    }

    const connection = this.servers.get(targetServer);
    if (!connection) {
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        `MCP server not found: ${targetServer}`
      );
    }

    return withRetry(
      () => connection.callTool(actualToolName, arguments_),
      { maxAttempts: this.options.retries || 3 }
    );
  }

  public async listResources(serverName?: string): Promise<MCPResource[]> {
    if (serverName) {
      const connection = this.servers.get(serverName);
      if (!connection) {
        throw new ArienError(
          ErrorCode.TOOL_ERROR,
          `MCP server not found: ${serverName}`
        );
      }
      return connection.listResources();
    }

    // List resources from all connected servers
    const allResources: MCPResource[] = [];
    for (const [name, connection] of this.servers) {
      try {
        const resources = await connection.listResources();
        allResources.push(...resources);
      } catch (error) {
        logger.warn('Failed to list resources from MCP server', { 
          serverName: name, 
          error 
        });
      }
    }
    return allResources;
  }

  public async readResource(uri: string, serverName?: string): Promise<any> {
    if (serverName) {
      const connection = this.servers.get(serverName);
      if (!connection) {
        throw new ArienError(
          ErrorCode.TOOL_ERROR,
          `MCP server not found: ${serverName}`
        );
      }
      return connection.readResource(uri);
    }

    // Try to read from any server that has this resource
    for (const [name, connection] of this.servers) {
      try {
        const resources = await connection.listResources();
        if (resources.some(resource => resource.uri === uri)) {
          return connection.readResource(uri);
        }
      } catch (error) {
        logger.debug('Failed to check resources from MCP server', { 
          serverName: name, 
          error 
        });
      }
    }

    throw new ArienError(
      ErrorCode.TOOL_ERROR,
      `Resource not found: ${uri}`
    );
  }

  public getConnectedServers(): string[] {
    return Array.from(this.servers.keys());
  }

  public isServerConnected(serverName: string): boolean {
    return this.servers.has(serverName);
  }
}

class MCPServerConnection {
  private process?: any; // Child process
  private connected = false;
  private messageId = 0;
  private pendingRequests = new Map<number, { resolve: Function; reject: Function }>();

  constructor(
    private config: MCPServer,
    private options: MCPClientOptions
  ) {}

  public async connect(): Promise<void> {
    // Implementation would spawn the MCP server process and establish communication
    // This is a simplified version - full implementation would handle stdio/JSON-RPC
    logger.debug('Connecting to MCP server', { config: this.config });
    
    // Simulate connection for now
    this.connected = true;
  }

  public async disconnect(): Promise<void> {
    if (this.process) {
      this.process.kill();
    }
    this.connected = false;
  }

  public async listTools(): Promise<MCPTool[]> {
    return this.sendRequest('tools/list', {});
  }

  public async callTool(name: string, arguments_: any): Promise<any> {
    return this.sendRequest('tools/call', { name, arguments: arguments_ });
  }

  public async listResources(): Promise<MCPResource[]> {
    return this.sendRequest('resources/list', {});
  }

  public async readResource(uri: string): Promise<any> {
    return this.sendRequest('resources/read', { uri });
  }

  private async sendRequest(method: string, params: any): Promise<any> {
    if (!this.connected) {
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        'MCP server not connected'
      );
    }

    const id = ++this.messageId;
    const message = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      
      // Simulate sending message and receiving response
      setTimeout(() => {
        const request = this.pendingRequests.get(id);
        if (request) {
          this.pendingRequests.delete(id);
          // Simulate response based on method
          if (method === 'tools/list') {
            request.resolve([]);
          } else if (method === 'resources/list') {
            request.resolve([]);
          } else {
            request.resolve({ result: 'success' });
          }
        }
      }, 100);
    });
  }
}
