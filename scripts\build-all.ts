#!/usr/bin/env tsx
// Build script for all packages
import { spawn } from 'child_process';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

interface BuildResult {
  package: string;
  success: boolean;
  duration: number;
  output: string;
  error?: string;
}

class BuildManager {
  private results: BuildResult[] = [];

  async buildAll(): Promise<void> {
    console.log('🏗️  Building Arien CLI Project\n');

    // Build order matters - core first, then cli
    const packages = [
      { name: 'core', path: 'core' },
      { name: 'cli', path: 'cli' }
    ];

    for (const pkg of packages) {
      await this.buildPackage(pkg.name, pkg.path);
    }

    this.printResults();
  }

  private async buildPackage(name: string, packagePath: string): Promise<void> {
    const startTime = Date.now();
    console.log(`📦 Building ${name}...`);

    try {
      const result = await this.runCommand('npm', ['run', 'build'], path.join(rootDir, packagePath));
      
      this.results.push({
        package: name,
        success: true,
        duration: Date.now() - startTime,
        output: result.stdout
      });

      console.log(`✅ ${name} built successfully (${Date.now() - startTime}ms)`);

    } catch (error) {
      this.results.push({
        package: name,
        success: false,
        duration: Date.now() - startTime,
        output: '',
        error: error instanceof Error ? error.message : String(error)
      });

      console.log(`❌ ${name} build failed (${Date.now() - startTime}ms)`);
      console.log(`   Error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private runCommand(command: string, args: string[], cwd: string): Promise<{ stdout: string; stderr: string }> {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, {
        cwd,
        stdio: 'pipe',
        shell: true
      });

      let stdout = '';
      let stderr = '';

      process.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr || stdout}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  private printResults(): void {
    console.log('\n📊 Build Results Summary\n');
    
    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const totalTime = this.results.reduce((sum, r) => sum + r.duration, 0);

    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏱️  Total Time: ${totalTime}ms`);
    console.log(`📈 Success Rate: ${((successful / this.results.length) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Builds:');
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  • ${r.package}: ${r.error}`);
        });
      
      console.log('\n⚠️  Build failed! Please fix the errors above.');
      process.exit(1);
    } else {
      console.log('\n🎉 All packages built successfully!');
      console.log('🚀 Project is ready for deployment!');
    }
  }
}

// Run the build
const buildManager = new BuildManager();
buildManager.buildAll().catch(error => {
  console.error('❌ Build process failed:', error);
  process.exit(1);
});
