// Path manipulation utilities
import { resolve, join, dirname, basename, extname, relative, isAbsolute, normalize } from 'path';
import { homedir, tmpdir } from 'os';

export function resolvePath(path: string, basePath?: string): string {
  if (isAbsolute(path)) {
    return normalize(path);
  }
  
  const base = basePath || process.cwd();
  return resolve(base, path);
}

export function getHomePath(): string {
  return homedir();
}

export function getTempPath(): string {
  return tmpdir();
}

export function getConfigPath(): string {
  return join(getHomePath(), '.arien');
}

export function getLogPath(): string {
  return join(getConfigPath(), 'logs');
}

export function getCachePath(): string {
  return join(getConfigPath(), 'cache');
}

export function joinPaths(...paths: string[]): string {
  return join(...paths);
}

export function getParentPath(path: string): string {
  return dirname(path);
}

export function getFileName(path: string): string {
  return basename(path);
}

export function getFileExtension(path: string): string {
  return extname(path);
}

export function getRelativePath(from: string, to: string): string {
  return relative(from, to);
}

export function normalizePath(path: string): string {
  return normalize(path);
}

export function isPathAbsolute(path: string): boolean {
  return isAbsolute(path);
}
