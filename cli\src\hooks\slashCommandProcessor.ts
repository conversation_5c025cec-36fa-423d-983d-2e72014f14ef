// / commands processor (CLI commands)
import { useState, useCallback } from 'react';
import { getConfig } from '@arien/core';

export interface SlashCommand {
  name: string;
  description: string;
  usage: string;
  aliases?: string[];
  handler: (args: string[], context: CommandContext) => Promise<CommandResult>;
}

export interface CommandContext {
  workingDirectory: string;
  userId: string;
  sessionId: string;
  config: any;
}

export interface CommandResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface UseSlashCommandProcessorReturn {
  processSlashCommand: (command: string, context?: Partial<CommandContext>) => Promise<CommandResult>;
  getAvailableCommands: () => SlashCommand[];
  getCommandHelp: (commandName: string) => string | null;
  isProcessing: boolean;
  lastResult: CommandResult | null;
}

export function useSlashCommandProcessor(): UseSlashCommandProcessorReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<CommandResult | null>(null);

  // Built-in slash commands
  const commands: SlashCommand[] = [
    {
      name: 'help',
      description: 'Show available commands or help for a specific command',
      usage: '/help [command]',
      aliases: ['h', '?'],
      handler: async (args) => {
        if (args.length === 0) {
          const commandList = commands.map(cmd => 
            `/${cmd.name} - ${cmd.description}`
          ).join('\n');
          
          return {
            success: true,
            message: `Available commands:\n${commandList}\n\nUse /help <command> for detailed help.`
          };
        }

        const commandName = args[0];
        const command = commands.find(cmd => 
          cmd.name === commandName || cmd.aliases?.includes(commandName)
        );

        if (!command) {
          return {
            success: false,
            message: `Unknown command: ${commandName}`,
            error: 'Command not found'
          };
        }

        return {
          success: true,
          message: `${command.name}: ${command.description}\nUsage: ${command.usage}`
        };
      }
    },
    {
      name: 'clear',
      description: 'Clear the chat history',
      usage: '/clear',
      aliases: ['cls'],
      handler: async () => {
        return {
          success: true,
          message: 'Chat history cleared',
          data: { action: 'clear_history' }
        };
      }
    },
    {
      name: 'config',
      description: 'Show or modify configuration',
      usage: '/config [get|set|list] [key] [value]',
      aliases: ['cfg'],
      handler: async (args, context) => {
        const config = getConfig();
        
        if (args.length === 0 || args[0] === 'list') {
          const configData = config.getConfig();
          const configStr = Object.entries(configData)
            .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
            .join('\n');
          
          return {
            success: true,
            message: `Current configuration:\n${configStr}`
          };
        }

        const action = args[0];
        
        if (action === 'get') {
          if (args.length < 2) {
            return {
              success: false,
              message: 'Usage: /config get <key>',
              error: 'Missing key parameter'
            };
          }
          
          const key = args[1];
          const value = config.get(key);
          
          return {
            success: true,
            message: `${key}: ${JSON.stringify(value)}`
          };
        }

        if (action === 'set') {
          if (args.length < 3) {
            return {
              success: false,
              message: 'Usage: /config set <key> <value>',
              error: 'Missing key or value parameter'
            };
          }
          
          const key = args[1];
          const value = args.slice(2).join(' ');
          
          try {
            // Try to parse as JSON, fallback to string
            let parsedValue;
            try {
              parsedValue = JSON.parse(value);
            } catch {
              parsedValue = value;
            }
            
            config.set(key, parsedValue);
            
            return {
              success: true,
              message: `Set ${key} to ${JSON.stringify(parsedValue)}`
            };
          } catch (error) {
            return {
              success: false,
              message: `Failed to set ${key}`,
              error: error instanceof Error ? error.message : String(error)
            };
          }
        }

        return {
          success: false,
          message: `Unknown config action: ${action}`,
          error: 'Invalid action'
        };
      }
    },
    {
      name: 'status',
      description: 'Show system status and statistics',
      usage: '/status',
      aliases: ['stat'],
      handler: async (args, context) => {
        const config = getConfig();
        const configData = config.getConfig();
        
        const status = {
          provider: configData.defaultProvider,
          model: configData.defaultModel,
          workingDirectory: context.workingDirectory,
          sessionId: context.sessionId,
          userId: context.userId
        };

        const statusStr = Object.entries(status)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');

        return {
          success: true,
          message: `System Status:\n${statusStr}`,
          data: status
        };
      }
    },
    {
      name: 'theme',
      description: 'Change the UI theme',
      usage: '/theme [theme-name]',
      handler: async (args) => {
        if (args.length === 0) {
          return {
            success: true,
            message: 'Available themes: default, dark, light, cyberpunk, matrix\nUsage: /theme <theme-name>'
          };
        }

        const themeName = args[0];
        const availableThemes = ['default', 'dark', 'light', 'cyberpunk', 'matrix'];
        
        if (!availableThemes.includes(themeName)) {
          return {
            success: false,
            message: `Unknown theme: ${themeName}. Available: ${availableThemes.join(', ')}`,
            error: 'Invalid theme'
          };
        }

        return {
          success: true,
          message: `Theme changed to ${themeName}`,
          data: { action: 'change_theme', theme: themeName }
        };
      }
    },
    {
      name: 'exit',
      description: 'Exit the application',
      usage: '/exit',
      aliases: ['quit', 'q'],
      handler: async () => {
        return {
          success: true,
          message: 'Goodbye!',
          data: { action: 'exit' }
        };
      }
    },
    {
      name: 'history',
      description: 'Show command history',
      usage: '/history [count]',
      aliases: ['hist'],
      handler: async (args) => {
        const count = args.length > 0 ? parseInt(args[0]) : 10;
        
        return {
          success: true,
          message: `Showing last ${count} commands from history`,
          data: { action: 'show_history', count }
        };
      }
    },
    {
      name: 'save',
      description: 'Save current conversation',
      usage: '/save [filename]',
      handler: async (args) => {
        const filename = args.length > 0 ? args[0] : `conversation-${Date.now()}.json`;
        
        return {
          success: true,
          message: `Conversation saved to ${filename}`,
          data: { action: 'save_conversation', filename }
        };
      }
    },
    {
      name: 'load',
      description: 'Load a saved conversation',
      usage: '/load <filename>',
      handler: async (args) => {
        if (args.length === 0) {
          return {
            success: false,
            message: 'Usage: /load <filename>',
            error: 'Missing filename parameter'
          };
        }

        const filename = args[0];
        
        return {
          success: true,
          message: `Loading conversation from ${filename}`,
          data: { action: 'load_conversation', filename }
        };
      }
    }
  ];

  const processSlashCommand = useCallback(async (
    command: string,
    context: Partial<CommandContext> = {}
  ): Promise<CommandResult> => {
    setIsProcessing(true);

    try {
      // Parse command
      const trimmed = command.trim();
      if (!trimmed.startsWith('/')) {
        return {
          success: false,
          message: 'Not a slash command',
          error: 'Commands must start with /'
        };
      }

      const parts = trimmed.slice(1).split(/\s+/);
      const commandName = parts[0];
      const args = parts.slice(1);

      // Find command
      const cmd = commands.find(c => 
        c.name === commandName || c.aliases?.includes(commandName)
      );

      if (!cmd) {
        return {
          success: false,
          message: `Unknown command: /${commandName}. Type /help for available commands.`,
          error: 'Command not found'
        };
      }

      // Execute command
      const fullContext: CommandContext = {
        workingDirectory: process.cwd(),
        userId: 'default',
        sessionId: 'default',
        config: {},
        ...context
      };

      const result = await cmd.handler(args, fullContext);
      setLastResult(result);
      return result;
    } catch (error) {
      const result: CommandResult = {
        success: false,
        message: 'Command execution failed',
        error: error instanceof Error ? error.message : String(error)
      };
      
      setLastResult(result);
      return result;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const getAvailableCommands = useCallback(() => {
    return [...commands];
  }, []);

  const getCommandHelp = useCallback((commandName: string): string | null => {
    const command = commands.find(cmd => 
      cmd.name === commandName || cmd.aliases?.includes(commandName)
    );
    
    if (!command) return null;
    
    return `${command.name}: ${command.description}\nUsage: ${command.usage}`;
  }, []);

  return {
    processSlashCommand,
    getAvailableCommands,
    getCommandHelp,
    isProcessing,
    lastResult
  };
}
