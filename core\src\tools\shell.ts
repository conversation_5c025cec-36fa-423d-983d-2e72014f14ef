// Shell command execution tool with security controls
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { resolve, isAbsolute } from 'path';
import { z } from 'zod';
import { BaseTool, ToolContext, ToolResult } from './tools.js';
import { logger } from '../core/logger.js';

const execAsync = promisify(exec);

const ShellCommandSchema = z.object({
  command: z.string().describe('Shell command to execute'),
  workingDirectory: z.string().optional().describe('Working directory for command execution'),
  timeout: z.number().optional().default(30000).describe('Command timeout in milliseconds'),
  captureOutput: z.boolean().optional().default(true).describe('Capture command output'),
  allowInteractive: z.boolean().optional().default(false).describe('Allow interactive commands'),
  environment: z.record(z.string()).optional().describe('Additional environment variables')
});

export class ShellTool extends BaseTool {
  private readonly dangerousCommands = [
    'rm -rf',
    'del /f /s /q',
    'format',
    'fdisk',
    'dd if=',
    'mkfs',
    'shutdown',
    'reboot',
    'halt',
    'poweroff',
    'init 0',
    'init 6',
    'sudo rm',
    'sudo dd',
    'sudo mkfs',
    'chmod 777',
    'chown -R',
    '> /dev/',
    'curl | sh',
    'wget | sh',
    'curl | bash',
    'wget | bash'
  ];

  constructor() {
    super({
      name: 'shell',
      description: 'Execute shell commands with security controls',
      parameters: ShellCommandSchema,
      requiresApproval: true,
      riskLevel: 'high',
      category: 'shell'
    });
  }

  private isDangerousCommand(command: string): boolean {
    const lowerCommand = command.toLowerCase();
    return this.dangerousCommands.some(dangerous => 
      lowerCommand.includes(dangerous.toLowerCase())
    );
  }

  private isCommandAllowed(command: string, context: ToolContext): boolean {
    // Extract the base command (first word)
    const baseCommand = command.trim().split(/\s+/)[0];
    
    // Check if command is in allowed list
    if (context.allowedCommands.length > 0) {
      return context.allowedCommands.some(allowed => 
        baseCommand === allowed || baseCommand.endsWith(`/${allowed}`) || baseCommand.endsWith(`\\${allowed}`)
      );
    }
    
    // If no allowed commands specified, allow all except dangerous ones
    return !this.isDangerousCommand(command);
  }

  async execute(params: z.infer<typeof ShellCommandSchema>, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const { command, workingDirectory, timeout, captureOutput, allowInteractive, environment } = validatedParams;

      logger.debug('Executing shell command', { command, workingDirectory, timeout });

      // Security checks
      if (!this.isCommandAllowed(command, context)) {
        return this.createErrorResult(
          `Command not allowed: ${command}. Check your security settings or allowed commands list.`
        );
      }

      if (this.isDangerousCommand(command)) {
        return this.createErrorResult(
          `Dangerous command detected: ${command}. This command is blocked for security reasons.`
        );
      }

      // Resolve working directory
      const cwd = workingDirectory 
        ? (isAbsolute(workingDirectory) ? workingDirectory : resolve(context.workingDirectory, workingDirectory))
        : context.workingDirectory;

      // Prepare environment
      const env = {
        ...process.env,
        ...environment
      };

      // Execute command
      if (allowInteractive) {
        return await this.executeInteractive(command, cwd, timeout, env);
      } else {
        return await this.executeNonInteractive(command, cwd, timeout, captureOutput, env);
      }
    } catch (error) {
      logger.error('Shell command execution failed', { error, params });
      return this.createErrorResult(`Command execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async executeNonInteractive(
    command: string,
    cwd: string,
    timeout: number,
    captureOutput: boolean,
    env: NodeJS.ProcessEnv
  ): Promise<ToolResult> {
    try {
      const startTime = Date.now();
      
      const { stdout, stderr } = await execAsync(command, {
        cwd,
        timeout,
        env,
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
        encoding: 'utf8'
      });

      const executionTime = Date.now() - startTime;
      
      const output = captureOutput ? stdout : 'Command executed successfully (output not captured)';
      const errorOutput = stderr ? `\nStderr: ${stderr}` : '';
      
      const metadata = {
        command,
        cwd,
        executionTime,
        exitCode: 0,
        hasStderr: !!stderr
      };

      logger.debug('Shell command completed successfully', { command, executionTime, hasStderr: !!stderr });

      return this.createSuccessResult(output + errorOutput, metadata);
    } catch (error: any) {
      const executionTime = Date.now() - Date.now();
      
      let errorMessage = 'Command execution failed';
      let exitCode = -1;
      
      if (error.code === 'ETIMEDOUT') {
        errorMessage = `Command timed out after ${timeout}ms`;
      } else if (error.code) {
        exitCode = error.code;
        errorMessage = `Command failed with exit code ${exitCode}`;
      }
      
      const output = error.stdout || '';
      const errorOutput = error.stderr || error.message || '';
      
      const metadata = {
        command,
        cwd,
        executionTime,
        exitCode,
        timedOut: error.code === 'ETIMEDOUT'
      };

      logger.warn('Shell command failed', { command, exitCode, error: errorMessage });

      // Return as success but with error information for the AI to handle
      return this.createSuccessResult(
        `${errorMessage}\nStdout: ${output}\nStderr: ${errorOutput}`,
        metadata
      );
    }
  }

  private async executeInteractive(
    command: string,
    cwd: string,
    timeout: number,
    env: NodeJS.ProcessEnv
  ): Promise<ToolResult> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      let output = '';
      let errorOutput = '';
      
      // Parse command and arguments
      const [cmd, ...args] = command.split(/\s+/);
      
      const child = spawn(cmd, args, {
        cwd,
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Set timeout
      const timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        resolve(this.createErrorResult(`Interactive command timed out after ${timeout}ms`));
      }, timeout);

      // Collect output
      child.stdout?.on('data', (data) => {
        output += data.toString();
      });

      child.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      child.on('close', (code) => {
        clearTimeout(timeoutId);
        const executionTime = Date.now() - startTime;
        
        const metadata = {
          command,
          cwd,
          executionTime,
          exitCode: code || 0,
          interactive: true
        };

        const fullOutput = output + (errorOutput ? `\nStderr: ${errorOutput}` : '');
        
        logger.debug('Interactive shell command completed', { command, exitCode: code, executionTime });

        resolve(this.createSuccessResult(fullOutput, metadata));
      });

      child.on('error', (error) => {
        clearTimeout(timeoutId);
        const executionTime = Date.now() - startTime;
        
        logger.error('Interactive shell command error', { command, error });
        
        resolve(this.createErrorResult(`Interactive command error: ${error.message}`));
      });
    });
  }
}
