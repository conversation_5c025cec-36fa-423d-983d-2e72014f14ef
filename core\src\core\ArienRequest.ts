// Low-level Providers API request handling
import { LanguageModel, generateText, streamText, CoreMessage, CoreTool } from 'ai';
import { anthropic } from '@ai-sdk/anthropic';
import { openai } from '@ai-sdk/openai';
import { google } from '@ai-sdk/google';
import { Provider, getModelConfig } from '../config/models.js';
import { logger } from './logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { retryWithBackoff } from '../utils/retry.js';

export interface RequestOptions {
  provider: Provider;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
  timeout?: number;
  retries?: number;
}

export interface RequestResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: string;
  model?: string;
  id?: string;
  created?: number;
  toolCalls?: any[];
  toolResults?: any[];
}

export interface StreamResponse {
  stream: AsyncGenerator<string, RequestResponse, unknown>;
  abort: () => void;
}

export class ArienRequest {
  private abortController?: AbortController;

  public async makeRequest(
    messages: CoreMessage[],
    options: RequestOptions,
    tools?: Record<string, CoreTool>
  ): Promise<RequestResponse> {
    const startTime = Date.now();
    
    try {
      logger.debug('Making API request', {
        provider: options.provider,
        model: options.model,
        messageCount: messages.length,
        toolCount: tools ? Object.keys(tools).length : 0
      });

      const model = this.getLanguageModel(options.provider, options.model);
      
      const result = await retryWithBackoff(
        async () => {
          this.abortController = new AbortController();
          
          return await generateText({
            model,
            messages,
            tools,
            temperature: options.temperature || 0.7,
            maxTokens: options.maxTokens || getModelConfig(options.model)?.maxTokens,
            topP: options.topP,
            frequencyPenalty: options.frequencyPenalty,
            presencePenalty: options.presencePenalty,
            stopSequences: options.stop,
            abortSignal: this.abortController.signal
          });
        },
        {
          maxRetries: options.retries || 3,
          baseDelay: 1000,
          maxDelay: 10000,
          shouldRetry: (error) => this.shouldRetryError(error)
        }
      );

      const duration = Date.now() - startTime;
      
      logger.debug('API request completed', {
        provider: options.provider,
        model: options.model,
        duration,
        usage: result.usage,
        finishReason: result.finishReason
      });

      return {
        content: result.text,
        usage: result.usage ? {
          promptTokens: result.usage.promptTokens,
          completionTokens: result.usage.completionTokens,
          totalTokens: result.usage.totalTokens
        } : undefined,
        finishReason: result.finishReason,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('API request failed', {
        provider: options.provider,
        model: options.model,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ArienError(ErrorCode.REQUEST_ABORTED, 'Request was aborted');
      }

      throw this.handleApiError(error, options);
    }
  }

  public async makeStreamRequest(
    messages: CoreMessage[],
    options: RequestOptions,
    tools?: Record<string, CoreTool>
  ): Promise<StreamResponse> {
    const startTime = Date.now();
    
    try {
      logger.debug('Making streaming API request', {
        provider: options.provider,
        model: options.model,
        messageCount: messages.length
      });

      const model = this.getLanguageModel(options.provider, options.model);
      this.abortController = new AbortController();
      
      const result = await streamText({
        model,
        messages,
        tools,
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || getModelConfig(options.model)?.maxTokens,
        topP: options.topP,
        frequencyPenalty: options.frequencyPenalty,
        presencePenalty: options.presencePenalty,
        stopSequences: options.stop,
        abortSignal: this.abortController.signal
      });

      const stream = this.createStreamGenerator(result, options, startTime);
      
      return {
        stream,
        abort: () => this.abortController?.abort()
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Streaming API request failed', {
        provider: options.provider,
        model: options.model,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });

      throw this.handleApiError(error, options);
    }
  }

  private async *createStreamGenerator(
    result: any,
    options: RequestOptions,
    startTime: number
  ): AsyncGenerator<string, RequestResponse, unknown> {
    let fullContent = '';
    let usage: any;
    let finishReason: string | undefined;

    try {
      for await (const delta of result.textStream) {
        fullContent += delta;
        yield delta;
      }

      // Get final result
      const finalResult = await result.finishReason;
      finishReason = finalResult;
      usage = result.usage;

      const duration = Date.now() - startTime;
      
      logger.debug('Streaming API request completed', {
        provider: options.provider,
        model: options.model,
        duration,
        contentLength: fullContent.length,
        usage,
        finishReason
      });

      return {
        content: fullContent,
        usage: usage ? {
          promptTokens: usage.promptTokens,
          completionTokens: usage.completionTokens,
          totalTokens: usage.totalTokens
        } : undefined,
        finishReason,
        toolCalls: result.toolCalls,
        toolResults: result.toolResults
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Streaming API request failed during streaming', {
        provider: options.provider,
        model: options.model,
        duration,
        error: error instanceof Error ? error.message : String(error)
      });

      throw this.handleApiError(error, options);
    }
  }

  private getLanguageModel(provider: Provider, model: string): LanguageModel {
    switch (provider) {
      case 'deepseek':
        return openai(model, {
          baseURL: 'https://api.deepseek.com',
          apiKey: process.env.DEEPSEEK_API_KEY
        });
      
      case 'openai':
        return openai(model, {
          apiKey: process.env.OPENAI_API_KEY
        });
      
      case 'anthropic':
        return anthropic(model, {
          apiKey: process.env.ANTHROPIC_API_KEY
        });
      
      case 'google':
        return google(model, {
          apiKey: process.env.GOOGLE_API_KEY
        });
      
      default:
        throw new ArienError(ErrorCode.INVALID_PROVIDER, `Unsupported provider: ${provider}`);
    }
  }

  private shouldRetryError(error: any): boolean {
    // Retry on network errors, rate limits, and temporary server errors
    if (error?.code === 'ECONNRESET' || error?.code === 'ENOTFOUND') {
      return true;
    }
    
    if (error?.status === 429 || error?.status === 502 || error?.status === 503 || error?.status === 504) {
      return true;
    }
    
    return false;
  }

  private handleApiError(error: any, options: RequestOptions): ArienError {
    if (error instanceof ArienError) {
      return error;
    }

    // Handle specific API errors
    if (error?.status === 401) {
      return new ArienError(ErrorCode.AUTHENTICATION_FAILED, `Authentication failed for ${options.provider}`);
    }
    
    if (error?.status === 403) {
      return new ArienError(ErrorCode.PERMISSION_DENIED, `Permission denied for ${options.provider}`);
    }
    
    if (error?.status === 429) {
      return new ArienError(ErrorCode.RATE_LIMITED, `Rate limit exceeded for ${options.provider}`);
    }
    
    if (error?.status === 404) {
      return new ArienError(ErrorCode.MODEL_NOT_FOUND, `Model ${options.model} not found for ${options.provider}`);
    }
    
    if (error?.status >= 500) {
      return new ArienError(ErrorCode.SERVER_ERROR, `Server error from ${options.provider}: ${error.message}`);
    }

    // Generic error
    return new ArienError(
      ErrorCode.API_ERROR,
      `API error from ${options.provider}: ${error?.message || String(error)}`
    );
  }

  public abort(): void {
    if (this.abortController) {
      this.abortController.abort();
      logger.debug('API request aborted');
    }
  }
}
