// Configuration hook
import { useState, useEffect } from 'react';
import { ArienConfig, loadConfig } from '@arien/core';

export interface UseConfigReturn extends ArienConfig {
  isLoading: boolean;
  error: Error | null;
  reload: () => Promise<void>;
}

export function useConfig(): UseConfigReturn {
  const [config, setConfig] = useState<ArienConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const loadConfiguration = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const loadedConfig = await loadConfig();
      setConfig(loadedConfig);
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setError(error);
      
      // Fallback to default config
      setConfig({
        defaultProvider: 'deepseek',
        defaultModel: 'deepseek-chat',
        temperature: 0.7,
        maxTokens: 4000,
        systemPrompt: 'You are <PERSON><PERSON>, a helpful AI assistant.',
        approvalLevel: 'default',
        sandboxEnabled: true,
        logLevel: 'info'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadConfiguration();
  }, []);

  const reload = async () => {
    await loadConfiguration();
  };

  return {
    ...(config || {
      defaultProvider: 'deepseek',
      defaultModel: 'deepseek-chat',
      temperature: 0.7,
      maxTokens: 4000,
      systemPrompt: 'You are Arien, a helpful AI assistant.',
      approvalLevel: 'default',
      sandboxEnabled: true,
      logLevel: 'info'
    }),
    isLoading,
    error,
    reload
  };
}
