// Breadth-first search for files
import { readdir, stat } from 'fs/promises';
import { join, relative, resolve } from 'path';
import { minimatch } from 'minimatch';
import { logger } from '../core/logger.js';

export interface BfsSearchOptions {
  maxDepth?: number;
  maxResults?: number;
  includeDirectories?: boolean;
  includeHidden?: boolean;
  followSymlinks?: boolean;
  patterns?: string[];
  excludePatterns?: string[];
  extensions?: string[];
  caseSensitive?: boolean;
  timeout?: number;
}

export interface BfsSearchResult {
  path: string;
  relativePath: string;
  isDirectory: boolean;
  size: number;
  depth: number;
  mtime: Date;
}

export interface BfsSearchStats {
  totalScanned: number;
  totalMatched: number;
  directoriesScanned: number;
  maxDepthReached: number;
  searchTime: number;
  timedOut: boolean;
}

export class BfsFileSearch {
  private aborted = false;
  private stats: BfsSearchStats = {
    totalScanned: 0,
    totalMatched: 0,
    directoriesScanned: 0,
    maxDepthReached: 0,
    searchTime: 0,
    timedOut: false
  };

  public async search(
    rootPath: string,
    options: BfsSearchOptions = {}
  ): Promise<{ results: BfsSearchResult[]; stats: BfsSearchStats }> {
    const startTime = Date.now();
    this.resetStats();

    const {
      maxDepth = 10,
      maxResults = 1000,
      includeDirectories = false,
      includeHidden = false,
      followSymlinks = false,
      patterns = [],
      excludePatterns = ['node_modules/**', '.git/**', 'dist/**', 'build/**'],
      extensions = [],
      caseSensitive = false,
      timeout = 30000
    } = options;

    const results: BfsSearchResult[] = [];
    const queue: Array<{ path: string; depth: number }> = [{ path: resolve(rootPath), depth: 0 }];
    const visited = new Set<string>();

    // Set up timeout
    const timeoutId = setTimeout(() => {
      this.aborted = true;
      this.stats.timedOut = true;
    }, timeout);

    try {
      while (queue.length > 0 && !this.aborted && results.length < maxResults) {
        const { path: currentPath, depth } = queue.shift()!;

        // Skip if already visited (for symlink handling)
        if (visited.has(currentPath)) continue;
        visited.add(currentPath);

        // Update max depth reached
        this.stats.maxDepthReached = Math.max(this.stats.maxDepthReached, depth);

        try {
          const stats = await stat(currentPath);
          this.stats.totalScanned++;

          // Handle symlinks
          if (stats.isSymbolicLink() && !followSymlinks) {
            continue;
          }

          const relativePath = relative(rootPath, currentPath);
          const isHidden = this.isHidden(relativePath);

          // Skip hidden files/directories if not included
          if (isHidden && !includeHidden) {
            continue;
          }

          // Check exclude patterns
          if (this.matchesPatterns(relativePath, excludePatterns, caseSensitive)) {
            continue;
          }

          if (stats.isDirectory()) {
            this.stats.directoriesScanned++;

            // Add directory to results if requested
            if (includeDirectories && this.shouldInclude(relativePath, patterns, extensions, caseSensitive)) {
              results.push({
                path: currentPath,
                relativePath,
                isDirectory: true,
                size: 0,
                depth,
                mtime: stats.mtime
              });
              this.stats.totalMatched++;
            }

            // Add subdirectories to queue if within depth limit
            if (depth < maxDepth) {
              try {
                const entries = await readdir(currentPath);
                for (const entry of entries) {
                  const entryPath = join(currentPath, entry);
                  queue.push({ path: entryPath, depth: depth + 1 });
                }
              } catch (error) {
                logger.debug('Failed to read directory', { path: currentPath, error });
              }
            }
          } else if (stats.isFile()) {
            // Check if file should be included
            if (this.shouldInclude(relativePath, patterns, extensions, caseSensitive)) {
              results.push({
                path: currentPath,
                relativePath,
                isDirectory: false,
                size: stats.size,
                depth,
                mtime: stats.mtime
              });
              this.stats.totalMatched++;
            }
          }
        } catch (error) {
          logger.debug('Failed to stat path', { path: currentPath, error });
        }
      }
    } finally {
      clearTimeout(timeoutId);
      this.stats.searchTime = Date.now() - startTime;
    }

    return { results, stats: this.stats };
  }

  public abort(): void {
    this.aborted = true;
  }

  private resetStats(): void {
    this.aborted = false;
    this.stats = {
      totalScanned: 0,
      totalMatched: 0,
      directoriesScanned: 0,
      maxDepthReached: 0,
      searchTime: 0,
      timedOut: false
    };
  }

  private isHidden(path: string): boolean {
    return path.split('/').some(part => part.startsWith('.'));
  }

  private shouldInclude(
    path: string,
    patterns: string[],
    extensions: string[],
    caseSensitive: boolean
  ): boolean {
    // If no patterns or extensions specified, include everything
    if (patterns.length === 0 && extensions.length === 0) {
      return true;
    }

    const pathToCheck = caseSensitive ? path : path.toLowerCase();

    // Check extension filter
    if (extensions.length > 0) {
      const ext = path.split('.').pop()?.toLowerCase() || '';
      const normalizedExtensions = extensions.map(e => e.toLowerCase());
      if (!normalizedExtensions.includes(ext)) {
        return false;
      }
    }

    // Check patterns
    if (patterns.length > 0) {
      const normalizedPatterns = caseSensitive ? patterns : patterns.map(p => p.toLowerCase());
      return normalizedPatterns.some(pattern => minimatch(pathToCheck, pattern));
    }

    return true;
  }

  private matchesPatterns(
    path: string,
    patterns: string[],
    caseSensitive: boolean
  ): boolean {
    if (patterns.length === 0) return false;

    const pathToCheck = caseSensitive ? path : path.toLowerCase();
    const normalizedPatterns = caseSensitive ? patterns : patterns.map(p => p.toLowerCase());

    return normalizedPatterns.some(pattern => minimatch(pathToCheck, pattern));
  }
}

// Convenience function for simple searches
export async function searchFiles(
  rootPath: string,
  options: BfsSearchOptions = {}
): Promise<BfsSearchResult[]> {
  const searcher = new BfsFileSearch();
  const { results } = await searcher.search(rootPath, options);
  return results;
}

// Search for files by name pattern
export async function findFilesByName(
  rootPath: string,
  namePattern: string,
  options: Omit<BfsSearchOptions, 'patterns'> = {}
): Promise<BfsSearchResult[]> {
  return searchFiles(rootPath, {
    ...options,
    patterns: [namePattern]
  });
}

// Search for files by extension
export async function findFilesByExtension(
  rootPath: string,
  extensions: string | string[],
  options: Omit<BfsSearchOptions, 'extensions'> = {}
): Promise<BfsSearchResult[]> {
  const extArray = Array.isArray(extensions) ? extensions : [extensions];
  return searchFiles(rootPath, {
    ...options,
    extensions: extArray
  });
}
