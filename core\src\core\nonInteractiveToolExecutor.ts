// Non-interactive tool execution
import { ToolRegistry, ToolApprovalRequest, ToolApprovalResponse } from '../tools/tool-registry.js';
import { ToolContext, ToolResult, ToolExecutionError } from '../tools/tools.js';
import { logger } from './logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface NonInteractiveOptions {
  autoApprove?: boolean;
  approvalLevel?: 'none' | 'safe' | 'all';
  allowedTools?: string[];
  blockedTools?: string[];
  maxExecutionTime?: number;
  dryRun?: boolean;
}

export interface ExecutionPlan {
  tools: Array<{
    toolName: string;
    parameters: any;
    riskLevel: 'low' | 'medium' | 'high';
    requiresApproval: boolean;
  }>;
  totalRiskScore: number;
  recommendedApprovalLevel: 'none' | 'safe' | 'all';
}

export interface ExecutionResult {
  success: boolean;
  results: ToolResult[];
  errors: Error[];
  executionTime: number;
  toolsExecuted: number;
  toolsSkipped: number;
}

export class NonInteractiveToolExecutor {
  private toolRegistry: ToolRegistry;
  private options: NonInteractiveOptions;

  constructor(toolRegistry: ToolRegistry, options: NonInteractiveOptions = {}) {
    this.toolRegistry = toolRegistry;
    this.options = {
      autoApprove: false,
      approvalLevel: 'safe',
      maxExecutionTime: 300000, // 5 minutes
      dryRun: false,
      ...options
    };
  }

  public async executeBatch(
    toolCalls: Array<{
      toolName: string;
      parameters: any;
      context?: ToolContext;
    }>
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    const results: ToolResult[] = [];
    const errors: Error[] = [];
    let toolsExecuted = 0;
    let toolsSkipped = 0;

    logger.info('Starting batch tool execution', {
      toolCount: toolCalls.length,
      options: this.sanitizeOptions(this.options)
    });

    // Create execution plan
    const plan = this.createExecutionPlan(toolCalls);
    
    if (this.options.dryRun) {
      logger.info('Dry run mode - execution plan created', {
        toolCount: plan.tools.length,
        totalRiskScore: plan.totalRiskScore,
        recommendedApprovalLevel: plan.recommendedApprovalLevel
      });
      
      return {
        success: true,
        results: [],
        errors: [],
        executionTime: Date.now() - startTime,
        toolsExecuted: 0,
        toolsSkipped: toolCalls.length
      };
    }

    // Execute tools sequentially
    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i];
      const planItem = plan.tools[i];

      try {
        // Check execution timeout
        if (Date.now() - startTime > (this.options.maxExecutionTime || 300000)) {
          const timeoutError = new ArienError(
            ErrorCode.TOOL_EXECUTION_TIMEOUT,
            'Batch execution timeout exceeded'
          );
          errors.push(timeoutError);
          toolsSkipped += toolCalls.length - i;
          break;
        }

        // Check if tool is allowed
        if (!this.isToolAllowed(toolCall.toolName)) {
          const blockError = new ArienError(
            ErrorCode.TOOL_EXECUTION_DENIED,
            `Tool ${toolCall.toolName} is not allowed in non-interactive mode`
          );
          errors.push(blockError);
          toolsSkipped++;
          continue;
        }

        // Handle approval
        if (planItem.requiresApproval && !this.shouldAutoApprove(planItem)) {
          const approvalError = new ArienError(
            ErrorCode.TOOL_EXECUTION_DENIED,
            `Tool ${toolCall.toolName} requires approval but auto-approval is disabled`
          );
          errors.push(approvalError);
          toolsSkipped++;
          continue;
        }

        // Execute the tool
        const context: ToolContext = {
          userId: 'non-interactive',
          sessionId: `batch_${startTime}`,
          nonInteractive: true,
          ...toolCall.context
        };

        logger.debug('Executing tool in batch', {
          toolName: toolCall.toolName,
          index: i + 1,
          total: toolCalls.length,
          riskLevel: planItem.riskLevel
        });

        const result = await this.toolRegistry.executeTool(
          toolCall.toolName,
          toolCall.parameters,
          context
        );

        results.push(result);
        toolsExecuted++;

        logger.debug('Tool executed successfully in batch', {
          toolName: toolCall.toolName,
          index: i + 1,
          executionTime: result.executionTime
        });

      } catch (error) {
        const toolError = error instanceof Error ? error : new Error(String(error));
        errors.push(toolError);
        toolsSkipped++;

        logger.error('Tool execution failed in batch', {
          toolName: toolCall.toolName,
          index: i + 1,
          error: toolError.message
        });

        // Decide whether to continue or stop
        if (this.shouldStopOnError(toolError, planItem)) {
          logger.warn('Stopping batch execution due to critical error', {
            toolName: toolCall.toolName,
            error: toolError.message
          });
          toolsSkipped += toolCalls.length - i - 1;
          break;
        }
      }
    }

    const executionTime = Date.now() - startTime;
    const success = errors.length === 0 || (toolsExecuted > 0 && errors.length < toolCalls.length);

    logger.info('Batch tool execution completed', {
      success,
      toolsExecuted,
      toolsSkipped,
      errorCount: errors.length,
      executionTime
    });

    return {
      success,
      results,
      errors,
      executionTime,
      toolsExecuted,
      toolsSkipped
    };
  }

  public createExecutionPlan(
    toolCalls: Array<{
      toolName: string;
      parameters: any;
      context?: ToolContext;
    }>
  ): ExecutionPlan {
    const tools = toolCalls.map(call => {
      const riskLevel = this.assessRiskLevel(call.toolName, call.parameters);
      const requiresApproval = this.requiresApproval(call.toolName, riskLevel);

      return {
        toolName: call.toolName,
        parameters: call.parameters,
        riskLevel,
        requiresApproval
      };
    });

    // Calculate total risk score
    const riskScores = { low: 1, medium: 3, high: 5 };
    const totalRiskScore = tools.reduce((sum, tool) => sum + riskScores[tool.riskLevel], 0);

    // Recommend approval level
    let recommendedApprovalLevel: 'none' | 'safe' | 'all' = 'none';
    if (tools.some(t => t.riskLevel === 'high')) {
      recommendedApprovalLevel = 'all';
    } else if (tools.some(t => t.riskLevel === 'medium')) {
      recommendedApprovalLevel = 'safe';
    }

    return {
      tools,
      totalRiskScore,
      recommendedApprovalLevel
    };
  }

  public setApprovalHandler(handler: (request: ToolApprovalRequest) => Promise<ToolApprovalResponse>): void {
    this.toolRegistry.setApprovalHandler(handler);
  }

  public updateOptions(options: Partial<NonInteractiveOptions>): void {
    this.options = { ...this.options, ...options };
    logger.debug('Non-interactive executor options updated', {
      options: this.sanitizeOptions(this.options)
    });
  }

  private isToolAllowed(toolName: string): boolean {
    // Check blocked tools first
    if (this.options.blockedTools?.includes(toolName)) {
      return false;
    }

    // If allowed tools list is specified, tool must be in it
    if (this.options.allowedTools && this.options.allowedTools.length > 0) {
      return this.options.allowedTools.includes(toolName);
    }

    // Default: allow all tools not explicitly blocked
    return true;
  }

  private shouldAutoApprove(planItem: { toolName: string; riskLevel: 'low' | 'medium' | 'high' }): boolean {
    if (!this.options.autoApprove) {
      return false;
    }

    switch (this.options.approvalLevel) {
      case 'none':
        return false;
      case 'safe':
        return planItem.riskLevel === 'low';
      case 'all':
        return true;
      default:
        return false;
    }
  }

  private requiresApproval(toolName: string, riskLevel: 'low' | 'medium' | 'high'): boolean {
    // High-risk tools always require approval
    if (riskLevel === 'high') {
      return true;
    }

    // Check tool-specific approval requirements
    const tool = this.toolRegistry.getTool(toolName);
    return tool?.definition.requiresApproval || false;
  }

  private assessRiskLevel(toolName: string, parameters: any): 'low' | 'medium' | 'high' {
    // High-risk tools
    const highRiskTools = [
      'shell',
      'write-file',
      'delete-file',
      'move-file',
      'copy-file'
    ];

    // Medium-risk tools
    const mediumRiskTools = [
      'edit-file',
      'create-directory',
      'web-fetch'
    ];

    if (highRiskTools.includes(toolName)) {
      return 'high';
    }

    if (mediumRiskTools.includes(toolName)) {
      return 'medium';
    }

    // Check parameters for risky operations
    if (parameters) {
      const paramStr = JSON.stringify(parameters).toLowerCase();
      
      // Look for potentially dangerous operations
      const dangerousPatterns = [
        'rm -rf',
        'delete',
        'remove',
        'format',
        'sudo',
        'admin',
        'password',
        'secret'
      ];

      if (dangerousPatterns.some(pattern => paramStr.includes(pattern))) {
        return 'high';
      }
    }

    return 'low';
  }

  private shouldStopOnError(error: Error, planItem: any): boolean {
    // Stop on critical errors
    if (error instanceof ArienError) {
      const criticalErrors = [
        ErrorCode.PERMISSION_DENIED,
        ErrorCode.AUTHENTICATION_FAILED,
        ErrorCode.RATE_LIMITED
      ];
      
      return criticalErrors.includes(error.code);
    }

    // Stop on high-risk tool failures
    return planItem.riskLevel === 'high';
  }

  private sanitizeOptions(options: NonInteractiveOptions): any {
    // Remove sensitive information for logging
    return {
      autoApprove: options.autoApprove,
      approvalLevel: options.approvalLevel,
      allowedToolsCount: options.allowedTools?.length || 0,
      blockedToolsCount: options.blockedTools?.length || 0,
      maxExecutionTime: options.maxExecutionTime,
      dryRun: options.dryRun
    };
  }
}
