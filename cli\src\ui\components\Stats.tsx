// Statistics display component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface StatItem {
  label: string;
  value: string | number;
  unit?: string;
  color?: string;
  icon?: string;
  trend?: {
    direction: 'up' | 'down' | 'stable';
    percentage?: number;
    isGood?: boolean;
  };
}

export interface StatGroup {
  title: string;
  items: StatItem[];
  color?: string;
}

interface StatsProps {
  groups: StatGroup[];
  layout?: 'horizontal' | 'vertical' | 'grid';
  showTrends?: boolean;
  compact?: boolean;
}

export const Stats: React.FC<StatsProps> = ({
  groups,
  layout = 'vertical',
  showTrends = true,
  compact = false
}) => {
  const theme = useTheme();

  const formatValue = (value: string | number, unit?: string) => {
    if (typeof value === 'number') {
      // Format large numbers
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M${unit || ''}`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K${unit || ''}`;
      }
      return `${value}${unit || ''}`;
    }
    return `${value}${unit || ''}`;
  };

  const getTrendIcon = (direction: 'up' | 'down' | 'stable') => {
    switch (direction) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '→';
      default: return '';
    }
  };

  const getTrendColor = (trend: StatItem['trend']) => {
    if (!trend) return theme.colors.muted;
    
    if (trend.direction === 'stable') return theme.colors.muted;
    
    // If isGood is specified, use it to determine color
    if (trend.isGood !== undefined) {
      return trend.isGood ? theme.colors.success : theme.colors.error;
    }
    
    // Default: up is good, down is bad
    return trend.direction === 'up' ? theme.colors.success : theme.colors.error;
  };

  const renderStatItem = (item: StatItem, index: number) => {
    const itemColor = item.color || theme.colors.text;
    
    if (compact) {
      return (
        <Box key={index} flexDirection="row" gap={1}>
          {item.icon && <Text color={itemColor}>{item.icon}</Text>}
          <Text color={theme.colors.muted}>{item.label}:</Text>
          <Text color={itemColor}>{formatValue(item.value, item.unit)}</Text>
          {showTrends && item.trend && (
            <Text color={getTrendColor(item.trend)}>
              {getTrendIcon(item.trend.direction)}
              {item.trend.percentage && `${item.trend.percentage}%`}
            </Text>
          )}
        </Box>
      );
    }

    return (
      <Box key={index} flexDirection="column" marginBottom={1}>
        <Box flexDirection="row">
          {item.icon && (
            <Text color={itemColor}>{item.icon} </Text>
          )}
          <Text color={theme.colors.muted}>{item.label}</Text>
        </Box>
        <Box flexDirection="row" marginLeft={item.icon ? 2 : 0}>
          <Text color={itemColor} bold>
            {formatValue(item.value, item.unit)}
          </Text>
          {showTrends && item.trend && (
            <Text color={getTrendColor(item.trend)}>
              {' '}{getTrendIcon(item.trend.direction)}
              {item.trend.percentage && ` ${item.trend.percentage}%`}
            </Text>
          )}
        </Box>
      </Box>
    );
  };

  const renderStatGroup = (group: StatGroup, groupIndex: number) => {
    const groupColor = group.color || theme.colors.primary;
    
    return (
      <Box key={groupIndex} flexDirection="column" marginBottom={compact ? 1 : 2}>
        <Text color={groupColor} bold>
          {group.title}
        </Text>
        <Box 
          flexDirection={layout === 'horizontal' ? 'row' : 'column'}
          marginTop={compact ? 0 : 1}
          marginLeft={compact ? 0 : 1}
          gap={layout === 'horizontal' ? 2 : 0}
        >
          {group.items.map((item, index) => renderStatItem(item, index))}
        </Box>
      </Box>
    );
  };

  if (layout === 'grid') {
    // Grid layout: arrange groups in a 2-column grid
    const leftGroups = groups.filter((_, index) => index % 2 === 0);
    const rightGroups = groups.filter((_, index) => index % 2 === 1);

    return (
      <Box flexDirection="row" gap={4}>
        <Box flexDirection="column" flexGrow={1}>
          {leftGroups.map((group, index) => renderStatGroup(group, index * 2))}
        </Box>
        <Box flexDirection="column" flexGrow={1}>
          {rightGroups.map((group, index) => renderStatGroup(group, index * 2 + 1))}
        </Box>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {groups.map((group, index) => renderStatGroup(group, index))}
    </Box>
  );
};
