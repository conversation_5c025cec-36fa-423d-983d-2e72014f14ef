// Theme management system
import { Theme, getTheme, getAvailableThemes } from './theme.js';
import { getConfig } from '@arien/core';

export class ThemeManager {
  private currentTheme: Theme;
  private listeners: Array<(theme: Theme) => void> = [];

  constructor() {
    const config = getConfig();
    const themeName = config.getConfig().theme;
    this.currentTheme = getTheme(themeName);
  }

  public getCurrentTheme(): Theme {
    return this.currentTheme;
  }

  public setTheme(themeName: string): void {
    const newTheme = getTheme(themeName);
    this.currentTheme = newTheme;
    
    // Update configuration
    const config = getConfig();
    config.updateConfig({ theme: themeName });
    
    // Notify listeners
    this.listeners.forEach(listener => listener(newTheme));
  }

  public getAvailableThemes(): string[] {
    return getAvailableThemes();
  }

  public addThemeChangeListener(listener: (theme: Theme) => void): void {
    this.listeners.push(listener);
  }

  public removeThemeChangeListener(listener: (theme: Theme) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  public getColor(colorKey: keyof Theme['colors']): string {
    return this.currentTheme.colors[colorKey];
  }

  public getSyntaxColor(syntaxKey: keyof Theme['syntax']): string {
    return this.currentTheme.syntax[syntaxKey];
  }

  public getUIColor(uiKey: keyof Theme['ui']): string {
    return this.currentTheme.ui[uiKey];
  }
}

// Global theme manager instance
export const themeManager = new ThemeManager();
