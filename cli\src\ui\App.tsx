// Main React application orchestrator
import React, { useState, useEffect } from 'react';
import { Box, Text, useApp } from 'ink';
import { getConfig, logger } from '@arien/core';
import { AuthDialog } from './components/AuthDialog.js';
import { Header } from './components/Header.js';
import { Footer } from './components/Footer.js';
import { InputPrompt } from './components/InputPrompt.js';
import { ChatDisplay } from './components/ChatDisplay.js';
import { LoadingIndicator } from './components/LoadingIndicator.js';

interface AppProps {
  initialMessage?: string;
  options?: {
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    autoApprove?: boolean;
    stream?: boolean;
    web?: boolean;
    debug?: boolean;
  };
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

export const App: React.FC<AppProps> = ({ initialMessage, options = {} }) => {
  const { exit } = useApp();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<string>('');

  // Check authentication status on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const config = getConfig();
        const validation = config.validateConfig();
        
        if (validation.valid) {
          setIsAuthenticated(true);
          
          // If we have an initial message, process it
          if (initialMessage) {
            await handleUserMessage(initialMessage);
          }
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        logger.error('Authentication check failed', { error });
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [initialMessage]);

  const handleAuthComplete = () => {
    setIsAuthenticated(true);
    logger.info('Authentication completed successfully');
  };

  const handleAuthCancel = () => {
    logger.info('Authentication cancelled by user');
    exit();
  };

  const handleUserMessage = async (message: string) => {
    if (isProcessing) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    setCurrentStreamingMessage('');

    try {
      const { ArienClient } = await import('@arien/core');
      
      const client = new ArienClient({
        provider: options.provider as any,
        model: options.model,
        temperature: options.temperature,
        maxTokens: options.maxTokens,
        stream: options.stream
      });

      const chatMessages = messages.concat(userMessage).map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      if (options.stream) {
        // Streaming mode
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '',
          timestamp: new Date(),
          isStreaming: true
        };

        setMessages(prev => [...prev, assistantMessage]);

        const stream = client.streamChat(chatMessages);
        let fullContent = '';

        for await (const chunk of stream) {
          fullContent += chunk;
          setCurrentStreamingMessage(fullContent);
          
          // Update the streaming message
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: fullContent }
                : msg
            )
          );
        }

        // Mark streaming as complete
        setMessages(prev => 
          prev.map(msg => 
            msg.id === assistantMessage.id 
              ? { ...msg, isStreaming: false }
              : msg
          )
        );
      } else {
        // Non-streaming mode
        const response = await client.chat(chatMessages);

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: response.content,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, assistantMessage]);
      }
    } catch (error) {
      logger.error('Message processing failed', { error, message });
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: `❌ Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
      setCurrentStreamingMessage('');
    }
  };

  const handleExit = () => {
    logger.info('User requested exit');
    exit();
  };

  if (isLoading) {
    return (
      <Box flexDirection="column" height="100%">
        <LoadingIndicator message="Initializing Arien CLI..." />
      </Box>
    );
  }

  if (!isAuthenticated) {
    return (
      <AuthDialog
        onComplete={handleAuthComplete}
        onCancel={handleAuthCancel}
      />
    );
  }

  return (
    <Box flexDirection="column" height="100%">
      <Header />
      
      <Box flexGrow={1} flexDirection="column">
        <ChatDisplay 
          messages={messages}
          isProcessing={isProcessing}
          currentStreamingMessage={currentStreamingMessage}
        />
      </Box>
      
      <InputPrompt
        onSubmit={handleUserMessage}
        onExit={handleExit}
        disabled={isProcessing}
      />
      
      <Footer 
        isProcessing={isProcessing}
        messageCount={messages.length}
      />
    </Box>
  );
};
