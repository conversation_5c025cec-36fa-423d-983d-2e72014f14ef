# Arien CLI - Basic Usage Examples

This document provides examples of how to use Arien CLI for various tasks.

## Getting Started

### 1. First Time Setup

```bash
# Install dependencies and build
npm run install:all
npm run build

# Configure your AI provider
arien auth

# Check status
arien status
```

### 2. Basic Conversations

```bash
# Simple question
arien "What is the difference between let and const in JavaScript?"

# Code help
arien "How do I create a React component with TypeScript?"

# File analysis
arien "Explain this code" @src/components/Button.tsx
```

## Configuration Examples

### Provider Configuration

```bash
# Configure DeepSeek
arien auth --provider deepseek

# Configure OpenAI
arien auth --provider openai

# Configure Anthropic
arien auth --provider anthropic

# Configure Google
arien auth --provider google
```

### Settings Management

```bash
# View current configuration
arien config show

# Set default provider
arien config set defaultProvider deepseek

# Set default model
arien config set defaultModel deepseek-chat

# Set theme
arien config set theme dracula

# View available models
arien models

# View models for specific provider
arien models --provider openai
```

## Advanced Usage

### Non-Interactive Mode

```bash
# Pipe input
echo "Explain this error message" | arien --non-interactive

# File input
arien --non-interactive < question.txt

# With specific options
echo "Generate a README" | arien --non-interactive --provider openai --model gpt-4
```

### Streaming Responses

```bash
# Enable streaming for real-time responses
arien --stream "Write a long explanation about machine learning"

# Non-interactive streaming
echo "Explain quantum computing" | arien --non-interactive --stream
```

### Provider and Model Selection

```bash
# Use specific provider
arien --provider anthropic "Analyze this code for security issues"

# Use specific model
arien --model gpt-4 "Complex reasoning task"

# Combine options
arien --provider openai --model gpt-4 --temperature 0.2 "Generate precise code"
```

### File Operations

```bash
# Read and analyze files
arien "Review this code for bugs" @src/main.ts

# Multiple files
arien "Compare these implementations" @src/v1.js @src/v2.js

# Directory analysis
arien "Summarize the structure of this project" @src/

# Create files
arien "Create a TypeScript interface for a user profile" > types/User.ts
```

### Web Integration

```bash
# Web search
arien --web "Latest React 18 features"

# Research with web context
arien --web "Compare performance of different sorting algorithms"
```

### Security and Approval

```bash
# Default approval level (confirm destructive operations)
arien "Delete all .log files in this directory"

# Auto-approve file edits only
arien config set security.approvalLevel auto-edit

# Auto-approve everything (dangerous!)
arien config set security.approvalLevel yolo

# Reset to default
arien config set security.approvalLevel default
```

## Practical Examples

### Code Review

```bash
# Review a specific file
arien "Review this code for best practices and potential issues" @src/api/users.ts

# Review recent changes
git diff HEAD~1 | arien --non-interactive "Review these changes"
```

### Documentation

```bash
# Generate documentation
arien "Generate JSDoc comments for this file" @src/utils/helpers.js

# Create README
arien "Create a comprehensive README for this project" @package.json @src/
```

### Debugging

```bash
# Analyze error logs
arien "Analyze this error and suggest fixes" @logs/error.log

# Debug code
arien "Help me debug this function" @src/problematic-function.js
```

### Project Setup

```bash
# Initialize new project
arien "Create a package.json for a TypeScript React project"

# Setup configuration
arien "Create a TypeScript config for a Node.js project"

# Generate boilerplate
arien "Create a basic Express.js server with TypeScript"
```

### Learning and Explanation

```bash
# Explain concepts
arien "Explain async/await with practical examples"

# Code explanation
arien "Explain how this algorithm works step by step" @src/sorting.js

# Best practices
arien "What are the best practices for React hooks?"
```

## Tips and Tricks

### 1. Use Aliases

Add to your shell profile:

```bash
alias ai="arien"
alias ask="arien"
alias code-review="arien 'Review this code'"
```

### 2. Combine with Git

```bash
# Review commits
git show HEAD | arien --non-interactive "Review this commit"

# Generate commit messages
git diff --cached | arien --non-interactive "Generate a commit message"
```

### 3. Project-Specific Configuration

Create `.arien.json` in your project root:

```json
{
  "defaultProvider": "deepseek",
  "defaultModel": "deepseek-coder",
  "security": {
    "approvalLevel": "auto-edit"
  }
}
```

### 4. Batch Operations

```bash
# Process multiple files
for file in src/*.js; do
  arien "Add JSDoc comments" @"$file" > "${file%.js}.documented.js"
done
```

### 5. Integration with Editors

For VS Code, add to tasks.json:

```json
{
  "label": "Ask Arien",
  "type": "shell",
  "command": "arien",
  "args": ["${input:question}"],
  "group": "build"
}
```

## Troubleshooting

### Common Issues

```bash
# Check configuration
arien status

# Reset configuration
arien config reset

# Clear cache
rm -rf ~/.arien/cache

# Enable debug logging
arien --debug "test message"

# Check logs
tail -f ~/.arien/logs/arien.log
```

### Performance Tips

```bash
# Use appropriate models for tasks
arien --model deepseek-chat "Simple question"  # Fast and cheap
arien --model gpt-4 "Complex reasoning"        # Slower but more capable

# Limit token usage
arien --max-tokens 500 "Brief explanation"
```

This covers the basic usage patterns. For more advanced features, see the API documentation and configuration guide.
