// Input box component with command handling
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../hooks/useTheme.js';

export interface InputBoxProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  multiline?: boolean;
}

export const InputBox: React.FC<InputBoxProps> = ({
  value,
  onChange,
  onSubmit,
  disabled = false,
  placeholder = "Type your message...",
  multiline = false
}) => {
  const theme = useTheme();
  const [cursorPosition, setCursorPosition] = useState(0);
  const [isComposing, setIsComposing] = useState(false);

  useInput((input, key) => {
    if (disabled) return;

    if (key.return) {
      if (multiline && !key.ctrl) {
        // Add newline in multiline mode
        const newValue = value.slice(0, cursorPosition) + '\n' + value.slice(cursorPosition);
        onChange(newValue);
        setCursorPosition(cursorPosition + 1);
      } else {
        // Submit message
        if (value.trim()) {
          onSubmit(value);
          setCursorPosition(0);
        }
      }
      return;
    }

    if (key.backspace || key.delete) {
      if (value.length > 0) {
        if (key.backspace && cursorPosition > 0) {
          const newValue = value.slice(0, cursorPosition - 1) + value.slice(cursorPosition);
          onChange(newValue);
          setCursorPosition(cursorPosition - 1);
        } else if (key.delete && cursorPosition < value.length) {
          const newValue = value.slice(0, cursorPosition) + value.slice(cursorPosition + 1);
          onChange(newValue);
        }
      }
      return;
    }

    if (key.leftArrow) {
      setCursorPosition(Math.max(0, cursorPosition - 1));
      return;
    }

    if (key.rightArrow) {
      setCursorPosition(Math.min(value.length, cursorPosition + 1));
      return;
    }

    if (key.upArrow || key.downArrow) {
      // TODO: Implement history navigation
      return;
    }

    if (key.ctrl) {
      if (input === 'a') {
        setCursorPosition(0);
        return;
      }
      if (input === 'e') {
        setCursorPosition(value.length);
        return;
      }
      if (input === 'u') {
        onChange('');
        setCursorPosition(0);
        return;
      }
      if (input === 'k') {
        onChange(value.slice(0, cursorPosition));
        return;
      }
      return;
    }

    // Regular character input
    if (input && !key.meta && !key.ctrl) {
      const newValue = value.slice(0, cursorPosition) + input + value.slice(cursorPosition);
      onChange(newValue);
      setCursorPosition(cursorPosition + input.length);
    }
  });

  const renderInput = () => {
    if (!value && placeholder) {
      return (
        <Text color={theme.colors.muted} dimColor>
          {placeholder}
        </Text>
      );
    }

    const beforeCursor = value.slice(0, cursorPosition);
    const atCursor = value[cursorPosition] || ' ';
    const afterCursor = value.slice(cursorPosition + 1);

    return (
      <Text>
        <Text color={theme.colors.text}>{beforeCursor}</Text>
        <Text color={theme.colors.text} inverse={!disabled}>
          {atCursor}
        </Text>
        <Text color={theme.colors.text}>{afterCursor}</Text>
      </Text>
    );
  };

  const getPromptSymbol = () => {
    if (disabled) return '⏸';
    if (value.startsWith('/')) return '>';
    return '❯';
  };

  return (
    <Box
      borderStyle="round"
      borderColor={disabled ? theme.colors.muted : theme.colors.border}
      paddingX={1}
      paddingY={0}
    >
      <Text color={disabled ? theme.colors.muted : theme.colors.primary}>
        {getPromptSymbol()}
      </Text>
      <Box marginLeft={1} flexGrow={1}>
        {renderInput()}
      </Box>
      {multiline && (
        <Box marginLeft="auto">
          <Text color={theme.colors.muted} dimColor>
            Ctrl+Enter to send
          </Text>
        </Box>
      )}
    </Box>
  );
};
