// Core configuration management system
import { z } from 'zod';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { Provider, PROVIDERS, DEFAULT_MODELS } from './models.js';

// Configuration schema validation
const ProviderConfigSchema = z.object({
  apiKey: z.string().optional(),
  baseUrl: z.string().optional(),
  organization: z.string().optional(),
  project: z.string().optional()
});

const SecurityConfigSchema = z.object({
  approvalLevel: z.enum(['default', 'auto-edit', 'yolo']).default('default'),
  sandbox: z.object({
    enabled: z.boolean().default(true),
    restrictive: z.boolean().default(true)
  }).default({}),
  allowedCommands: z.array(z.string()).default(['git', 'npm', 'node', 'python', 'pip'])
});

const ConfigSchema = z.object({
  providers: z.record(z.nativeEnum(PROVIDERS), ProviderConfigSchema).default({}),
  defaultProvider: z.nativeEnum(PROVIDERS).default(PROVIDERS.DEEPSEEK),
  defaultModel: z.string().optional(),
  security: SecurityConfigSchema.default({}),
  theme: z.string().default('default'),
  editor: z.string().optional(),
  autoSave: z.boolean().default(true),
  historySize: z.number().default(1000),
  telemetry: z.boolean().default(true),
  debug: z.boolean().default(false)
});

export type ArienConfig = z.infer<typeof ConfigSchema>;
export type ProviderConfig = z.infer<typeof ProviderConfigSchema>;
export type SecurityConfig = z.infer<typeof SecurityConfigSchema>;

export class ConfigManager {
  private configPath: string;
  private config: ArienConfig;

  constructor(configDir?: string) {
    const baseDir = configDir || join(homedir(), '.arien');
    this.configPath = join(baseDir, 'config.json');
    this.ensureConfigDir();
    this.config = this.loadConfig();
  }

  private ensureConfigDir(): void {
    const dir = dirname(this.configPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
  }

  private loadConfig(): ArienConfig {
    try {
      if (existsSync(this.configPath)) {
        const configData = JSON.parse(readFileSync(this.configPath, 'utf-8'));
        return ConfigSchema.parse(configData);
      }
    } catch (error) {
      console.warn('Failed to load config, using defaults:', error);
    }
    
    return ConfigSchema.parse({});
  }

  public saveConfig(): void {
    try {
      writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save config: ${error}`);
    }
  }

  public getConfig(): ArienConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<ArienConfig>): void {
    this.config = ConfigSchema.parse({ ...this.config, ...updates });
    this.saveConfig();
  }

  public getProviderConfig(provider: Provider): ProviderConfig {
    return this.config.providers[provider] || {};
  }

  public setProviderConfig(provider: Provider, config: ProviderConfig): void {
    this.config.providers[provider] = config;
    this.saveConfig();
  }

  public getDefaultModel(): string {
    return this.config.defaultModel || DEFAULT_MODELS[this.config.defaultProvider];
  }

  public setDefaultModel(model: string): void {
    this.config.defaultModel = model;
    this.saveConfig();
  }

  public getSecurityConfig(): SecurityConfig {
    return this.config.security;
  }

  public setSecurityConfig(security: Partial<SecurityConfig>): void {
    this.config.security = { ...this.config.security, ...security };
    this.saveConfig();
  }

  public isProviderConfigured(provider: Provider): boolean {
    const config = this.getProviderConfig(provider);
    return !!config.apiKey;
  }

  public getConfiguredProviders(): Provider[] {
    return Object.values(PROVIDERS).filter(provider => this.isProviderConfigured(provider));
  }

  public validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if at least one provider is configured
    const configuredProviders = this.getConfiguredProviders();
    if (configuredProviders.length === 0) {
      errors.push('No providers are configured. Please set up at least one AI provider.');
    }

    // Check if default provider is configured
    if (!this.isProviderConfigured(this.config.defaultProvider)) {
      errors.push(`Default provider '${this.config.defaultProvider}' is not configured.`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  public reset(): void {
    this.config = ConfigSchema.parse({});
    this.saveConfig();
  }
}

// Global config instance
let globalConfig: ConfigManager | null = null;

export function getConfig(): ConfigManager {
  if (!globalConfig) {
    globalConfig = new ConfigManager();
  }
  return globalConfig;
}

export function initConfig(configDir?: string): ConfigManager {
  globalConfig = new ConfigManager(configDir);
  return globalConfig;
}
