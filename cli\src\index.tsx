#!/usr/bin/env node

// CLI entry point
import React from 'react';
import { render } from 'ink';
import { program } from 'commander';
import { App } from './App.js';
import { version } from '../package.json';

// Parse command line arguments
program
  .name('arien')
  .description('AI-powered CLI assistant')
  .version(version)
  .option('-m, --message <message>', 'Initial message to send')
  .option('-c, --config <path>', 'Path to configuration file')
  .option('-t, --theme <theme>', 'Theme to use (default, dark, light, cyberpunk, matrix)')
  .option('-p, --provider <provider>', 'AI provider to use (deepseek, openai, anthropic, google)')
  .option('--model <model>', 'AI model to use')
  .option('--temperature <temp>', 'Temperature for AI responses (0.0-1.0)', parseFloat)
  .option('--max-tokens <tokens>', 'Maximum tokens for AI responses', parseInt)
  .option('--approval-level <level>', 'Tool approval level (default, auto-edit, yolo)')
  .option('--no-sandbox', 'Disable security sandbox')
  .option('--debug', 'Enable debug mode')
  .option('--log-level <level>', 'Log level (error, warn, info, debug)')
  .parse();

const options = program.opts();

// Set environment variables from CLI options
if (options.provider) {
  process.env.ARIEN_PROVIDER = options.provider;
}

if (options.model) {
  process.env.ARIEN_MODEL = options.model;
}

if (options.temperature !== undefined) {
  process.env.ARIEN_TEMPERATURE = options.temperature.toString();
}

if (options.maxTokens) {
  process.env.ARIEN_MAX_TOKENS = options.maxTokens.toString();
}

if (options.approvalLevel) {
  process.env.ARIEN_APPROVAL_LEVEL = options.approvalLevel;
}

if (options.noSandbox) {
  process.env.ARIEN_SANDBOX_ENABLED = 'false';
}

if (options.logLevel) {
  process.env.ARIEN_LOG_LEVEL = options.logLevel;
}

if (options.theme) {
  process.env.ARIEN_THEME = options.theme;
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nGoodbye! 👋');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nShutting down gracefully...');
  process.exit(0);
});

// Render the app
const { unmount } = render(
  <App
    initialMessage={options.message}
    configPath={options.config}
    theme={options.theme}
    debug={options.debug}
  />
);

// Handle app exit
process.on('exit', () => {
  unmount();
});
