// Editor integration utilities
import { spawn, ChildProcess } from 'child_process';
import { writeFile, unlink, access } from 'fs/promises';
import { join, resolve } from 'path';
import { tmpdir } from 'os';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from './errors.js';

export interface EditorConfig {
  command: string;
  args?: string[];
  waitForClose?: boolean;
  openInNewWindow?: boolean;
  lineNumberSupport?: boolean;
  projectSupport?: boolean;
  tempFileExtension?: string;
}

export interface EditorResult {
  success: boolean;
  content?: string;
  exitCode?: number;
  error?: string;
  duration: number;
}

export interface EditorOptions {
  content?: string;
  filePath?: string;
  lineNumber?: number;
  column?: number;
  language?: string;
  readOnly?: boolean;
  timeout?: number;
}

export class EditorManager {
  private static readonly POPULAR_EDITORS: Record<string, EditorConfig> = {
    'code': {
      command: 'code',
      args: ['--wait'],
      waitForClose: true,
      openInNewWindow: false,
      lineNumberSupport: true,
      projectSupport: true,
      tempFileExtension: '.tmp'
    },
    'vim': {
      command: 'vim',
      waitForClose: true,
      lineNumberSupport: true,
      projectSupport: false,
      tempFileExtension: '.tmp'
    },
    'nvim': {
      command: 'nvim',
      waitForClose: true,
      lineNumberSupport: true,
      projectSupport: false,
      tempFileExtension: '.tmp'
    },
    'emacs': {
      command: 'emacs',
      waitForClose: true,
      lineNumberSupport: true,
      projectSupport: true,
      tempFileExtension: '.tmp'
    },
    'subl': {
      command: 'subl',
      args: ['--wait'],
      waitForClose: true,
      openInNewWindow: false,
      lineNumberSupport: true,
      projectSupport: true,
      tempFileExtension: '.tmp'
    },
    'atom': {
      command: 'atom',
      args: ['--wait'],
      waitForClose: true,
      openInNewWindow: false,
      lineNumberSupport: true,
      projectSupport: true,
      tempFileExtension: '.tmp'
    },
    'nano': {
      command: 'nano',
      waitForClose: true,
      lineNumberSupport: true,
      projectSupport: false,
      tempFileExtension: '.tmp'
    }
  };

  private config: EditorConfig;

  constructor(editorName: string, customConfig?: Partial<EditorConfig>) {
    const baseConfig = EditorManager.POPULAR_EDITORS[editorName];
    if (!baseConfig && !customConfig) {
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        `Unknown editor: ${editorName}. Use a known editor or provide custom configuration.`
      );
    }

    this.config = {
      ...baseConfig,
      ...customConfig
    };
  }

  public async openFile(filePath: string, options: EditorOptions = {}): Promise<EditorResult> {
    const startTime = Date.now();

    try {
      const resolvedPath = resolve(filePath);
      
      // Check if file exists
      try {
        await access(resolvedPath);
      } catch {
        throw new ArienError(
          ErrorCode.FILE_NOT_FOUND,
          `File not found: ${filePath}`
        );
      }

      const args = this.buildArgs(resolvedPath, options);
      const result = await this.executeEditor(args, options);

      return {
        ...result,
        duration: Date.now() - startTime
      };
    } catch (error) {
      logger.error('Failed to open file in editor', { filePath, error });
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      };
    }
  }

  public async editContent(content: string, options: EditorOptions = {}): Promise<EditorResult> {
    const startTime = Date.now();
    let tempFilePath: string | null = null;

    try {
      // Create temporary file
      const extension = options.language ? `.${options.language}` : this.config.tempFileExtension || '.tmp';
      tempFilePath = join(tmpdir(), `arien-edit-${Date.now()}${extension}`);
      
      await writeFile(tempFilePath, content, 'utf-8');

      const args = this.buildArgs(tempFilePath, options);
      const result = await this.executeEditor(args, options);

      // Read the modified content if successful
      if (result.success && this.config.waitForClose) {
        try {
          const { readFile } = await import('fs/promises');
          const modifiedContent = await readFile(tempFilePath, 'utf-8');
          result.content = modifiedContent;
        } catch (error) {
          logger.warn('Failed to read modified content', { tempFilePath, error });
        }
      }

      return {
        ...result,
        duration: Date.now() - startTime
      };
    } catch (error) {
      logger.error('Failed to edit content in editor', { error });
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      };
    } finally {
      // Clean up temporary file
      if (tempFilePath) {
        try {
          await unlink(tempFilePath);
        } catch (error) {
          logger.debug('Failed to clean up temporary file', { tempFilePath, error });
        }
      }
    }
  }

  public async openProject(projectPath: string, options: EditorOptions = {}): Promise<EditorResult> {
    const startTime = Date.now();

    try {
      if (!this.config.projectSupport) {
        throw new ArienError(
          ErrorCode.OPERATION_NOT_SUPPORTED,
          `Editor ${this.config.command} does not support project opening`
        );
      }

      const resolvedPath = resolve(projectPath);
      const args = this.buildProjectArgs(resolvedPath, options);
      const result = await this.executeEditor(args, options);

      return {
        ...result,
        duration: Date.now() - startTime
      };
    } catch (error) {
      logger.error('Failed to open project in editor', { projectPath, error });
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      };
    }
  }

  private buildArgs(filePath: string, options: EditorOptions): string[] {
    const args = [...(this.config.args || [])];

    // Add file path
    if (options.lineNumber && this.config.lineNumberSupport) {
      // Different editors have different syntax for line numbers
      switch (this.config.command) {
        case 'code':
          args.push(`${filePath}:${options.lineNumber}${options.column ? `:${options.column}` : ''}`);
          break;
        case 'vim':
        case 'nvim':
          args.push(`+${options.lineNumber}`, filePath);
          break;
        case 'emacs':
          args.push(`+${options.lineNumber}:${options.column || 1}`, filePath);
          break;
        case 'subl':
          args.push(`${filePath}:${options.lineNumber}${options.column ? `:${options.column}` : ''}`);
          break;
        default:
          args.push(filePath);
      }
    } else {
      args.push(filePath);
    }

    // Add read-only flag if supported
    if (options.readOnly) {
      switch (this.config.command) {
        case 'vim':
        case 'nvim':
          args.unshift('-R');
          break;
        case 'emacs':
          args.unshift('--eval', '(setq buffer-read-only t)');
          break;
      }
    }

    return args;
  }

  private buildProjectArgs(projectPath: string, options: EditorOptions): string[] {
    const args = [...(this.config.args || [])];

    switch (this.config.command) {
      case 'code':
        args.push(projectPath);
        if (this.config.openInNewWindow) {
          args.unshift('--new-window');
        }
        break;
      case 'subl':
        args.push('--project', projectPath);
        break;
      case 'atom':
        args.push(projectPath);
        break;
      default:
        args.push(projectPath);
    }

    return args;
  }

  private async executeEditor(args: string[], options: EditorOptions): Promise<Omit<EditorResult, 'duration'>> {
    return new Promise((resolve) => {
      const timeout = options.timeout || 300000; // 5 minutes default
      let timedOut = false;

      const child: ChildProcess = spawn(this.config.command, args, {
        stdio: this.config.waitForClose ? 'inherit' : 'ignore',
        detached: !this.config.waitForClose
      });

      // Set up timeout
      const timeoutId = setTimeout(() => {
        timedOut = true;
        child.kill('SIGTERM');
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);
      }, timeout);

      if (this.config.waitForClose) {
        child.on('close', (code) => {
          clearTimeout(timeoutId);
          
          if (timedOut) {
            resolve({
              success: false,
              error: 'Editor operation timed out',
              exitCode: null
            });
          } else {
            resolve({
              success: code === 0,
              exitCode: code,
              error: code !== 0 ? `Editor exited with code ${code}` : undefined
            });
          }
        });

        child.on('error', (error) => {
          clearTimeout(timeoutId);
          resolve({
            success: false,
            error: error.message,
            exitCode: null
          });
        });
      } else {
        // For non-waiting editors, assume success if spawn succeeds
        clearTimeout(timeoutId);
        resolve({
          success: true,
          exitCode: 0
        });
      }
    });
  }

  public static async detectAvailableEditors(): Promise<string[]> {
    const available: string[] = [];

    for (const editorName of Object.keys(EditorManager.POPULAR_EDITORS)) {
      try {
        const { spawn } = await import('child_process');
        const child = spawn(editorName, ['--version'], { stdio: 'ignore' });
        
        await new Promise<void>((resolve) => {
          child.on('close', (code) => {
            if (code === 0) {
              available.push(editorName);
            }
            resolve();
          });
          
          child.on('error', () => {
            resolve();
          });
        });
      } catch {
        // Editor not available
      }
    }

    return available;
  }

  public static getEditorConfig(editorName: string): EditorConfig | null {
    return EditorManager.POPULAR_EDITORS[editorName] || null;
  }
}

// Convenience functions
export async function openFileInEditor(
  editorName: string,
  filePath: string,
  options: EditorOptions = {}
): Promise<EditorResult> {
  const editor = new EditorManager(editorName);
  return editor.openFile(filePath, options);
}

export async function editContentInEditor(
  editorName: string,
  content: string,
  options: EditorOptions = {}
): Promise<EditorResult> {
  const editor = new EditorManager(editorName);
  return editor.editContent(content, options);
}

export async function openProjectInEditor(
  editorName: string,
  projectPath: string,
  options: EditorOptions = {}
): Promise<EditorResult> {
  const editor = new EditorManager(editorName);
  return editor.openProject(projectPath, options);
}
