// Conversation turn management
import { ChatMessage } from './client.js';
import { logger } from './logger.js';
import { v4 as uuidv4 } from 'uuid';

export interface Turn {
  id: string;
  sessionId: string;
  userMessage: ChatMessage;
  assistantMessage?: ChatMessage;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  status: 'pending' | 'completed' | 'failed' | 'aborted';
  error?: Error;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: Record<string, any>;
}

export interface TurnMetrics {
  totalTurns: number;
  completedTurns: number;
  failedTurns: number;
  averageDuration: number;
  totalTokensUsed: number;
  averageTokensPerTurn: number;
}

export class TurnManager {
  private turns = new Map<string, Turn>();
  private sessionTurns = new Map<string, string[]>();

  public createTurn(sessionId: string, userMessage: ChatMessage): Turn {
    const turn: Turn = {
      id: uuidv4(),
      sessionId,
      userMessage,
      startTime: new Date(),
      status: 'pending'
    };

    this.turns.set(turn.id, turn);
    
    // Add to session turns
    if (!this.sessionTurns.has(sessionId)) {
      this.sessionTurns.set(sessionId, []);
    }
    this.sessionTurns.get(sessionId)!.push(turn.id);

    logger.debug('Turn created', {
      turnId: turn.id,
      sessionId,
      messageLength: userMessage.content.length
    });

    return turn;
  }

  public completeTurn(
    turnId: string,
    assistantMessage: ChatMessage,
    usage?: { promptTokens: number; completionTokens: number; totalTokens: number }
  ): Turn {
    const turn = this.turns.get(turnId);
    if (!turn) {
      throw new Error(`Turn ${turnId} not found`);
    }

    const endTime = new Date();
    const duration = endTime.getTime() - turn.startTime.getTime();

    turn.assistantMessage = assistantMessage;
    turn.endTime = endTime;
    turn.duration = duration;
    turn.status = 'completed';
    turn.usage = usage;

    logger.debug('Turn completed', {
      turnId,
      sessionId: turn.sessionId,
      duration,
      usage,
      responseLength: assistantMessage.content.length
    });

    return turn;
  }

  public failTurn(turnId: string, error: Error): Turn {
    const turn = this.turns.get(turnId);
    if (!turn) {
      throw new Error(`Turn ${turnId} not found`);
    }

    const endTime = new Date();
    const duration = endTime.getTime() - turn.startTime.getTime();

    turn.endTime = endTime;
    turn.duration = duration;
    turn.status = 'failed';
    turn.error = error;

    logger.error('Turn failed', {
      turnId,
      sessionId: turn.sessionId,
      duration,
      error: error.message
    });

    return turn;
  }

  public abortTurn(turnId: string): Turn {
    const turn = this.turns.get(turnId);
    if (!turn) {
      throw new Error(`Turn ${turnId} not found`);
    }

    const endTime = new Date();
    const duration = endTime.getTime() - turn.startTime.getTime();

    turn.endTime = endTime;
    turn.duration = duration;
    turn.status = 'aborted';

    logger.debug('Turn aborted', {
      turnId,
      sessionId: turn.sessionId,
      duration
    });

    return turn;
  }

  public getTurn(turnId: string): Turn | undefined {
    return this.turns.get(turnId);
  }

  public getSessionTurns(sessionId: string): Turn[] {
    const turnIds = this.sessionTurns.get(sessionId) || [];
    return turnIds.map(id => this.turns.get(id)!).filter(Boolean);
  }

  public getLastTurn(sessionId: string): Turn | undefined {
    const turns = this.getSessionTurns(sessionId);
    return turns.length > 0 ? turns[turns.length - 1] : undefined;
  }

  public getSessionMetrics(sessionId: string): TurnMetrics {
    const turns = this.getSessionTurns(sessionId);
    
    const completedTurns = turns.filter(t => t.status === 'completed');
    const failedTurns = turns.filter(t => t.status === 'failed');
    
    const totalDuration = completedTurns.reduce((sum, t) => sum + (t.duration || 0), 0);
    const averageDuration = completedTurns.length > 0 ? totalDuration / completedTurns.length : 0;
    
    const totalTokens = completedTurns.reduce((sum, t) => sum + (t.usage?.totalTokens || 0), 0);
    const averageTokensPerTurn = completedTurns.length > 0 ? totalTokens / completedTurns.length : 0;

    return {
      totalTurns: turns.length,
      completedTurns: completedTurns.length,
      failedTurns: failedTurns.length,
      averageDuration,
      totalTokensUsed: totalTokens,
      averageTokensPerTurn
    };
  }

  public getAllMetrics(): TurnMetrics {
    const allTurns = Array.from(this.turns.values());
    
    const completedTurns = allTurns.filter(t => t.status === 'completed');
    const failedTurns = allTurns.filter(t => t.status === 'failed');
    
    const totalDuration = completedTurns.reduce((sum, t) => sum + (t.duration || 0), 0);
    const averageDuration = completedTurns.length > 0 ? totalDuration / completedTurns.length : 0;
    
    const totalTokens = completedTurns.reduce((sum, t) => sum + (t.usage?.totalTokens || 0), 0);
    const averageTokensPerTurn = completedTurns.length > 0 ? totalTokens / completedTurns.length : 0;

    return {
      totalTurns: allTurns.length,
      completedTurns: completedTurns.length,
      failedTurns: failedTurns.length,
      averageDuration,
      totalTokensUsed: totalTokens,
      averageTokensPerTurn
    };
  }

  public cleanupOldTurns(maxAge: number = 24 * 60 * 60 * 1000): number {
    const cutoffTime = new Date(Date.now() - maxAge);
    let cleanedCount = 0;

    for (const [turnId, turn] of this.turns.entries()) {
      if (turn.startTime < cutoffTime) {
        this.turns.delete(turnId);
        
        // Remove from session turns
        const sessionTurnIds = this.sessionTurns.get(turn.sessionId);
        if (sessionTurnIds) {
          const index = sessionTurnIds.indexOf(turnId);
          if (index > -1) {
            sessionTurnIds.splice(index, 1);
          }
          
          // Clean up empty session entries
          if (sessionTurnIds.length === 0) {
            this.sessionTurns.delete(turn.sessionId);
          }
        }
        
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.info('Cleaned up old turns', { cleanedCount, cutoffTime });
    }

    return cleanedCount;
  }

  public clearSession(sessionId: string): number {
    const turnIds = this.sessionTurns.get(sessionId) || [];
    
    for (const turnId of turnIds) {
      this.turns.delete(turnId);
    }
    
    this.sessionTurns.delete(sessionId);
    
    logger.info('Session turns cleared', { sessionId, turnCount: turnIds.length });
    
    return turnIds.length;
  }

  public getTurnCount(): number {
    return this.turns.size;
  }

  public getSessionCount(): number {
    return this.sessionTurns.size;
  }
}
