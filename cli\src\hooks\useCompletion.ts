// Tab completion functionality hook
import { useState, useCallback, useMemo } from 'react';
import { readdir, stat } from 'fs/promises';
import { join, dirname, basename, resolve } from 'path';
import { getConfig } from '@arien/core';

export interface CompletionItem {
  text: string;
  type: 'command' | 'file' | 'directory' | 'option' | 'provider' | 'model';
  description?: string;
  insertText?: string;
  priority?: number;
}

export interface CompletionContext {
  input: string;
  cursorPosition: number;
  workingDirectory: string;
  currentWord: string;
  previousWord?: string;
  isAtStart: boolean;
}

export interface UseCompletionReturn {
  completions: CompletionItem[];
  selectedIndex: number;
  isVisible: boolean;
  getCompletions: (context: CompletionContext) => Promise<CompletionItem[]>;
  selectNext: () => void;
  selectPrevious: () => void;
  acceptCompletion: () => string | null;
  hideCompletions: () => void;
  showCompletions: (context: CompletionContext) => Promise<void>;
}

export function useCompletion(): UseCompletionReturn {
  const [completions, setCompletions] = useState<CompletionItem[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [currentContext, setCurrentContext] = useState<CompletionContext | null>(null);

  // Built-in commands for completion
  const builtinCommands = useMemo(() => [
    { text: '/help', type: 'command' as const, description: 'Show help information' },
    { text: '/clear', type: 'command' as const, description: 'Clear chat history' },
    { text: '/config', type: 'command' as const, description: 'Show or modify configuration' },
    { text: '/status', type: 'command' as const, description: 'Show system status' },
    { text: '/theme', type: 'command' as const, description: 'Change UI theme' },
    { text: '/exit', type: 'command' as const, description: 'Exit the application' },
    { text: '/history', type: 'command' as const, description: 'Show command history' },
    { text: '/save', type: 'command' as const, description: 'Save conversation' },
    { text: '/load', type: 'command' as const, description: 'Load conversation' }
  ], []);

  // Get file and directory completions
  const getFileCompletions = useCallback(async (
    path: string,
    workingDir: string
  ): Promise<CompletionItem[]> => {
    try {
      const absolutePath = resolve(workingDir, path);
      const dirPath = dirname(absolutePath);
      const prefix = basename(absolutePath);

      const entries = await readdir(dirPath);
      const completions: CompletionItem[] = [];

      for (const entry of entries) {
        if (!entry.startsWith(prefix)) continue;

        try {
          const entryPath = join(dirPath, entry);
          const stats = await stat(entryPath);
          
          completions.push({
            text: entry,
            type: stats.isDirectory() ? 'directory' : 'file',
            description: stats.isDirectory() ? 'Directory' : 'File',
            insertText: stats.isDirectory() ? `${entry}/` : entry,
            priority: stats.isDirectory() ? 1 : 2
          });
        } catch {
          // Skip entries we can't stat
        }
      }

      return completions.sort((a, b) => {
        // Directories first, then files, then by name
        if (a.priority !== b.priority) {
          return (a.priority || 0) - (b.priority || 0);
        }
        return a.text.localeCompare(b.text);
      });
    } catch {
      return [];
    }
  }, []);

  // Get provider completions
  const getProviderCompletions = useCallback((): CompletionItem[] => {
    return [
      { text: 'deepseek', type: 'provider', description: 'DeepSeek AI models' },
      { text: 'openai', type: 'provider', description: 'OpenAI GPT models' },
      { text: 'anthropic', type: 'provider', description: 'Anthropic Claude models' },
      { text: 'google', type: 'provider', description: 'Google Gemini models' }
    ];
  }, []);

  // Get model completions
  const getModelCompletions = useCallback((provider?: string): CompletionItem[] => {
    const models: Record<string, CompletionItem[]> = {
      deepseek: [
        { text: 'deepseek-chat', type: 'model', description: 'DeepSeek Chat model' },
        { text: 'deepseek-coder', type: 'model', description: 'DeepSeek Coder model' }
      ],
      openai: [
        { text: 'gpt-4', type: 'model', description: 'GPT-4 model' },
        { text: 'gpt-4-turbo', type: 'model', description: 'GPT-4 Turbo model' },
        { text: 'gpt-3.5-turbo', type: 'model', description: 'GPT-3.5 Turbo model' }
      ],
      anthropic: [
        { text: 'claude-3-opus', type: 'model', description: 'Claude 3 Opus model' },
        { text: 'claude-3-sonnet', type: 'model', description: 'Claude 3 Sonnet model' },
        { text: 'claude-3-haiku', type: 'model', description: 'Claude 3 Haiku model' }
      ],
      google: [
        { text: 'gemini-pro', type: 'model', description: 'Gemini Pro model' },
        { text: 'gemini-pro-vision', type: 'model', description: 'Gemini Pro Vision model' }
      ]
    };

    if (provider && models[provider]) {
      return models[provider];
    }

    // Return all models if no specific provider
    return Object.values(models).flat();
  }, []);

  // Get config option completions
  const getConfigCompletions = useCallback((subcommand?: string): CompletionItem[] => {
    if (subcommand === 'get' || subcommand === 'set') {
      return [
        { text: 'defaultProvider', type: 'option', description: 'Default AI provider' },
        { text: 'defaultModel', type: 'option', description: 'Default AI model' },
        { text: 'theme', type: 'option', description: 'UI theme' },
        { text: 'security.approvalLevel', type: 'option', description: 'Tool approval level' },
        { text: 'debug', type: 'option', description: 'Debug mode' },
        { text: 'telemetry', type: 'option', description: 'Telemetry enabled' }
      ];
    }

    return [
      { text: 'get', type: 'option', description: 'Get configuration value' },
      { text: 'set', type: 'option', description: 'Set configuration value' },
      { text: 'list', type: 'option', description: 'List all configuration' }
    ];
  }, []);

  // Main completion logic
  const getCompletions = useCallback(async (
    context: CompletionContext
  ): Promise<CompletionItem[]> => {
    const { input, currentWord, previousWord, isAtStart } = context;
    let completions: CompletionItem[] = [];

    // Slash commands
    if (isAtStart && currentWord.startsWith('/')) {
      completions = builtinCommands.filter(cmd => 
        cmd.text.startsWith(currentWord)
      );
    }
    // File references (@ prefix)
    else if (currentWord.startsWith('@')) {
      const path = currentWord.slice(1);
      const fileCompletions = await getFileCompletions(path, context.workingDirectory);
      completions = fileCompletions.map(comp => ({
        ...comp,
        text: `@${comp.text}`,
        insertText: `@${comp.insertText || comp.text}`
      }));
    }
    // Config command completions
    else if (input.startsWith('/config')) {
      const parts = input.split(/\s+/);
      if (parts.length === 2) {
        // Completing subcommand
        completions = getConfigCompletions();
      } else if (parts.length === 3 && (parts[1] === 'get' || parts[1] === 'set')) {
        // Completing config key
        completions = getConfigCompletions(parts[1]);
      } else if (parts.length === 4 && parts[1] === 'set' && parts[2] === 'defaultProvider') {
        // Completing provider value
        completions = getProviderCompletions();
      } else if (parts.length === 4 && parts[1] === 'set' && parts[2] === 'defaultModel') {
        // Completing model value
        completions = getModelCompletions();
      }
    }
    // Theme command completions
    else if (input.startsWith('/theme')) {
      completions = [
        { text: 'default', type: 'option', description: 'Default theme' },
        { text: 'dark', type: 'option', description: 'Dark theme' },
        { text: 'light', type: 'option', description: 'Light theme' },
        { text: 'cyberpunk', type: 'option', description: 'Cyberpunk theme' },
        { text: 'matrix', type: 'option', description: 'Matrix theme' }
      ];
    }
    // File path completions for regular input
    else if (currentWord.includes('/') || currentWord.includes('\\')) {
      completions = await getFileCompletions(currentWord, context.workingDirectory);
    }

    // Filter by current word
    if (currentWord && !currentWord.startsWith('@') && !currentWord.startsWith('/')) {
      completions = completions.filter(comp =>
        comp.text.toLowerCase().startsWith(currentWord.toLowerCase())
      );
    }

    return completions;
  }, [builtinCommands, getFileCompletions, getProviderCompletions, getModelCompletions, getConfigCompletions]);

  const showCompletions = useCallback(async (context: CompletionContext) => {
    setCurrentContext(context);
    const items = await getCompletions(context);
    setCompletions(items);
    setSelectedIndex(0);
    setIsVisible(items.length > 0);
  }, [getCompletions]);

  const hideCompletions = useCallback(() => {
    setIsVisible(false);
    setCompletions([]);
    setSelectedIndex(0);
    setCurrentContext(null);
  }, []);

  const selectNext = useCallback(() => {
    setSelectedIndex(prev => 
      prev < completions.length - 1 ? prev + 1 : 0
    );
  }, [completions.length]);

  const selectPrevious = useCallback(() => {
    setSelectedIndex(prev => 
      prev > 0 ? prev - 1 : completions.length - 1
    );
  }, [completions.length]);

  const acceptCompletion = useCallback((): string | null => {
    if (!isVisible || completions.length === 0 || !currentContext) {
      return null;
    }

    const completion = completions[selectedIndex];
    if (!completion) return null;

    const { input, cursorPosition, currentWord } = currentContext;
    
    // Find the start of the current word
    let wordStart = cursorPosition;
    while (wordStart > 0 && !/\s/.test(input[wordStart - 1])) {
      wordStart--;
    }

    // Replace the current word with the completion
    const before = input.slice(0, wordStart);
    const after = input.slice(cursorPosition);
    const insertText = completion.insertText || completion.text;
    
    hideCompletions();
    
    return before + insertText + after;
  }, [isVisible, completions, selectedIndex, currentContext, hideCompletions]);

  return {
    completions,
    selectedIndex,
    isVisible,
    getCompletions,
    selectNext,
    selectPrevious,
    acceptCompletion,
    hideCompletions,
    showCompletions
  };
}
