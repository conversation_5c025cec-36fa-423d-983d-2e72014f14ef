// @ commands processor (file operations)
import { useState, useCallback } from 'react';
import { readFile, stat } from 'fs/promises';
import { resolve, extname, basename } from 'path';
import { glob } from 'fast-glob';

export interface FileReference {
  path: string;
  content?: string;
  size?: number;
  type: 'file' | 'directory' | 'glob';
  error?: string;
}

export interface AtCommandResult {
  files: FileReference[];
  totalSize: number;
  errors: string[];
  command: string;
}

export interface UseAtCommandProcessorReturn {
  processAtCommand: (command: string, workingDir?: string) => Promise<AtCommandResult>;
  isProcessing: boolean;
  lastResult: AtCommandResult | null;
  error: string | null;
}

export function useAtCommandProcessor(): UseAtCommandProcessorReturn {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastResult, setLastResult] = useState<AtCommandResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const processAtCommand = useCallback(async (
    command: string,
    workingDir: string = process.cwd()
  ): Promise<AtCommandResult> => {
    setIsProcessing(true);
    setError(null);

    try {
      // Parse @ commands from the input
      const atCommands = command.match(/@[\w\-\.\/\*\?]+/g) || [];
      
      if (atCommands.length === 0) {
        const result: AtCommandResult = {
          files: [],
          totalSize: 0,
          errors: [],
          command
        };
        setLastResult(result);
        return result;
      }

      const files: FileReference[] = [];
      const errors: string[] = [];
      let totalSize = 0;

      for (const atCommand of atCommands) {
        const path = atCommand.slice(1); // Remove @ prefix
        const absolutePath = resolve(workingDir, path);

        try {
          // Check if it's a glob pattern
          if (path.includes('*') || path.includes('?') || path.includes('[')) {
            const globResults = await glob(path, {
              cwd: workingDir,
              absolute: true,
              onlyFiles: true,
              ignore: ['node_modules/**', '.git/**', 'dist/**', 'build/**']
            });

            for (const globPath of globResults) {
              try {
                const stats = await stat(globPath);
                const content = await readFile(globPath, 'utf-8');
                
                files.push({
                  path: globPath,
                  content,
                  size: stats.size,
                  type: 'file'
                });
                
                totalSize += stats.size;
              } catch (err) {
                errors.push(`Failed to read ${globPath}: ${err instanceof Error ? err.message : String(err)}`);
              }
            }

            if (globResults.length === 0) {
              files.push({
                path,
                type: 'glob',
                error: 'No files matched the pattern'
              });
            }
          } else {
            // Single file or directory
            const stats = await stat(absolutePath);

            if (stats.isDirectory()) {
              // List directory contents
              const dirFiles = await glob('**/*', {
                cwd: absolutePath,
                absolute: true,
                onlyFiles: true,
                ignore: ['node_modules/**', '.git/**'],
                deep: 2 // Limit depth
              });

              files.push({
                path: absolutePath,
                type: 'directory',
                size: dirFiles.length
              });

              // Add first few files from directory
              for (const dirFile of dirFiles.slice(0, 10)) {
                try {
                  const fileStats = await stat(dirFile);
                  const content = await readFile(dirFile, 'utf-8');
                  
                  files.push({
                    path: dirFile,
                    content,
                    size: fileStats.size,
                    type: 'file'
                  });
                  
                  totalSize += fileStats.size;
                } catch (err) {
                  errors.push(`Failed to read ${dirFile}: ${err instanceof Error ? err.message : String(err)}`);
                }
              }

              if (dirFiles.length > 10) {
                errors.push(`Directory ${path} contains ${dirFiles.length} files, showing first 10`);
              }
            } else {
              // Single file
              const content = await readFile(absolutePath, 'utf-8');
              
              files.push({
                path: absolutePath,
                content,
                size: stats.size,
                type: 'file'
              });
              
              totalSize += stats.size;
            }
          }
        } catch (err) {
          errors.push(`Failed to process ${path}: ${err instanceof Error ? err.message : String(err)}`);
          files.push({
            path,
            type: 'file',
            error: err instanceof Error ? err.message : String(err)
          });
        }
      }

      const result: AtCommandResult = {
        files,
        totalSize,
        errors,
        command
      };

      setLastResult(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      
      const result: AtCommandResult = {
        files: [],
        totalSize: 0,
        errors: [errorMessage],
        command
      };
      
      setLastResult(result);
      return result;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  return {
    processAtCommand,
    isProcessing,
    lastResult,
    error
  };
}
