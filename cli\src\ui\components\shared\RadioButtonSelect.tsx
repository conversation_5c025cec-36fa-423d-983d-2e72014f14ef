// Radio button selection component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../../hooks/useTheme.js';

export interface RadioOption {
  label: string;
  value: string;
  description?: string;
  disabled?: boolean;
  icon?: string;
}

interface RadioButtonSelectProps {
  options: RadioOption[];
  value?: string;
  onChange?: (value: string) => void;
  onSubmit?: (value: string) => void;
  title?: string;
  allowDeselect?: boolean;
  showDescriptions?: boolean;
  layout?: 'vertical' | 'horizontal';
  disabled?: boolean;
}

export const RadioButtonSelect: React.FC<RadioButtonSelectProps> = ({
  options,
  value,
  onChange,
  onSubmit,
  title,
  allowDeselect = false,
  showDescriptions = true,
  layout = 'vertical',
  disabled = false
}) => {
  const theme = useTheme();
  const [selectedValue, setSelectedValue] = useState(value || '');
  const [highlightedIndex, setHighlightedIndex] = useState(
    value ? options.findIndex(opt => opt.value === value) : 0
  );

  useInput((input, key) => {
    if (disabled) return;

    if (key.upArrow && layout === 'vertical') {
      setHighlightedIndex(prev => {
        const newIndex = prev > 0 ? prev - 1 : options.length - 1;
        return options[newIndex].disabled ? prev : newIndex;
      });
    } else if (key.downArrow && layout === 'vertical') {
      setHighlightedIndex(prev => {
        const newIndex = prev < options.length - 1 ? prev + 1 : 0;
        return options[newIndex].disabled ? prev : newIndex;
      });
    } else if (key.leftArrow && layout === 'horizontal') {
      setHighlightedIndex(prev => {
        const newIndex = prev > 0 ? prev - 1 : options.length - 1;
        return options[newIndex].disabled ? prev : newIndex;
      });
    } else if (key.rightArrow && layout === 'horizontal') {
      setHighlightedIndex(prev => {
        const newIndex = prev < options.length - 1 ? prev + 1 : 0;
        return options[newIndex].disabled ? prev : newIndex;
      });
    } else if (input === ' ' || key.return) {
      const option = options[highlightedIndex];
      if (option && !option.disabled) {
        const newValue = selectedValue === option.value && allowDeselect ? '' : option.value;
        setSelectedValue(newValue);
        onChange?.(newValue);
        
        if (key.return) {
          onSubmit?.(newValue);
        }
      }
    }
  });

  const renderOption = (option: RadioOption, index: number) => {
    const isSelected = selectedValue === option.value;
    const isHighlighted = highlightedIndex === index;
    const isDisabled = option.disabled || disabled;

    const getOptionColor = () => {
      if (isDisabled) return theme.colors.muted;
      if (isSelected) return theme.colors.success;
      if (isHighlighted) return theme.colors.primary;
      return theme.colors.text;
    };

    const getRadioIcon = () => {
      if (isSelected) return '●';
      return '○';
    };

    const optionContent = (
      <Box
        key={option.value}
        flexDirection="column"
        paddingX={isHighlighted ? 1 : 0}
        borderStyle={isHighlighted ? 'single' : undefined}
        borderColor={isHighlighted ? theme.colors.primary : undefined}
      >
        {/* Main option */}
        <Box flexDirection="row">
          <Text color={getOptionColor()}>
            {getRadioIcon()} 
          </Text>
          {option.icon && (
            <Text color={getOptionColor()}>
              {option.icon} 
            </Text>
          )}
          <Text color={getOptionColor()}>
            {option.label}
          </Text>
          {isDisabled && (
            <Text color={theme.colors.muted}> (disabled)</Text>
          )}
        </Box>

        {/* Description */}
        {showDescriptions && option.description && (
          <Box marginLeft={2} marginTop={0}>
            <Text color={theme.colors.muted}>
              {option.description}
            </Text>
          </Box>
        )}
      </Box>
    );

    if (layout === 'horizontal') {
      return (
        <Box key={option.value} marginRight={2}>
          {optionContent}
        </Box>
      );
    }

    return optionContent;
  };

  return (
    <Box flexDirection="column">
      {/* Title */}
      {title && (
        <Box marginBottom={1}>
          <Text color={theme.colors.primary}>{title}</Text>
        </Box>
      )}

      {/* Options */}
      <Box flexDirection={layout === 'horizontal' ? 'row' : 'column'}>
        {options.map((option, index) => renderOption(option, index))}
      </Box>

      {/* Instructions */}
      <Box marginTop={1}>
        <Text color={theme.colors.muted}>
          Use {layout === 'vertical' ? '↑↓' : '←→'} to navigate, Space to select
          {onSubmit && ', Enter to confirm'}
          {allowDeselect && ', Space again to deselect'}
        </Text>
      </Box>
    </Box>
  );
};
