// File pattern matching tool
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { minimatch } from 'minimatch';
import { BaseTool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const GlobSchema = z.object({
  pattern: z.string().describe('Glob pattern to match files (e.g., "**/*.ts", "src/**/*.js")'),
  cwd: z.string().optional().describe('Working directory to search in (defaults to current directory)'),
  includeHidden: z.boolean().optional().default(false).describe('Include hidden files and directories'),
  followSymlinks: z.boolean().optional().default(false).describe('Follow symbolic links'),
  maxDepth: z.number().optional().default(10).describe('Maximum directory depth to search'),
  exclude: z.array(z.string()).optional().describe('Patterns to exclude from results'),
  caseSensitive: z.boolean().optional().default(true).describe('Case sensitive pattern matching'),
  onlyFiles: z.boolean().optional().default(false).describe('Return only files, not directories'),
  onlyDirectories: z.boolean().optional().default(false).describe('Return only directories, not files'),
  absolute: z.boolean().optional().default(false).describe('Return absolute paths instead of relative')
});

export interface GlobResult {
  matches: string[];
  totalMatches: number;
  searchTime: number;
  pattern: string;
  searchDirectory: string;
}

export interface FileInfo {
  path: string;
  type: 'file' | 'directory' | 'symlink';
  size?: number;
  modified?: Date;
  permissions?: string;
}

export class GlobTool extends BaseTool {
  constructor() {
    super({
      name: 'glob',
      description: 'Find files and directories using glob patterns',
      parameters: GlobSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const startTime = Date.now();

      const searchDirectory = validatedParams.cwd 
        ? path.resolve(context.workingDirectory, validatedParams.cwd)
        : context.workingDirectory;

      // Security check - ensure search directory is within working directory
      if (!searchDirectory.startsWith(context.workingDirectory)) {
        throw new ToolExecutionError(
          this.definition.name,
          'Search directory is outside the allowed working directory'
        );
      }

      logger.debug('Starting glob search', {
        pattern: validatedParams.pattern,
        searchDirectory,
        options: this.sanitizeParams(validatedParams)
      });

      const matches = await this.findMatches(searchDirectory, validatedParams);
      const searchTime = Date.now() - startTime;

      const result: GlobResult = {
        matches,
        totalMatches: matches.length,
        searchTime,
        pattern: validatedParams.pattern,
        searchDirectory
      };

      logger.info('Glob search completed', {
        pattern: validatedParams.pattern,
        totalMatches: matches.length,
        searchTime
      });

      return this.createSuccessResult(
        `Found ${matches.length} matches for pattern "${validatedParams.pattern}"`,
        result
      );

    } catch (error) {
      logger.error('Glob search failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async findMatches(searchDirectory: string, params: any): Promise<string[]> {
    const matches: string[] = [];
    const visited = new Set<string>();

    await this.searchRecursive(
      searchDirectory,
      searchDirectory,
      params,
      matches,
      visited,
      0
    );

    // Sort matches for consistent output
    matches.sort();

    return matches;
  }

  private async searchRecursive(
    currentDir: string,
    baseDir: string,
    params: any,
    matches: string[],
    visited: Set<string>,
    depth: number
  ): Promise<void> {
    // Check depth limit
    if (depth > params.maxDepth) {
      return;
    }

    // Avoid infinite loops with symlinks
    const realPath = await fs.realpath(currentDir);
    if (visited.has(realPath)) {
      return;
    }
    visited.add(realPath);

    try {
      const entries = await fs.readdir(currentDir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        const relativePath = path.relative(baseDir, fullPath);

        // Skip hidden files/directories if not requested
        if (!params.includeHidden && entry.name.startsWith('.')) {
          continue;
        }

        // Check if this path should be excluded
        if (this.isExcluded(relativePath, params.exclude || [])) {
          continue;
        }

        // Determine entry type
        let entryType: 'file' | 'directory' | 'symlink' = 'file';
        if (entry.isDirectory()) {
          entryType = 'directory';
        } else if (entry.isSymbolicLink()) {
          entryType = 'symlink';
        }

        // Apply file/directory filters
        if (params.onlyFiles && entryType !== 'file') {
          if (entryType === 'directory') {
            // Still recurse into directories even if we only want files
            await this.searchRecursive(
              fullPath,
              baseDir,
              params,
              matches,
              visited,
              depth + 1
            );
          }
          continue;
        }

        if (params.onlyDirectories && entryType !== 'directory') {
          continue;
        }

        // Test against pattern
        const testPath = params.absolute ? fullPath : relativePath;
        if (this.matchesPattern(testPath, params.pattern, params.caseSensitive)) {
          matches.push(testPath);
        }

        // Recurse into directories
        if (entryType === 'directory') {
          await this.searchRecursive(
            fullPath,
            baseDir,
            params,
            matches,
            visited,
            depth + 1
          );
        } else if (entryType === 'symlink' && params.followSymlinks) {
          try {
            const stat = await fs.stat(fullPath);
            if (stat.isDirectory()) {
              await this.searchRecursive(
                fullPath,
                baseDir,
                params,
                matches,
                visited,
                depth + 1
              );
            }
          } catch {
            // Ignore broken symlinks
          }
        }
      }
    } catch (error) {
      // Log but don't fail on permission errors
      logger.debug('Directory access failed', {
        directory: currentDir,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private matchesPattern(filePath: string, pattern: string, caseSensitive: boolean): boolean {
    const options = {
      dot: true, // Allow matching hidden files if pattern includes them
      nocase: !caseSensitive,
      matchBase: false,
      flipNegate: false
    };

    return minimatch(filePath, pattern, options);
  }

  private isExcluded(filePath: string, excludePatterns: string[]): boolean {
    return excludePatterns.some(pattern => 
      minimatch(filePath, pattern, { dot: true })
    );
  }

  public async getFileInfo(filePath: string, context: ToolContext): Promise<FileInfo> {
    const absolutePath = path.resolve(context.workingDirectory, filePath);
    
    try {
      const stat = await fs.stat(absolutePath);
      const lstat = await fs.lstat(absolutePath);

      let type: 'file' | 'directory' | 'symlink' = 'file';
      if (lstat.isSymbolicLink()) {
        type = 'symlink';
      } else if (stat.isDirectory()) {
        type = 'directory';
      }

      return {
        path: filePath,
        type,
        size: type === 'file' ? stat.size : undefined,
        modified: stat.mtime,
        permissions: this.formatPermissions(stat.mode)
      };
    } catch (error) {
      throw new ToolExecutionError(
        this.definition.name,
        `Failed to get file info for ${filePath}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private formatPermissions(mode: number): string {
    const permissions = [];
    
    // Owner permissions
    permissions.push((mode & 0o400) ? 'r' : '-');
    permissions.push((mode & 0o200) ? 'w' : '-');
    permissions.push((mode & 0o100) ? 'x' : '-');
    
    // Group permissions
    permissions.push((mode & 0o040) ? 'r' : '-');
    permissions.push((mode & 0o020) ? 'w' : '-');
    permissions.push((mode & 0o010) ? 'x' : '-');
    
    // Other permissions
    permissions.push((mode & 0o004) ? 'r' : '-');
    permissions.push((mode & 0o002) ? 'w' : '-');
    permissions.push((mode & 0o001) ? 'x' : '-');
    
    return permissions.join('');
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Limit pattern length for logging
    if (sanitized.pattern && sanitized.pattern.length > 100) {
      sanitized.pattern = sanitized.pattern.substring(0, 100) + '...';
    }
    
    return sanitized;
  }
}
