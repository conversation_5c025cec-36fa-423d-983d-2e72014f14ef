// Directory listing tool
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { BaseTool, <PERSON><PERSON><PERSON><PERSON>ult, <PERSON>lContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const LsSchema = z.object({
  path: z.string().optional().describe('Directory path to list (defaults to current directory)'),
  all: z.boolean().optional().default(false).describe('Show hidden files and directories'),
  long: z.boolean().optional().default(false).describe('Use long listing format with detailed information'),
  recursive: z.boolean().optional().default(false).describe('List directories recursively'),
  sortBy: z.enum(['name', 'size', 'modified', 'type']).optional().default('name').describe('Sort entries by specified field'),
  reverse: z.boolean().optional().default(false).describe('Reverse sort order'),
  maxDepth: z.number().optional().default(3).describe('Maximum recursion depth'),
  filter: z.string().optional().describe('Filter entries by pattern (glob-style)'),
  showSize: z.boolean().optional().default(true).describe('Show file sizes'),
  humanReadable: z.boolean().optional().default(true).describe('Show sizes in human readable format')
});

export interface FileEntry {
  name: string;
  path: string;
  type: 'file' | 'directory' | 'symlink' | 'other';
  size?: number;
  sizeHuman?: string;
  modified: Date;
  permissions: string;
  owner?: string;
  group?: string;
  isHidden: boolean;
  target?: string; // For symlinks
}

export interface LsResult {
  entries: FileEntry[];
  totalEntries: number;
  totalSize: number;
  totalSizeHuman: string;
  path: string;
  isRecursive: boolean;
}

export class LsTool extends BaseTool {
  constructor() {
    super({
      name: 'ls',
      description: 'List directory contents with detailed information',
      parameters: LsSchema,
      requiresApproval: false,
      riskLevel: 'low',
      category: 'file'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);

      const targetPath = validatedParams.path 
        ? path.resolve(context.workingDirectory, validatedParams.path)
        : context.workingDirectory;

      // Security check - ensure target path is within working directory
      if (!targetPath.startsWith(context.workingDirectory)) {
        throw new ToolExecutionError(
          this.definition.name,
          'Target path is outside the allowed working directory'
        );
      }

      logger.debug('Listing directory contents', {
        targetPath,
        options: this.sanitizeParams(validatedParams)
      });

      const result = await this.listDirectory(targetPath, validatedParams, 0);

      logger.info('Directory listing completed', {
        targetPath,
        totalEntries: result.totalEntries,
        totalSize: result.totalSizeHuman
      });

      const output = this.formatOutput(result, validatedParams);

      return this.createSuccessResult(output, result);

    } catch (error) {
      logger.error('Directory listing failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async listDirectory(
    dirPath: string,
    params: any,
    currentDepth: number
  ): Promise<LsResult> {
    const entries: FileEntry[] = [];
    let totalSize = 0;

    try {
      // Check if path exists and is accessible
      const stat = await fs.stat(dirPath);
      if (!stat.isDirectory()) {
        throw new ToolExecutionError(
          this.definition.name,
          `Path is not a directory: ${dirPath}`
        );
      }

      // Read directory contents
      const dirEntries = await fs.readdir(dirPath, { withFileTypes: true });

      for (const dirEntry of dirEntries) {
        const entryPath = path.join(dirPath, dirEntry.name);
        
        // Skip hidden files unless requested
        if (!params.all && dirEntry.name.startsWith('.')) {
          continue;
        }

        // Apply filter if specified
        if (params.filter && !this.matchesFilter(dirEntry.name, params.filter)) {
          continue;
        }

        try {
          const fileEntry = await this.createFileEntry(entryPath, dirEntry, params);
          entries.push(fileEntry);
          
          if (fileEntry.size) {
            totalSize += fileEntry.size;
          }

          // Recurse into subdirectories if requested
          if (params.recursive && 
              fileEntry.type === 'directory' && 
              currentDepth < params.maxDepth) {
            const subResult = await this.listDirectory(entryPath, params, currentDepth + 1);
            entries.push(...subResult.entries);
            totalSize += subResult.totalSize;
          }
        } catch (error) {
          // Log but don't fail on individual file errors
          logger.debug('Failed to process entry', {
            entryPath,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      // Sort entries
      this.sortEntries(entries, params.sortBy, params.reverse);

    } catch (error) {
      throw new ToolExecutionError(
        this.definition.name,
        `Failed to list directory: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    return {
      entries,
      totalEntries: entries.length,
      totalSize,
      totalSizeHuman: this.formatSize(totalSize),
      path: dirPath,
      isRecursive: params.recursive
    };
  }

  private async createFileEntry(
    entryPath: string,
    dirEntry: any,
    params: any
  ): Promise<FileEntry> {
    const stat = await fs.lstat(entryPath);
    const realStat = dirEntry.isSymbolicLink() ? await fs.stat(entryPath).catch(() => stat) : stat;

    let type: 'file' | 'directory' | 'symlink' | 'other' = 'other';
    if (dirEntry.isSymbolicLink()) {
      type = 'symlink';
    } else if (realStat.isDirectory()) {
      type = 'directory';
    } else if (realStat.isFile()) {
      type = 'file';
    }

    const entry: FileEntry = {
      name: dirEntry.name,
      path: entryPath,
      type,
      modified: stat.mtime,
      permissions: this.formatPermissions(stat.mode),
      isHidden: dirEntry.name.startsWith('.')
    };

    // Add size information for files
    if (type === 'file' && params.showSize) {
      entry.size = realStat.size;
      if (params.humanReadable) {
        entry.sizeHuman = this.formatSize(realStat.size);
      }
    }

    // Add symlink target
    if (type === 'symlink') {
      try {
        entry.target = await fs.readlink(entryPath);
      } catch {
        entry.target = '<broken>';
      }
    }

    // Add owner/group information if available (Unix-like systems)
    if (process.platform !== 'win32') {
      try {
        entry.owner = stat.uid.toString();
        entry.group = stat.gid.toString();
      } catch {
        // Ignore if not available
      }
    }

    return entry;
  }

  private sortEntries(entries: FileEntry[], sortBy: string, reverse: boolean): void {
    entries.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'size':
          comparison = (a.size || 0) - (b.size || 0);
          break;
        case 'modified':
          comparison = a.modified.getTime() - b.modified.getTime();
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }

      return reverse ? -comparison : comparison;
    });
  }

  private formatOutput(result: LsResult, params: any): string {
    const lines: string[] = [];

    if (params.long) {
      // Long format output
      lines.push(`total ${result.totalEntries} entries, ${result.totalSizeHuman}`);
      lines.push('');

      for (const entry of result.entries) {
        const parts = [
          this.formatTypeIndicator(entry.type),
          entry.permissions,
          entry.owner || '',
          entry.group || '',
          entry.sizeHuman || entry.size?.toString() || '',
          this.formatDate(entry.modified),
          entry.name
        ];

        if (entry.type === 'symlink' && entry.target) {
          parts[parts.length - 1] += ` -> ${entry.target}`;
        }

        lines.push(parts.filter(p => p !== '').join('\t'));
      }
    } else {
      // Simple format output
      const names = result.entries.map(entry => {
        let name = entry.name;
        
        // Add type indicators
        if (entry.type === 'directory') {
          name += '/';
        } else if (entry.type === 'symlink') {
          name += '@';
        }

        return name;
      });

      // Format in columns if not recursive
      if (!result.isRecursive && names.length > 0) {
        const terminalWidth = 80; // Assume 80 character width
        const maxNameLength = Math.max(...names.map(n => n.length));
        const columns = Math.floor(terminalWidth / (maxNameLength + 2));
        
        if (columns > 1) {
          for (let i = 0; i < names.length; i += columns) {
            const row = names.slice(i, i + columns);
            lines.push(row.map(name => name.padEnd(maxNameLength + 2)).join('').trimEnd());
          }
        } else {
          lines.push(...names);
        }
      } else {
        lines.push(...names);
      }
    }

    return lines.join('\n');
  }

  private formatPermissions(mode: number): string {
    const permissions = [];
    
    // File type
    if ((mode & 0o170000) === 0o040000) permissions.push('d'); // directory
    else if ((mode & 0o170000) === 0o120000) permissions.push('l'); // symlink
    else permissions.push('-'); // regular file
    
    // Owner permissions
    permissions.push((mode & 0o400) ? 'r' : '-');
    permissions.push((mode & 0o200) ? 'w' : '-');
    permissions.push((mode & 0o100) ? 'x' : '-');
    
    // Group permissions
    permissions.push((mode & 0o040) ? 'r' : '-');
    permissions.push((mode & 0o020) ? 'w' : '-');
    permissions.push((mode & 0o010) ? 'x' : '-');
    
    // Other permissions
    permissions.push((mode & 0o004) ? 'r' : '-');
    permissions.push((mode & 0o002) ? 'w' : '-');
    permissions.push((mode & 0o001) ? 'x' : '-');
    
    return permissions.join('');
  }

  private formatTypeIndicator(type: string): string {
    switch (type) {
      case 'directory': return 'd';
      case 'symlink': return 'l';
      case 'file': return '-';
      default: return '?';
    }
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'K', 'M', 'G', 'T'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(unitIndex === 0 ? 0 : 1)}${units[unitIndex]}`;
  }

  private formatDate(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffDays < 180) {
      // Recent files: show month, day, time
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      // Older files: show month, day, year
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: '2-digit',
        year: 'numeric'
      });
    }
  }

  private matchesFilter(name: string, filter: string): boolean {
    // Simple glob pattern matching
    const regexPattern = filter
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');
    
    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(name);
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Limit path length for logging
    if (sanitized.path && sanitized.path.length > 100) {
      sanitized.path = sanitized.path.substring(0, 100) + '...';
    }
    
    return sanitized;
  }
}
