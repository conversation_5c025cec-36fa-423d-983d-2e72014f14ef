// Size-constrained container component
import React, { ReactNode } from 'react';
import { Box, measureElement } from 'ink';

interface MaxSizedBoxProps {
  children: ReactNode;
  maxWidth?: number;
  maxHeight?: number;
  minWidth?: number;
  minHeight?: number;
  overflow?: 'hidden' | 'scroll' | 'wrap';
  padding?: number;
  margin?: number;
  borderStyle?: 'single' | 'double' | 'round' | 'bold' | 'singleDouble' | 'doubleSingle' | 'classic';
  borderColor?: string;
  backgroundColor?: string;
}

export const MaxSizedBox: React.FC<MaxSizedBoxProps> = ({
  children,
  maxWidth,
  maxHeight,
  minWidth,
  minHeight,
  overflow = 'hidden',
  padding,
  margin,
  borderStyle,
  borderColor,
  backgroundColor
}) => {
  const getBoxProps = () => {
    const props: any = {};

    // Size constraints
    if (maxWidth !== undefined) props.width = maxWidth;
    if (maxHeight !== undefined) props.height = maxHeight;
    if (minWidth !== undefined) props.minWidth = minWidth;
    if (minHeight !== undefined) props.minHeight = minHeight;

    // Spacing
    if (padding !== undefined) {
      props.paddingX = padding;
      props.paddingY = padding;
    }
    if (margin !== undefined) {
      props.marginX = margin;
      props.marginY = margin;
    }

    // Styling
    if (borderStyle) props.borderStyle = borderStyle;
    if (borderColor) props.borderColor = borderColor;
    if (backgroundColor) props.backgroundColor = backgroundColor;

    // Overflow handling
    if (overflow === 'wrap') {
      props.flexWrap = 'wrap';
    } else if (overflow === 'scroll') {
      props.overflowY = 'scroll';
    }

    return props;
  };

  return (
    <Box {...getBoxProps()}>
      {children}
    </Box>
  );
};
