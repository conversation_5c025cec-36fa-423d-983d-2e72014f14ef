# Arien CLI Demo

This document demonstrates the key features of Arien CLI.

## 🚀 Quick Start

### 1. Installation

```bash
# Clone or download the project
cd arien-cli

# Run setup (installs dependencies and builds)
npm run setup

# Or manually:
npm run install:all
npm run build
```

### 2. First Time Configuration

```bash
# Configure your AI provider
node cli/dist/index.js auth

# Check status
node cli/dist/index.js status
```

### 3. Basic Usage

```bash
# Simple conversation
node cli/dist/index.js "What is TypeScript?"

# Interactive mode (default)
node cli/dist/index.js

# Non-interactive mode
echo "Explain React hooks" | node cli/dist/index.js --non-interactive
```

## 🎯 Key Features Demo

### Multi-Provider Support

```bash
# Use DeepSeek (fast and cost-effective)
node cli/dist/index.js --provider deepseek "Write a simple function"

# Use OpenAI GPT-4 (powerful reasoning)
node cli/dist/index.js --provider openai --model gpt-4 "Complex algorithm design"

# Use Anthropic Claude (excellent for analysis)
node cli/dist/index.js --provider anthropic "Analyze this code structure"

# Use Google Gemini (multimodal capabilities)
node cli/dist/index.js --provider google "Explain this concept"
```

### Streaming Responses

```bash
# Real-time streaming
node cli/dist/index.js --stream "Write a detailed explanation of machine learning"

# Non-interactive streaming
echo "Explain quantum computing" | node cli/dist/index.js --non-interactive --stream
```

### Configuration Management

```bash
# View all settings
node cli/dist/index.js config show

# Set default provider
node cli/dist/index.js config set defaultProvider deepseek

# Set default model
node cli/dist/index.js config set defaultModel deepseek-chat

# View available models
node cli/dist/index.js models

# Models for specific provider
node cli/dist/index.js models --provider openai
```

### Security Features

```bash
# Default security (prompts for dangerous operations)
node cli/dist/index.js "Delete all temporary files"

# Auto-approve file edits only
node cli/dist/index.js config set security.approvalLevel auto-edit

# View security settings
node cli/dist/index.js config get security
```

### Theme System

```bash
# Interactive theme selection
node cli/dist/index.js theme

# Set theme directly
node cli/dist/index.js config set theme dracula

# Available themes: default, dracula, github-dark
```

## 🛠️ Advanced Usage

### File Operations

```bash
# Read and analyze files (when file tools are implemented)
node cli/dist/index.js "Review this code" @src/main.ts

# Multiple files
node cli/dist/index.js "Compare these files" @file1.js @file2.js

# Directory analysis
node cli/dist/index.js "Summarize this project structure" @src/
```

### Shell Integration

```bash
# Pipe git diff
git diff | node cli/dist/index.js --non-interactive "Review these changes"

# Analyze logs
tail -n 50 app.log | node cli/dist/index.js --non-interactive "Find errors"

# Generate commit messages
git diff --cached | node cli/dist/index.js --non-interactive "Generate commit message"
```

### Custom Parameters

```bash
# Control creativity
node cli/dist/index.js --temperature 0.1 "Generate precise code"
node cli/dist/index.js --temperature 0.9 "Write creative content"

# Limit response length
node cli/dist/index.js --max-tokens 100 "Brief explanation"

# Combine options
node cli/dist/index.js --provider openai --model gpt-4 --temperature 0.2 --max-tokens 500 "Detailed analysis"
```

## 🎨 UI Features

### Interactive Mode

When you run `node cli/dist/index.js` without arguments:

- **Header**: Shows current provider and model
- **Chat Display**: Conversation history with timestamps
- **Input Prompt**: Type your messages
- **Footer**: Status and message count
- **Themes**: Multiple visual themes available

### Keyboard Shortcuts

- `Enter`: Send message
- `Ctrl+C`: Exit application
- `Tab`: Auto-completion (when implemented)
- `↑/↓`: Command history (when implemented)

## 🔧 Development

### Project Structure

```
arien-cli/
├── cli/                 # React/Ink terminal interface
│   ├── src/ui/         # UI components
│   ├── src/themes/     # Visual themes
│   └── src/utils/      # CLI utilities
├── core/               # Business logic
│   ├── src/core/       # AI integration
│   ├── src/tools/      # Tool system
│   ├── src/config/     # Configuration
│   └── src/utils/      # Core utilities
└── examples/           # Usage examples
```

### Building

```bash
# Build everything
npm run build

# Build specific package
npm run build:core
npm run build:cli

# Development mode
npm run dev
```

### Testing

```bash
# Run all tests
npm test

# Test specific package
npm run test:core
npm run test:cli

# Watch mode
cd core && npm run test:watch
```

## 🌟 Example Workflows

### Code Review

```bash
# Review a file
node cli/dist/index.js "Review this code for best practices" @src/api.ts

# Review git changes
git diff HEAD~1 | node cli/dist/index.js --non-interactive "Review changes"
```

### Documentation

```bash
# Generate README
node cli/dist/index.js "Create a README for this project" @package.json

# API documentation
node cli/dist/index.js "Generate API docs" @src/api/
```

### Learning

```bash
# Explain concepts
node cli/dist/index.js "Explain async/await with examples"

# Code explanation
node cli/dist/index.js "How does this algorithm work?" @src/sort.js
```

### Debugging

```bash
# Analyze errors
node cli/dist/index.js "Debug this error" @logs/error.log

# Performance analysis
node cli/dist/index.js "Optimize this function" @src/slow-function.js
```

## 🚨 Troubleshooting

### Common Issues

```bash
# Check configuration
node cli/dist/index.js status

# Reset configuration
node cli/dist/index.js config reset

# Enable debug logging
node cli/dist/index.js --debug "test message"

# Check logs
tail -f ~/.arien/logs/arien.log
```

### Performance Tips

- Use `deepseek-chat` for general questions (fast, cheap)
- Use `gpt-4` for complex reasoning (slower, more expensive)
- Use `--max-tokens` to limit response length
- Enable streaming for long responses

## 🎯 Next Steps

1. **Configure Multiple Providers**: Set up all supported AI providers
2. **Customize Themes**: Try different visual themes
3. **Explore Tools**: Use file operations and shell commands (when implemented)
4. **Integration**: Add to your development workflow
5. **Contribute**: Help improve the project

## 📚 Resources

- [Basic Usage Examples](examples/basic-usage.md)
- [Contributing Guide](CONTRIBUTING.md)
- [Configuration Reference](README.md#configuration)
- [API Documentation](core/README.md)

Happy coding with Arien CLI! 🚀
