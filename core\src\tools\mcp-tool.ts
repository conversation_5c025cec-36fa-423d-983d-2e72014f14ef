// MCP tool integration for Arien
import { z } from 'zod';
import { MC<PERSON>lient, MCPServer } from './mcp-client.js';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';
import { getConfig } from '../config/config.js';

const MCPToolCallSchema = z.object({
  server: z.string().optional(),
  tool: z.string(),
  arguments: z.record(z.any()).optional()
});

const MCPResourceReadSchema = z.object({
  server: z.string().optional(),
  uri: z.string()
});

const MCPServerConfigSchema = z.object({
  name: z.string(),
  command: z.string(),
  args: z.array(z.string()).optional(),
  env: z.record(z.string()).optional(),
  cwd: z.string().optional(),
  timeout: z.number().optional()
});

export class MCPToolIntegration {
  private client: MCPClient;
  private initialized = false;

  constructor() {
    this.client = new MCPClient({
      timeout: 30000,
      retries: 3,
      debug: process.env.DEBUG === 'true'
    });
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      const config = getConfig();
      const mcpConfig = config.getConfig().mcp;

      if (mcpConfig?.servers && Array.isArray(mcpConfig.servers)) {
        logger.info('Initializing MCP servers', { 
          serverCount: mcpConfig.servers.length 
        });

        for (const serverConfig of mcpConfig.servers) {
          try {
            const validatedConfig = MCPServerConfigSchema.parse(serverConfig);
            await this.client.connectToServer(validatedConfig);
          } catch (error) {
            logger.warn('Failed to connect to MCP server', { 
              serverConfig, 
              error 
            });
          }
        }
      }

      this.initialized = true;
      logger.info('MCP tool integration initialized');
    } catch (error) {
      logger.error('Failed to initialize MCP tool integration', { error });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        'Failed to initialize MCP integration',
        { error }
      );
    }
  }

  public async callTool(input: unknown): Promise<any> {
    await this.initialize();

    const parsed = MCPToolCallSchema.parse(input);
    const { server, tool, arguments: args = {} } = parsed;

    logger.info('Calling MCP tool', { server, tool, args });

    try {
      const result = await this.client.callTool(tool, args, server);
      
      logger.debug('MCP tool call completed', { 
        server, 
        tool, 
        resultType: typeof result 
      });

      return {
        success: true,
        result,
        tool,
        server: server || 'auto-detected'
      };
    } catch (error) {
      logger.error('MCP tool call failed', { server, tool, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `MCP tool call failed: ${tool}`,
        { server, tool, args, error }
      );
    }
  }

  public async listTools(serverName?: string): Promise<any> {
    await this.initialize();

    try {
      const tools = await this.client.listTools(serverName);
      
      return {
        success: true,
        tools: tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          schema: tool.inputSchema
        })),
        serverName: serverName || 'all'
      };
    } catch (error) {
      logger.error('Failed to list MCP tools', { serverName, error });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        'Failed to list MCP tools',
        { serverName, error }
      );
    }
  }

  public async readResource(input: unknown): Promise<any> {
    await this.initialize();

    const parsed = MCPResourceReadSchema.parse(input);
    const { server, uri } = parsed;

    logger.info('Reading MCP resource', { server, uri });

    try {
      const content = await this.client.readResource(uri, server);
      
      return {
        success: true,
        uri,
        content,
        server: server || 'auto-detected'
      };
    } catch (error) {
      logger.error('Failed to read MCP resource', { server, uri, error });
      throw new ArienError(
        ErrorCode.TOOL_EXECUTION_FAILED,
        `Failed to read MCP resource: ${uri}`,
        { server, uri, error }
      );
    }
  }

  public async listResources(serverName?: string): Promise<any> {
    await this.initialize();

    try {
      const resources = await this.client.listResources(serverName);
      
      return {
        success: true,
        resources: resources.map(resource => ({
          uri: resource.uri,
          name: resource.name,
          description: resource.description,
          mimeType: resource.mimeType
        })),
        serverName: serverName || 'all'
      };
    } catch (error) {
      logger.error('Failed to list MCP resources', { serverName, error });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        'Failed to list MCP resources',
        { serverName, error }
      );
    }
  }

  public async getServerStatus(): Promise<any> {
    await this.initialize();

    const connectedServers = this.client.getConnectedServers();
    const serverStatus = await Promise.all(
      connectedServers.map(async (serverName) => {
        try {
          const tools = await this.client.listTools(serverName);
          const resources = await this.client.listResources(serverName);
          
          return {
            name: serverName,
            connected: true,
            toolCount: tools.length,
            resourceCount: resources.length
          };
        } catch (error) {
          return {
            name: serverName,
            connected: false,
            error: error.message
          };
        }
      })
    );

    return {
      success: true,
      servers: serverStatus,
      totalServers: connectedServers.length
    };
  }

  public async addServer(serverConfig: MCPServer): Promise<any> {
    await this.initialize();

    try {
      const validatedConfig = MCPServerConfigSchema.parse(serverConfig);
      await this.client.connectToServer(validatedConfig);
      
      logger.info('MCP server added successfully', { name: serverConfig.name });
      
      return {
        success: true,
        message: `Successfully connected to MCP server: ${serverConfig.name}`,
        serverName: serverConfig.name
      };
    } catch (error) {
      logger.error('Failed to add MCP server', { serverConfig, error });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        `Failed to add MCP server: ${serverConfig.name}`,
        { serverConfig, error }
      );
    }
  }

  public async removeServer(serverName: string): Promise<any> {
    await this.initialize();

    try {
      await this.client.disconnectFromServer(serverName);
      
      logger.info('MCP server removed successfully', { name: serverName });
      
      return {
        success: true,
        message: `Successfully disconnected from MCP server: ${serverName}`,
        serverName
      };
    } catch (error) {
      logger.error('Failed to remove MCP server', { serverName, error });
      throw new ArienError(
        ErrorCode.TOOL_ERROR,
        `Failed to remove MCP server: ${serverName}`,
        { serverName, error }
      );
    }
  }

  public async shutdown(): Promise<void> {
    if (this.initialized) {
      await this.client.disconnectAll();
      this.initialized = false;
      logger.info('MCP tool integration shut down');
    }
  }

  public getToolDefinitions() {
    return [
      {
        name: 'mcp_call_tool',
        description: 'Call a tool from a connected MCP server',
        inputSchema: {
          type: 'object',
          properties: {
            server: {
              type: 'string',
              description: 'Name of the MCP server (optional, will auto-detect if not provided)'
            },
            tool: {
              type: 'string',
              description: 'Name of the tool to call'
            },
            arguments: {
              type: 'object',
              description: 'Arguments to pass to the tool'
            }
          },
          required: ['tool']
        }
      },
      {
        name: 'mcp_list_tools',
        description: 'List available tools from MCP servers',
        inputSchema: {
          type: 'object',
          properties: {
            server: {
              type: 'string',
              description: 'Name of specific MCP server (optional, lists from all if not provided)'
            }
          }
        }
      },
      {
        name: 'mcp_read_resource',
        description: 'Read a resource from an MCP server',
        inputSchema: {
          type: 'object',
          properties: {
            server: {
              type: 'string',
              description: 'Name of the MCP server (optional, will auto-detect if not provided)'
            },
            uri: {
              type: 'string',
              description: 'URI of the resource to read'
            }
          },
          required: ['uri']
        }
      },
      {
        name: 'mcp_list_resources',
        description: 'List available resources from MCP servers',
        inputSchema: {
          type: 'object',
          properties: {
            server: {
              type: 'string',
              description: 'Name of specific MCP server (optional, lists from all if not provided)'
            }
          }
        }
      },
      {
        name: 'mcp_server_status',
        description: 'Get status of connected MCP servers',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      }
    ];
  }
}

// Export singleton instance
export const mcpTool = new MCPToolIntegration();
