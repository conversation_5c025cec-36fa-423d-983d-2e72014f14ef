// Tool approval dialog component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../hooks/useTheme.js';
import { ApprovalRequest } from '@arien/core';

export interface ToolApprovalDialogProps {
  request: ApprovalRequest;
  onApprove: () => void;
  onDeny: () => void;
  autoApproveTimeout?: number;
}

export const ToolApprovalDialog: React.FC<ToolApprovalDialogProps> = ({
  request,
  onApprove,
  onDeny,
  autoApproveTimeout
}) => {
  const theme = useTheme();
  const [timeLeft, setTimeLeft] = useState(autoApproveTimeout || 0);
  const [selectedOption, setSelectedOption] = useState<'approve' | 'deny'>('deny');

  // Handle keyboard input
  useInput((input, key) => {
    if (key.leftArrow || key.rightArrow) {
      setSelectedOption(prev => prev === 'approve' ? 'deny' : 'approve');
      return;
    }

    if (key.return) {
      if (selectedOption === 'approve') {
        onApprove();
      } else {
        onDeny();
      }
      return;
    }

    if (input.toLowerCase() === 'y') {
      onApprove();
      return;
    }

    if (input.toLowerCase() === 'n') {
      onDeny();
      return;
    }

    if (key.escape) {
      onDeny();
      return;
    }
  });

  // Auto-approve countdown
  React.useEffect(() => {
    if (autoApproveTimeout && timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (autoApproveTimeout && timeLeft === 0) {
      onApprove();
    }
  }, [timeLeft, autoApproveTimeout, onApprove]);

  const getRiskColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.success;
      default: return theme.colors.muted;
    }
  };

  const getRiskIcon = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'high': return '⚠️';
      case 'medium': return '⚡';
      case 'low': return '✅';
      default: return '❓';
    }
  };

  return (
    <Box
      position="absolute"
      top={2}
      left={2}
      right={2}
      borderStyle="double"
      borderColor={getRiskColor(request.riskLevel)}
      paddingX={2}
      paddingY={1}
      flexDirection="column"
      backgroundColor="black"
    >
      {/* Header */}
      <Box justifyContent="center" marginBottom={1}>
        <Text color={theme.colors.warning} bold>
          🔐 TOOL APPROVAL REQUIRED
        </Text>
      </Box>

      {/* Request details */}
      <Box flexDirection="column" marginBottom={2}>
        <Box>
          <Text color={theme.colors.text} bold>
            Operation: 
          </Text>
          <Text color={theme.colors.primary} marginLeft={1}>
            {request.operation}
          </Text>
        </Box>

        <Box marginTop={1}>
          <Text color={theme.colors.text} bold>
            Resource: 
          </Text>
          <Text color={theme.colors.text} marginLeft={1}>
            {request.resource}
          </Text>
        </Box>

        <Box marginTop={1}>
          <Text color={theme.colors.text} bold>
            Risk Level: 
          </Text>
          <Text color={getRiskColor(request.riskLevel)} marginLeft={1}>
            {getRiskIcon(request.riskLevel)} {request.riskLevel.toUpperCase()}
          </Text>
        </Box>

        <Box marginTop={1}>
          <Text color={theme.colors.text} bold>
            Description: 
          </Text>
        </Box>
        <Box marginTop={1} paddingLeft={2}>
          <Text color={theme.colors.muted}>
            {request.description}
          </Text>
        </Box>
      </Box>

      {/* Context details */}
      {request.context && Object.keys(request.context).length > 0 && (
        <Box flexDirection="column" marginBottom={2}>
          <Text color={theme.colors.text} bold>
            Additional Context:
          </Text>
          {Object.entries(request.context).map(([key, value]) => (
            <Box key={key} marginTop={1} paddingLeft={2}>
              <Text color={theme.colors.muted}>
                {key}: {String(value)}
              </Text>
            </Box>
          ))}
        </Box>
      )}

      {/* Auto-approve countdown */}
      {autoApproveTimeout && timeLeft > 0 && (
        <Box justifyContent="center" marginBottom={1}>
          <Text color={theme.colors.warning}>
            Auto-approving in {timeLeft} seconds...
          </Text>
        </Box>
      )}

      {/* Action buttons */}
      <Box justifyContent="center" marginBottom={1}>
        <Box
          borderStyle="round"
          borderColor={selectedOption === 'approve' ? theme.colors.success : theme.colors.muted}
          paddingX={2}
          paddingY={0}
          marginRight={2}
        >
          <Text 
            color={selectedOption === 'approve' ? theme.colors.success : theme.colors.muted}
            bold={selectedOption === 'approve'}
          >
            ✓ Approve (Y)
          </Text>
        </Box>

        <Box
          borderStyle="round"
          borderColor={selectedOption === 'deny' ? theme.colors.error : theme.colors.muted}
          paddingX={2}
          paddingY={0}
        >
          <Text 
            color={selectedOption === 'deny' ? theme.colors.error : theme.colors.muted}
            bold={selectedOption === 'deny'}
          >
            ✗ Deny (N)
          </Text>
        </Box>
      </Box>

      {/* Instructions */}
      <Box justifyContent="center">
        <Text color={theme.colors.muted} dimColor>
          Use ← → arrows to select, Enter to confirm, Escape to deny
        </Text>
      </Box>

      {/* Risk warning */}
      {request.riskLevel === 'high' && (
        <Box 
          justifyContent="center" 
          marginTop={1}
          borderStyle="single"
          borderColor={theme.colors.error}
          paddingX={1}
        >
          <Text color={theme.colors.error} bold>
            ⚠️  HIGH RISK OPERATION - PROCEED WITH CAUTION  ⚠️
          </Text>
        </Box>
      )}
    </Box>
  );
};
