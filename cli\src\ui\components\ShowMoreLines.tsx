// Expandable content display component
import React, { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

interface ShowMoreLinesProps {
  content: string[];
  initialLines?: number;
  expandIncrement?: number;
  showLineNumbers?: boolean;
  highlightPattern?: RegExp;
  title?: string;
  allowCollapse?: boolean;
}

export const ShowMoreLines: React.FC<ShowMoreLinesProps> = ({
  content,
  initialLines = 10,
  expandIncrement = 10,
  showLineNumbers = false,
  highlightPattern,
  title,
  allowCollapse = true
}) => {
  const theme = useTheme();
  const [visibleLines, setVisibleLines] = useState(initialLines);
  const [isExpanded, setIsExpanded] = useState(false);

  useInput((input, key) => {
    if (key.return || input === ' ') {
      if (isExpanded && allowCollapse) {
        setVisibleLines(initialLines);
        setIsExpanded(false);
      } else if (visibleLines < content.length) {
        const newLines = Math.min(content.length, visibleLines + expandIncrement);
        setVisibleLines(newLines);
        if (newLines >= content.length) {
          setIsExpanded(true);
        }
      }
    } else if (key.escape && isExpanded && allowCollapse) {
      setVisibleLines(initialLines);
      setIsExpanded(false);
    }
  });

  const highlightText = (text: string) => {
    if (!highlightPattern) return text;
    
    const parts = text.split(highlightPattern);
    const matches = text.match(highlightPattern) || [];
    
    const result: React.ReactNode[] = [];
    for (let i = 0; i < parts.length; i++) {
      result.push(parts[i]);
      if (i < matches.length) {
        result.push(
          <Text key={`match-${i}`} backgroundColor={theme.colors.warning} color={theme.colors.background}>
            {matches[i]}
          </Text>
        );
      }
    }
    return result;
  };

  const formatLineNumber = (lineNum: number) => {
    const maxDigits = content.length.toString().length;
    return lineNum.toString().padStart(maxDigits, ' ');
  };

  const displayedContent = content.slice(0, visibleLines);
  const hasMore = visibleLines < content.length;
  const remainingLines = content.length - visibleLines;

  return (
    <Box flexDirection="column">
      {/* Title */}
      {title && (
        <Box marginBottom={1}>
          <Text color={theme.colors.primary}>{title}</Text>
          <Text color={theme.colors.muted}> ({content.length} lines)</Text>
        </Box>
      )}

      {/* Content */}
      <Box flexDirection="column">
        {displayedContent.map((line, index) => (
          <Box key={index}>
            {showLineNumbers && (
              <Text color={theme.colors.muted}>
                {formatLineNumber(index + 1)}:{' '}
              </Text>
            )}
            <Text color={theme.colors.text}>
              {highlightText(line)}
            </Text>
          </Box>
        ))}
      </Box>

      {/* Show more/less controls */}
      {(hasMore || (isExpanded && allowCollapse)) && (
        <Box marginTop={1} flexDirection="column">
          {hasMore && (
            <Box>
              <Text color={theme.colors.secondary}>
                ▼ Show {Math.min(expandIncrement, remainingLines)} more lines 
              </Text>
              <Text color={theme.colors.muted}>
                ({remainingLines} remaining)
              </Text>
            </Box>
          )}
          
          {isExpanded && allowCollapse && (
            <Box>
              <Text color={theme.colors.secondary}>
                ▲ Collapse to {initialLines} lines
              </Text>
            </Box>
          )}
          
          <Box marginTop={0}>
            <Text color={theme.colors.muted}>
              Press Enter/Space to {hasMore ? 'expand' : 'collapse'}
              {isExpanded && allowCollapse && ', Escape to collapse'}
            </Text>
          </Box>
        </Box>
      )}

      {/* Summary when fully expanded */}
      {isExpanded && (
        <Box marginTop={1}>
          <Text color={theme.colors.success}>
            ✅ Showing all {content.length} lines
          </Text>
        </Box>
      )}
    </Box>
  );
};
