// Application footer with status information
import React from 'react';
import { Box, Text } from 'ink';

interface FooterProps {
  isProcessing: boolean;
  messageCount: number;
}

export const Footer: React.FC<FooterProps> = ({ isProcessing, messageCount }) => {
  const getStatusText = () => {
    if (isProcessing) {
      return '⏳ Processing...';
    }
    return '✅ Ready';
  };

  const getStatusColor = () => {
    if (isProcessing) {
      return 'yellow';
    }
    return 'green';
  };

  return (
    <Box borderStyle="round" borderColor="gray" padding={1} marginTop={1}>
      <Box justifyContent="space-between" width="100%">
        <Box>
          <Text color={getStatusColor()}>
            {getStatusText()}
          </Text>
        </Box>
        
        <Box>
          <Text dimColor>
            Messages: {messageCount}
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
