# Arien CLI Installation Script for Windows
param(
    [switch]$Global
)

Write-Host "🤖 Installing Arien CLI..." -ForegroundColor Blue

# Check if Node.js is installed
try {
    $nodeVersion = node -v
    Write-Host "✅ Node.js $nodeVersion detected" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 18+ first." -ForegroundColor Red
    Write-Host "Visit: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check Node.js version
$nodeVersionNumber = $nodeVersion -replace 'v', ''
$requiredVersion = [version]"18.0.0"
$currentVersion = [version]$nodeVersionNumber

if ($currentVersion -lt $requiredVersion) {
    Write-Host "❌ Node.js version $nodeVersionNumber is not supported. Please install Node.js 18+ first." -ForegroundColor Red
    exit 1
}

# Check if npm is installed
try {
    npm -v | Out-Null
    Write-Host "✅ npm detected" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed. Please install npm first." -ForegroundColor Red
    exit 1
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Blue
try {
    npm run install:all
    Write-Host "✅ Dependencies installed" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

# Build the project
Write-Host "🔨 Building project..." -ForegroundColor Blue
try {
    npm run build
    Write-Host "✅ Project built successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to build project" -ForegroundColor Red
    exit 1
}

# Create global symlink if requested
if ($Global) {
    Write-Host "🔗 Creating global symlink..." -ForegroundColor Blue
    try {
        npm link cli
        Write-Host "✅ Global symlink created. You can now use 'arien' command anywhere." -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Failed to create global symlink. You may need to run as Administrator." -ForegroundColor Yellow
        Write-Host "ℹ️  You can run Arien CLI with: npm start" -ForegroundColor Cyan
    }
} else {
    $response = Read-Host "🔗 Create global symlink for 'arien' command? (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        try {
            npm link cli
            Write-Host "✅ Global symlink created. You can now use 'arien' command anywhere." -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Failed to create global symlink. You may need to run as Administrator." -ForegroundColor Yellow
            Write-Host "ℹ️  You can run Arien CLI with: npm start" -ForegroundColor Cyan
        }
    } else {
        Write-Host "ℹ️  You can run Arien CLI with: npm start" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "🎉 Arien CLI installation completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure your AI provider: arien auth" -ForegroundColor White
Write-Host "2. Start using Arien: arien `"Hello, how can you help me?`"" -ForegroundColor White
Write-Host ""
Write-Host "For more information, run: arien --help" -ForegroundColor Cyan
