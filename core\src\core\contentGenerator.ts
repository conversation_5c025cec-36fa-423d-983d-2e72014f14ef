// Content generation utilities
import { ChatMessage } from './client.js';
import { logger } from './logger.js';
import { TokenLimitManager, CompressionOptions } from './tokenLimits.js';

export interface ContentGenerationOptions {
  maxLength?: number;
  style?: 'concise' | 'detailed' | 'technical' | 'casual';
  format?: 'markdown' | 'plain' | 'code' | 'json';
  includeExamples?: boolean;
  targetAudience?: 'beginner' | 'intermediate' | 'expert';
}

export interface SummaryOptions {
  maxLength: number;
  preserveCodeBlocks?: boolean;
  includeKeyPoints?: boolean;
  format?: 'bullet' | 'paragraph' | 'outline';
}

export class ContentGenerator {
  private tokenManager: TokenLimitManager;

  constructor() {
    this.tokenManager = new TokenLimitManager();
  }

  public async compressMessages(
    messages: ChatMessage[],
    targetTokens?: number
  ): Promise<ChatMessage[]> {
    const currentTokens = await this.tokenManager.countTokens(messages);
    
    if (!targetTokens) {
      targetTokens = Math.floor(currentTokens * 0.7); // Compress to 70% of original
    }

    const compressionOptions: CompressionOptions = {
      strategy: 'summarize',
      preserveRecent: Math.min(10, Math.floor(messages.length * 0.3)),
      maxTokens: targetTokens
    };

    logger.info('Compressing message history', {
      originalTokens: currentTokens,
      targetTokens,
      originalMessageCount: messages.length
    });

    const compressedMessages = await this.tokenManager.compressMessages(messages, compressionOptions);
    const finalTokens = await this.tokenManager.countTokens(compressedMessages);

    logger.info('Message compression completed', {
      originalTokens: currentTokens,
      finalTokens,
      compressionRatio: Math.round((1 - finalTokens / currentTokens) * 100),
      originalMessageCount: messages.length,
      finalMessageCount: compressedMessages.length
    });

    return compressedMessages;
  }

  public generateSystemPrompt(
    context: {
      userRole?: string;
      projectType?: string;
      preferences?: Record<string, any>;
      tools?: string[];
    }
  ): string {
    const { userRole = 'developer', projectType, preferences = {}, tools = [] } = context;

    let prompt = `You are an AI assistant helping a ${userRole}`;
    
    if (projectType) {
      prompt += ` working on a ${projectType} project`;
    }

    prompt += '. You should be helpful, accurate, and provide practical solutions.';

    // Add tool information
    if (tools.length > 0) {
      prompt += `\n\nYou have access to the following tools: ${tools.join(', ')}.`;
      prompt += ' Use them when appropriate to help the user.';
    }

    // Add preferences
    if (preferences.codeStyle) {
      prompt += `\n\nCode style preference: ${preferences.codeStyle}`;
    }

    if (preferences.verbosity) {
      prompt += `\n\nResponse style: ${preferences.verbosity}`;
    }

    if (preferences.language) {
      prompt += `\n\nPreferred programming language: ${preferences.language}`;
    }

    return prompt;
  }

  public async generateSummary(
    content: string,
    options: SummaryOptions
  ): Promise<string> {
    logger.debug('Generating content summary', {
      contentLength: content.length,
      maxLength: options.maxLength,
      format: options.format
    });

    // Extract key information
    const sentences = this.extractSentences(content);
    const codeBlocks = options.preserveCodeBlocks ? this.extractCodeBlocks(content) : [];
    const keyPoints = options.includeKeyPoints ? this.extractKeyPoints(content) : [];

    // Score sentences by importance
    const scoredSentences = this.scoreSentences(sentences);
    
    // Select sentences to include
    const selectedSentences = this.selectSentencesForSummary(
      scoredSentences,
      options.maxLength,
      codeBlocks.length * 50 // Reserve space for code blocks
    );

    // Format the summary
    let summary = this.formatSummary(selectedSentences, keyPoints, options.format || 'paragraph');

    // Add code blocks if requested
    if (options.preserveCodeBlocks && codeBlocks.length > 0) {
      summary += '\n\n' + codeBlocks.join('\n\n');
    }

    // Ensure we don't exceed max length
    if (summary.length > options.maxLength) {
      summary = summary.substring(0, options.maxLength - 3) + '...';
    }

    logger.debug('Summary generated', {
      originalLength: content.length,
      summaryLength: summary.length,
      compressionRatio: Math.round((1 - summary.length / content.length) * 100)
    });

    return summary;
  }

  public generateCodeDocumentation(
    code: string,
    language: string,
    options: ContentGenerationOptions = {}
  ): string {
    const style = options.style || 'technical';
    const includeExamples = options.includeExamples || false;

    let documentation = `# ${language} Code Documentation\n\n`;

    // Analyze code structure
    const functions = this.extractFunctions(code, language);
    const classes = this.extractClasses(code, language);
    const imports = this.extractImports(code, language);

    // Document imports
    if (imports.length > 0) {
      documentation += '## Dependencies\n\n';
      imports.forEach(imp => {
        documentation += `- ${imp}\n`;
      });
      documentation += '\n';
    }

    // Document classes
    if (classes.length > 0) {
      documentation += '## Classes\n\n';
      classes.forEach(cls => {
        documentation += `### ${cls.name}\n\n`;
        if (cls.description) {
          documentation += `${cls.description}\n\n`;
        }
        if (includeExamples && cls.example) {
          documentation += `**Example:**\n\`\`\`${language}\n${cls.example}\n\`\`\`\n\n`;
        }
      });
    }

    // Document functions
    if (functions.length > 0) {
      documentation += '## Functions\n\n';
      functions.forEach(func => {
        documentation += `### ${func.name}\n\n`;
        if (func.description) {
          documentation += `${func.description}\n\n`;
        }
        if (func.parameters && func.parameters.length > 0) {
          documentation += '**Parameters:**\n';
          func.parameters.forEach(param => {
            documentation += `- \`${param.name}\` (${param.type}): ${param.description}\n`;
          });
          documentation += '\n';
        }
        if (func.returns) {
          documentation += `**Returns:** ${func.returns}\n\n`;
        }
        if (includeExamples && func.example) {
          documentation += `**Example:**\n\`\`\`${language}\n${func.example}\n\`\`\`\n\n`;
        }
      });
    }

    return documentation;
  }

  private extractSentences(content: string): string[] {
    // Split content into sentences
    return content
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 10);
  }

  private extractCodeBlocks(content: string): string[] {
    const codeBlockRegex = /```[\s\S]*?```/g;
    return content.match(codeBlockRegex) || [];
  }

  private extractKeyPoints(content: string): string[] {
    const keyPoints: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmed = line.trim();
      
      // Look for bullet points, numbered lists, or emphasized text
      if (trimmed.match(/^[-*•]\s+/) || 
          trimmed.match(/^\d+\.\s+/) ||
          trimmed.match(/^\*\*.*\*\*/) ||
          trimmed.match(/^##?\s+/)) {
        keyPoints.push(trimmed);
      }
    }

    return keyPoints.slice(0, 5); // Limit to 5 key points
  }

  private scoreSentences(sentences: string[]): Array<{ sentence: string; score: number }> {
    return sentences.map(sentence => ({
      sentence,
      score: this.calculateSentenceScore(sentence)
    })).sort((a, b) => b.score - a.score);
  }

  private calculateSentenceScore(sentence: string): number {
    let score = 0;

    // Length score (prefer medium-length sentences)
    const length = sentence.length;
    if (length >= 50 && length <= 150) {
      score += 2;
    } else if (length >= 20 && length <= 200) {
      score += 1;
    }

    // Keyword score
    const importantKeywords = [
      'important', 'key', 'main', 'primary', 'essential', 'critical',
      'should', 'must', 'need', 'require', 'recommend',
      'function', 'method', 'class', 'variable', 'parameter',
      'error', 'issue', 'problem', 'solution', 'fix'
    ];

    const lowerSentence = sentence.toLowerCase();
    importantKeywords.forEach(keyword => {
      if (lowerSentence.includes(keyword)) {
        score += 1;
      }
    });

    // Position score (first and last sentences are often important)
    // This would need sentence index to implement properly

    return score;
  }

  private selectSentencesForSummary(
    scoredSentences: Array<{ sentence: string; score: number }>,
    maxLength: number,
    reservedSpace: number = 0
  ): string[] {
    const availableLength = maxLength - reservedSpace;
    const selected: string[] = [];
    let currentLength = 0;

    for (const { sentence } of scoredSentences) {
      if (currentLength + sentence.length + 2 <= availableLength) { // +2 for spacing
        selected.push(sentence);
        currentLength += sentence.length + 2;
      }
    }

    return selected;
  }

  private formatSummary(
    sentences: string[],
    keyPoints: string[],
    format: 'bullet' | 'paragraph' | 'outline'
  ): string {
    switch (format) {
      case 'bullet':
        let bulletSummary = '';
        if (keyPoints.length > 0) {
          bulletSummary += '**Key Points:**\n';
          keyPoints.forEach(point => {
            bulletSummary += `• ${point}\n`;
          });
          bulletSummary += '\n';
        }
        bulletSummary += '**Summary:**\n';
        sentences.forEach(sentence => {
          bulletSummary += `• ${sentence}\n`;
        });
        return bulletSummary;

      case 'outline':
        let outlineSummary = '';
        if (keyPoints.length > 0) {
          outlineSummary += '1. Key Points\n';
          keyPoints.forEach((point, index) => {
            outlineSummary += `   ${index + 1}.1. ${point}\n`;
          });
          outlineSummary += '\n2. Summary\n';
        }
        sentences.forEach((sentence, index) => {
          outlineSummary += `   2.${index + 1}. ${sentence}\n`;
        });
        return outlineSummary;

      case 'paragraph':
      default:
        let paragraphSummary = '';
        if (keyPoints.length > 0) {
          paragraphSummary += keyPoints.join(' ') + '\n\n';
        }
        paragraphSummary += sentences.join(' ');
        return paragraphSummary;
    }
  }

  private extractFunctions(code: string, language: string): any[] {
    // Simplified function extraction - in production, use proper AST parsing
    const functions: any[] = [];
    
    const functionRegexes = {
      javascript: /function\s+(\w+)\s*\([^)]*\)/g,
      typescript: /(?:function\s+(\w+)|(\w+)\s*:\s*\([^)]*\)\s*=>)/g,
      python: /def\s+(\w+)\s*\([^)]*\):/g,
      java: /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\([^)]*\)/g
    };

    const regex = functionRegexes[language as keyof typeof functionRegexes];
    if (regex) {
      let match;
      while ((match = regex.exec(code)) !== null) {
        functions.push({
          name: match[1] || match[2],
          description: `Function ${match[1] || match[2]}`,
          parameters: [],
          returns: 'Unknown'
        });
      }
    }

    return functions;
  }

  private extractClasses(code: string, language: string): any[] {
    // Simplified class extraction
    const classes: any[] = [];
    
    const classRegexes = {
      javascript: /class\s+(\w+)/g,
      typescript: /class\s+(\w+)/g,
      python: /class\s+(\w+)/g,
      java: /(?:public|private)?\s*class\s+(\w+)/g
    };

    const regex = classRegexes[language as keyof typeof classRegexes];
    if (regex) {
      let match;
      while ((match = regex.exec(code)) !== null) {
        classes.push({
          name: match[1],
          description: `Class ${match[1]}`
        });
      }
    }

    return classes;
  }

  private extractImports(code: string, language: string): string[] {
    const imports: string[] = [];
    
    const importRegexes = {
      javascript: /import\s+.*?from\s+['"]([^'"]+)['"]/g,
      typescript: /import\s+.*?from\s+['"]([^'"]+)['"]/g,
      python: /(?:import\s+(\w+)|from\s+(\w+)\s+import)/g,
      java: /import\s+([^;]+);/g
    };

    const regex = importRegexes[language as keyof typeof importRegexes];
    if (regex) {
      let match;
      while ((match = regex.exec(code)) !== null) {
        imports.push(match[1] || match[2]);
      }
    }

    return imports;
  }
}
