// Error display component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../hooks/useTheme.js';

export interface ErrorDisplayProps {
  error: Error;
  showDetails?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  showDetails = false,
  onRetry,
  onDismiss
}) => {
  const theme = useTheme();

  const getErrorType = (error: Error): string => {
    if (error.name === 'ArienError') {
      return (error as any).code || 'UNKNOWN_ERROR';
    }
    return error.name || 'Error';
  };

  const getErrorSeverity = (error: Error): 'low' | 'medium' | 'high' => {
    const errorType = getErrorType(error);
    
    if (errorType.includes('AUTHENTICATION') || errorType.includes('PERMISSION')) {
      return 'high';
    }
    
    if (errorType.includes('NETWORK') || errorType.includes('TIMEOUT')) {
      return 'medium';
    }
    
    return 'low';
  };

  const getSeverityColor = (severity: 'low' | 'medium' | 'high'): string => {
    switch (severity) {
      case 'high': return theme.colors.error;
      case 'medium': return theme.colors.warning;
      case 'low': return theme.colors.muted;
    }
  };

  const severity = getErrorSeverity(error);
  const errorType = getErrorType(error);

  return (
    <Box
      borderStyle="round"
      borderColor={getSeverityColor(severity)}
      paddingX={2}
      paddingY={1}
      flexDirection="column"
    >
      {/* Error header */}
      <Box>
        <Text color={getSeverityColor(severity)} bold>
          ✗ {errorType}
        </Text>
      </Box>

      {/* Error message */}
      <Box marginTop={1}>
        <Text color={theme.colors.text}>
          {error.message}
        </Text>
      </Box>

      {/* Error details */}
      {showDetails && (error as any).details && (
        <Box marginTop={1} flexDirection="column">
          <Text color={theme.colors.muted} bold>
            Details:
          </Text>
          <Text color={theme.colors.muted}>
            {JSON.stringify((error as any).details, null, 2)}
          </Text>
        </Box>
      )}

      {/* Stack trace (only in development) */}
      {showDetails && error.stack && process.env.NODE_ENV === 'development' && (
        <Box marginTop={1} flexDirection="column">
          <Text color={theme.colors.muted} bold>
            Stack Trace:
          </Text>
          <Text color={theme.colors.muted}>
            {error.stack.split('\n').slice(0, 5).join('\n')}
          </Text>
        </Box>
      )}

      {/* Action buttons */}
      {(onRetry || onDismiss) && (
        <Box marginTop={1}>
          {onRetry && (
            <Text color={theme.colors.primary}>
              Press R to retry
            </Text>
          )}
          {onRetry && onDismiss && (
            <Text color={theme.colors.muted}> • </Text>
          )}
          {onDismiss && (
            <Text color={theme.colors.muted}>
              Press D to dismiss
            </Text>
          )}
        </Box>
      )}

      {/* Help text */}
      <Box marginTop={1}>
        <Text color={theme.colors.muted} dimColor>
          {severity === 'high' 
            ? 'This error requires attention. Check your configuration.'
            : severity === 'medium'
            ? 'This error might be temporary. Try again in a moment.'
            : 'This is a minor error that should resolve automatically.'
          }
        </Text>
      </Box>
    </Box>
  );
};
