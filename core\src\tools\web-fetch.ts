// Web content fetching tool
import { z } from 'zod';
import fetch from 'node-fetch';
import { <PERSON>Tool, ToolResult, ToolContext, ToolExecutionError } from './tools.js';
import { logger } from '../core/logger.js';

export const WebFetchSchema = z.object({
  url: z.string().url().describe('URL to fetch content from'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'HEAD']).optional().default('GET').describe('HTTP method'),
  headers: z.record(z.string()).optional().describe('HTTP headers to send'),
  body: z.string().optional().describe('Request body (for POST/PUT requests)'),
  timeout: z.number().optional().default(30000).describe('Request timeout in milliseconds'),
  followRedirects: z.boolean().optional().default(true).describe('Follow HTTP redirects'),
  maxRedirects: z.number().optional().default(5).describe('Maximum number of redirects to follow'),
  userAgent: z.string().optional().describe('User agent string'),
  format: z.enum(['text', 'json', 'html', 'raw']).optional().default('text').describe('Response format'),
  extractText: z.boolean().optional().default(false).describe('Extract text content from HTML'),
  maxSize: z.number().optional().default(10 * 1024 * 1024).describe('Maximum response size in bytes')
});

export interface WebFetchResult {
  url: string;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  content: string;
  contentType: string;
  size: number;
  responseTime: number;
  redirects?: string[];
  extractedText?: string;
}

export class WebFetchTool extends BaseTool {
  constructor() {
    super({
      name: 'web-fetch',
      description: 'Fetch content from web URLs with various options',
      parameters: WebFetchSchema,
      requiresApproval: false,
      riskLevel: 'medium',
      category: 'web'
    });
  }

  public async execute(params: any, context: ToolContext): Promise<ToolResult> {
    try {
      const validatedParams = this.validateParams(params);
      const startTime = Date.now();

      logger.debug('Fetching web content', {
        url: validatedParams.url,
        method: validatedParams.method,
        options: this.sanitizeParams(validatedParams)
      });

      // Validate URL
      this.validateUrl(validatedParams.url);

      const result = await this.fetchContent(validatedParams);
      result.responseTime = Date.now() - startTime;

      logger.info('Web fetch completed', {
        url: validatedParams.url,
        status: result.status,
        size: result.size,
        responseTime: result.responseTime
      });

      const summary = this.createSummary(result);
      return this.createSuccessResult(summary, result);

    } catch (error) {
      logger.error('Web fetch failed', {
        error: error instanceof Error ? error.message : String(error),
        params: this.sanitizeParams(params)
      });

      if (error instanceof ToolExecutionError) {
        throw error;
      }

      throw new ToolExecutionError(
        this.definition.name,
        error instanceof Error ? error.message : String(error),
        error instanceof Error ? error : undefined
      );
    }
  }

  private async fetchContent(params: any): Promise<WebFetchResult> {
    const { url, method, headers = {}, body, timeout, followRedirects, maxRedirects, userAgent, format, extractText, maxSize } = params;

    // Set default headers
    const requestHeaders = {
      'User-Agent': userAgent || 'Arien-CLI/1.0',
      'Accept': this.getAcceptHeader(format),
      ...headers
    };

    // Add content type for POST/PUT requests
    if ((method === 'POST' || method === 'PUT') && body && !requestHeaders['Content-Type']) {
      requestHeaders['Content-Type'] = 'application/json';
    }

    const fetchOptions: any = {
      method,
      headers: requestHeaders,
      timeout,
      redirect: followRedirects ? 'follow' : 'manual',
      follow: maxRedirects,
      size: maxSize
    };

    if (body && (method === 'POST' || method === 'PUT')) {
      fetchOptions.body = body;
    }

    try {
      const response = await fetch(url, fetchOptions);
      
      // Get response headers
      const responseHeaders: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        responseHeaders[key] = value;
      });

      const contentType = response.headers.get('content-type') || 'unknown';
      
      // Read response content
      let content: string;
      try {
        if (format === 'json') {
          const jsonData = await response.json();
          content = JSON.stringify(jsonData, null, 2);
        } else {
          content = await response.text();
        }
      } catch (error) {
        throw new ToolExecutionError(
          this.definition.name,
          `Failed to read response content: ${error instanceof Error ? error.message : String(error)}`
        );
      }

      const result: WebFetchResult = {
        url: response.url,
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
        content,
        contentType,
        size: Buffer.byteLength(content, 'utf8'),
        responseTime: 0 // Will be set by caller
      };

      // Extract text from HTML if requested
      if (extractText && this.isHtmlContent(contentType)) {
        result.extractedText = this.extractTextFromHtml(content);
      }

      // Check if response was successful
      if (!response.ok) {
        logger.warn('HTTP request returned error status', {
          url,
          status: response.status,
          statusText: response.statusText
        });
      }

      return result;

    } catch (error) {
      if (error instanceof ToolExecutionError) {
        throw error;
      }

      // Handle specific fetch errors
      if (error.name === 'AbortError') {
        throw new ToolExecutionError(this.definition.name, `Request timeout: ${url}`);
      }
      
      if (error.code === 'ENOTFOUND') {
        throw new ToolExecutionError(this.definition.name, `Host not found: ${url}`);
      }
      
      if (error.code === 'ECONNREFUSED') {
        throw new ToolExecutionError(this.definition.name, `Connection refused: ${url}`);
      }

      throw new ToolExecutionError(
        this.definition.name,
        `Network error: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private validateUrl(url: string): void {
    try {
      const parsedUrl = new URL(url);
      
      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        throw new ToolExecutionError(
          this.definition.name,
          `Unsupported protocol: ${parsedUrl.protocol}. Only HTTP and HTTPS are allowed.`
        );
      }

      // Block localhost and private IP ranges for security
      const hostname = parsedUrl.hostname.toLowerCase();
      if (this.isPrivateOrLocalhost(hostname)) {
        throw new ToolExecutionError(
          this.definition.name,
          `Access to private/localhost addresses is not allowed: ${hostname}`
        );
      }

    } catch (error) {
      if (error instanceof ToolExecutionError) {
        throw error;
      }
      
      throw new ToolExecutionError(
        this.definition.name,
        `Invalid URL: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private isPrivateOrLocalhost(hostname: string): boolean {
    // Check for localhost
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1') {
      return true;
    }

    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
      /^192\.168\./,
      /^169\.254\./, // Link-local
      /^fc00:/, // IPv6 private
      /^fe80:/ // IPv6 link-local
    ];

    return privateRanges.some(range => range.test(hostname));
  }

  private getAcceptHeader(format: string): string {
    switch (format) {
      case 'json':
        return 'application/json, */*';
      case 'html':
        return 'text/html, application/xhtml+xml, */*';
      case 'text':
        return 'text/plain, text/*, */*';
      default:
        return '*/*';
    }
  }

  private isHtmlContent(contentType: string): boolean {
    return contentType.includes('text/html') || contentType.includes('application/xhtml');
  }

  private extractTextFromHtml(html: string): string {
    // Simple HTML text extraction - remove tags and decode entities
    let text = html
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // Remove scripts
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // Remove styles
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    return text;
  }

  private createSummary(result: WebFetchResult): string {
    const lines = [
      `Fetched content from: ${result.url}`,
      `Status: ${result.status} ${result.statusText}`,
      `Content-Type: ${result.contentType}`,
      `Size: ${this.formatSize(result.size)}`,
      `Response Time: ${result.responseTime}ms`
    ];

    if (result.extractedText) {
      lines.push(`Extracted Text Length: ${result.extractedText.length} characters`);
    }

    return lines.join('\n');
  }

  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(unitIndex === 0 ? 0 : 1)}${units[unitIndex]}`;
  }

  private sanitizeParams(params: any): any {
    // Remove potentially sensitive information for logging
    const sanitized = { ...params };
    
    // Remove sensitive headers
    if (sanitized.headers) {
      const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
      for (const header of sensitiveHeaders) {
        if (sanitized.headers[header]) {
          sanitized.headers[header] = '[REDACTED]';
        }
      }
    }

    // Truncate body for logging
    if (sanitized.body && sanitized.body.length > 200) {
      sanitized.body = sanitized.body.substring(0, 200) + '...';
    }
    
    return sanitized;
  }
}
