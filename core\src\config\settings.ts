// User settings with scope-based configuration
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../core/logger.js';

export type SettingScope = 'global' | 'workspace' | 'project' | 'session';

export interface Setting {
  key: string;
  value: any;
  scope: SettingScope;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  default?: any;
  validation?: z.ZodSchema;
  category?: string;
  sensitive?: boolean;
}

export interface SettingsGroup {
  name: string;
  description: string;
  settings: Record<string, Setting>;
}

export interface SettingsConfig {
  groups: Record<string, SettingsGroup>;
  scopePriority: SettingScope[];
  autoSave: boolean;
  encryptSensitive: boolean;
}

const SettingSchema = z.object({
  key: z.string(),
  value: z.any(),
  scope: z.enum(['global', 'workspace', 'project', 'session']),
  description: z.string().optional(),
  type: z.enum(['string', 'number', 'boolean', 'object', 'array']),
  default: z.any().optional(),
  category: z.string().optional(),
  sensitive: z.boolean().optional().default(false)
});

const SettingsGroupSchema = z.object({
  name: z.string(),
  description: z.string(),
  settings: z.record(SettingSchema)
});

const SettingsConfigSchema = z.object({
  groups: z.record(SettingsGroupSchema),
  scopePriority: z.array(z.enum(['global', 'workspace', 'project', 'session'])),
  autoSave: z.boolean().default(true),
  encryptSensitive: z.boolean().default(true)
});

export class SettingsManager {
  private config: SettingsConfig;
  private settingsCache = new Map<string, Map<SettingScope, any>>();
  private settingsPaths: Record<SettingScope, string>;
  private changeListeners = new Map<string, Array<(value: any, oldValue: any) => void>>();

  constructor(
    globalPath: string,
    workspacePath?: string,
    projectPath?: string
  ) {
    this.settingsPaths = {
      global: globalPath,
      workspace: workspacePath || path.join(process.cwd(), '.arien', 'workspace-settings.json'),
      project: projectPath || path.join(process.cwd(), '.arien', 'project-settings.json'),
      session: '' // Session settings are not persisted
    };

    this.config = SettingsConfigSchema.parse({
      groups: this.getDefaultGroups(),
      scopePriority: ['session', 'project', 'workspace', 'global']
    });

    this.loadAllSettings();
  }

  public async get<T = any>(key: string, scope?: SettingScope): Promise<T | undefined> {
    if (scope) {
      return this.getFromScope(key, scope);
    }

    // Get value using scope priority
    for (const currentScope of this.config.scopePriority) {
      const value = this.getFromScope(key, currentScope);
      if (value !== undefined) {
        return value;
      }
    }

    // Return default value if available
    const setting = this.findSetting(key);
    return setting?.default;
  }

  public async set<T = any>(
    key: string,
    value: T,
    scope: SettingScope = 'global'
  ): Promise<void> {
    const setting = this.findSetting(key);
    if (!setting) {
      throw new Error(`Setting '${key}' is not defined`);
    }

    // Validate value
    if (setting.validation) {
      try {
        setting.validation.parse(value);
      } catch (error) {
        throw new Error(`Invalid value for setting '${key}': ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Validate type
    if (!this.validateType(value, setting.type)) {
      throw new Error(`Invalid type for setting '${key}'. Expected ${setting.type}, got ${typeof value}`);
    }

    const oldValue = await this.get(key, scope);

    // Update cache
    if (!this.settingsCache.has(key)) {
      this.settingsCache.set(key, new Map());
    }
    this.settingsCache.get(key)!.set(scope, value);

    // Save to file if not session scope
    if (scope !== 'session' && this.config.autoSave) {
      await this.saveScope(scope);
    }

    // Notify listeners
    this.notifyListeners(key, value, oldValue);

    logger.debug('Setting updated', {
      key,
      scope,
      type: setting.type,
      category: setting.category
    });
  }

  public async unset(key: string, scope: SettingScope): Promise<boolean> {
    const scopeCache = this.settingsCache.get(key);
    if (!scopeCache || !scopeCache.has(scope)) {
      return false;
    }

    const oldValue = scopeCache.get(scope);
    scopeCache.delete(key);

    // Save to file if not session scope
    if (scope !== 'session' && this.config.autoSave) {
      await this.saveScope(scope);
    }

    // Notify listeners
    const newValue = await this.get(key);
    this.notifyListeners(key, newValue, oldValue);

    logger.debug('Setting unset', { key, scope });
    return true;
  }

  public async reset(key: string, scope?: SettingScope): Promise<void> {
    const setting = this.findSetting(key);
    if (!setting) {
      throw new Error(`Setting '${key}' is not defined`);
    }

    if (scope) {
      await this.unset(key, scope);
    } else {
      // Reset in all scopes
      for (const currentScope of Object.values(this.config.scopePriority)) {
        await this.unset(key, currentScope);
      }
    }

    logger.debug('Setting reset', { key, scope });
  }

  public async has(key: string, scope?: SettingScope): Promise<boolean> {
    if (scope) {
      return this.getFromScope(key, scope) !== undefined;
    }

    return (await this.get(key)) !== undefined;
  }

  public async list(
    category?: string,
    scope?: SettingScope
  ): Promise<Array<{ key: string; value: any; scope: SettingScope; setting: Setting }>> {
    const results: Array<{ key: string; value: any; scope: SettingScope; setting: Setting }> = [];

    for (const [groupName, group] of Object.entries(this.config.groups)) {
      for (const [settingKey, setting] of Object.entries(group.settings)) {
        if (category && setting.category !== category) {
          continue;
        }

        if (scope) {
          const value = this.getFromScope(settingKey, scope);
          if (value !== undefined) {
            results.push({ key: settingKey, value, scope, setting });
          }
        } else {
          const value = await this.get(settingKey);
          if (value !== undefined) {
            // Find which scope provided the value
            let valueScope: SettingScope = 'global';
            for (const currentScope of this.config.scopePriority) {
              if (this.getFromScope(settingKey, currentScope) !== undefined) {
                valueScope = currentScope;
                break;
              }
            }
            results.push({ key: settingKey, value, scope: valueScope, setting });
          }
        }
      }
    }

    return results;
  }

  public async export(scope?: SettingScope): Promise<Record<string, any>> {
    const exported: Record<string, any> = {};
    const settings = await this.list(undefined, scope);

    for (const { key, value, setting } of settings) {
      if (!setting.sensitive) {
        exported[key] = value;
      }
    }

    return exported;
  }

  public async import(
    settings: Record<string, any>,
    scope: SettingScope = 'global',
    overwrite: boolean = false
  ): Promise<{ imported: number; skipped: number; errors: string[] }> {
    let imported = 0;
    let skipped = 0;
    const errors: string[] = [];

    for (const [key, value] of Object.entries(settings)) {
      try {
        const exists = await this.has(key, scope);
        if (exists && !overwrite) {
          skipped++;
          continue;
        }

        await this.set(key, value, scope);
        imported++;
      } catch (error) {
        errors.push(`Failed to import ${key}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    logger.info('Settings imported', { imported, skipped, errors: errors.length });
    return { imported, skipped, errors };
  }

  public onChange(key: string, listener: (value: any, oldValue: any) => void): () => void {
    if (!this.changeListeners.has(key)) {
      this.changeListeners.set(key, []);
    }
    this.changeListeners.get(key)!.push(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.changeListeners.get(key);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  public async save(scope?: SettingScope): Promise<void> {
    if (scope) {
      await this.saveScope(scope);
    } else {
      for (const currentScope of ['global', 'workspace', 'project'] as SettingScope[]) {
        await this.saveScope(currentScope);
      }
    }
  }

  private getFromScope(key: string, scope: SettingScope): any {
    return this.settingsCache.get(key)?.get(scope);
  }

  private findSetting(key: string): Setting | undefined {
    for (const group of Object.values(this.config.groups)) {
      if (group.settings[key]) {
        return group.settings[key];
      }
    }
    return undefined;
  }

  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number';
      case 'boolean':
        return typeof value === 'boolean';
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'array':
        return Array.isArray(value);
      default:
        return true;
    }
  }

  private async loadAllSettings(): Promise<void> {
    for (const scope of ['global', 'workspace', 'project'] as SettingScope[]) {
      await this.loadScope(scope);
    }
  }

  private async loadScope(scope: SettingScope): Promise<void> {
    if (scope === 'session') {
      return; // Session settings are not persisted
    }

    const filePath = this.settingsPaths[scope];
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const data = JSON.parse(content);

      for (const [key, value] of Object.entries(data)) {
        if (!this.settingsCache.has(key)) {
          this.settingsCache.set(key, new Map());
        }
        this.settingsCache.get(key)!.set(scope, value);
      }

      logger.debug('Settings loaded', { scope, count: Object.keys(data).length });
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        logger.warn('Failed to load settings', {
          scope,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  private async saveScope(scope: SettingScope): Promise<void> {
    if (scope === 'session') {
      return; // Session settings are not persisted
    }

    const filePath = this.settingsPaths[scope];
    const data: Record<string, any> = {};

    // Collect all settings for this scope
    for (const [key, scopeMap] of this.settingsCache.entries()) {
      if (scopeMap.has(scope)) {
        data[key] = scopeMap.get(scope);
      }
    }

    try {
      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });
      
      // Write settings file
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      
      logger.debug('Settings saved', { scope, count: Object.keys(data).length });
    } catch (error) {
      logger.error('Failed to save settings', {
        scope,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  private notifyListeners(key: string, newValue: any, oldValue: any): void {
    const listeners = this.changeListeners.get(key);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(newValue, oldValue);
        } catch (error) {
          logger.error('Settings change listener error', {
            key,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }
    }
  }

  private getDefaultGroups(): Record<string, SettingsGroup> {
    return {
      general: {
        name: 'General',
        description: 'General application settings',
        settings: {
          'theme': {
            key: 'theme',
            value: 'default',
            scope: 'global',
            type: 'string',
            default: 'default',
            description: 'Application theme',
            category: 'appearance'
          },
          'autoSave': {
            key: 'autoSave',
            value: true,
            scope: 'global',
            type: 'boolean',
            default: true,
            description: 'Automatically save changes',
            category: 'behavior'
          },
          'historySize': {
            key: 'historySize',
            value: 1000,
            scope: 'global',
            type: 'number',
            default: 1000,
            description: 'Maximum number of history items to keep',
            category: 'behavior'
          }
        }
      },
      ai: {
        name: 'AI Settings',
        description: 'AI provider and model settings',
        settings: {
          'defaultProvider': {
            key: 'defaultProvider',
            value: 'deepseek',
            scope: 'global',
            type: 'string',
            default: 'deepseek',
            description: 'Default AI provider',
            category: 'ai'
          },
          'defaultModel': {
            key: 'defaultModel',
            value: 'deepseek-chat',
            scope: 'global',
            type: 'string',
            default: 'deepseek-chat',
            description: 'Default AI model',
            category: 'ai'
          },
          'temperature': {
            key: 'temperature',
            value: 0.7,
            scope: 'global',
            type: 'number',
            default: 0.7,
            description: 'AI response creativity (0.0 - 1.0)',
            category: 'ai'
          }
        }
      },
      security: {
        name: 'Security',
        description: 'Security and sandbox settings',
        settings: {
          'sandboxEnabled': {
            key: 'sandboxEnabled',
            value: true,
            scope: 'global',
            type: 'boolean',
            default: true,
            description: 'Enable security sandbox',
            category: 'security'
          },
          'approvalLevel': {
            key: 'approvalLevel',
            value: 'default',
            scope: 'global',
            type: 'string',
            default: 'default',
            description: 'Tool approval level (default, auto-edit, yolo)',
            category: 'security'
          }
        }
      }
    };
  }
}
