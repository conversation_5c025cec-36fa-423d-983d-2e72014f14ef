// Telemetry SDK integration
import { logger } from '../core/logger.js';
import { getConfig } from '../config/config.js';
import { 
  TelemetryEvent, 
  TelemetryMetric, 
  TelemetryConfig,
  ErrorReport,
  TelemetryEventName,
  TelemetryMetricName
} from './types.js';
import { 
  getTelemetryLogger, 
  initTelemetryLogger,
  FileTelemetryProvider,
  ConsoleTelemetryProvider
} from './loggers.js';
import { 
  getMetricsCollector,
  startMetricsCollection,
  stopMetricsCollection
} from './metrics.js';
import { 
  TELEMETRY_EVENTS,
  TELEMETRY_METRICS,
  DEFAULT_TELEMETRY_CONFIG
} from './constants.js';
import * as os from 'os';
import * as path from 'path';

export class TelemetrySDK {
  private static instance: TelemetrySDK | null = null;
  private initialized = false;
  private sessionId: string;
  private userId: string;
  private startTime: Date;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.userId = this.generateUserId();
    this.startTime = new Date();
  }

  public static getInstance(): TelemetrySDK {
    if (!TelemetrySDK.instance) {
      TelemetrySDK.instance = new TelemetrySDK();
    }
    return TelemetrySDK.instance;
  }

  public async initialize(config?: Partial<TelemetryConfig>): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Get configuration
      const arienConfig = getConfig().getConfig();
      const telemetryEnabled = arienConfig.telemetry !== false;
      
      if (!telemetryEnabled) {
        logger.info('Telemetry disabled by configuration');
        return;
      }

      // Initialize telemetry logger
      const telemetryConfig: Partial<TelemetryConfig> = {
        ...DEFAULT_TELEMETRY_CONFIG,
        enabled: telemetryEnabled,
        debug: arienConfig.debug || false,
        ...config
      };

      const telemetryLogger = initTelemetryLogger(telemetryConfig);

      // Add providers
      this.addDefaultProviders(telemetryLogger);

      // Start metrics collection
      startMetricsCollection();

      // Record initialization event
      await this.recordEvent(TELEMETRY_EVENTS.APP_STARTED, {
        version: process.env.ARIEN_VERSION || '1.0.0',
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        sessionId: this.sessionId,
        userId: this.userId
      });

      this.initialized = true;
      logger.info('Telemetry SDK initialized', { 
        sessionId: this.sessionId,
        enabled: telemetryEnabled
      });

    } catch (error) {
      logger.error('Failed to initialize telemetry SDK', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async shutdown(): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      // Record shutdown event
      await this.recordEvent(TELEMETRY_EVENTS.APP_STOPPED, {
        sessionDuration: Date.now() - this.startTime.getTime(),
        sessionId: this.sessionId
      });

      // Stop metrics collection
      stopMetricsCollection();

      // Shutdown telemetry logger
      const telemetryLogger = getTelemetryLogger();
      await telemetryLogger.shutdown();

      this.initialized = false;
      logger.info('Telemetry SDK shut down');

    } catch (error) {
      logger.error('Failed to shutdown telemetry SDK', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async recordEvent(
    name: TelemetryEventName, 
    properties?: Record<string, any>
  ): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      const event: TelemetryEvent = {
        name,
        timestamp: new Date(),
        userId: this.userId,
        sessionId: this.sessionId,
        properties: properties || {},
        context: this.getContext()
      };

      const telemetryLogger = getTelemetryLogger();
      await telemetryLogger.logEvent(event);

    } catch (error) {
      logger.error('Failed to record telemetry event', {
        eventName: name,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async recordMetric(
    name: TelemetryMetricName,
    value: number,
    unit?: string,
    tags?: Record<string, string>
  ): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      const metric: TelemetryMetric = {
        name,
        value,
        unit,
        timestamp: new Date(),
        tags
      };

      const telemetryLogger = getTelemetryLogger();
      await telemetryLogger.logMetric(metric);

    } catch (error) {
      logger.error('Failed to record telemetry metric', {
        metricName: name,
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async recordError(error: Error, context?: Record<string, any>): Promise<void> {
    if (!this.initialized) {
      return;
    }

    try {
      const errorReport: ErrorReport = {
        errorType: error.constructor.name,
        errorMessage: error.message,
        stackTrace: error.stack,
        context: context || {},
        timestamp: new Date(),
        userId: this.userId,
        sessionId: this.sessionId,
        version: process.env.ARIEN_VERSION || '1.0.0',
        platform: os.platform(),
        severity: this.assessErrorSeverity(error)
      };

      const telemetryLogger = getTelemetryLogger();
      await telemetryLogger.logError(errorReport);

    } catch (telemetryError) {
      logger.error('Failed to record error telemetry', {
        originalError: error.message,
        telemetryError: telemetryError instanceof Error ? telemetryError.message : String(telemetryError)
      });
    }
  }

  public async recordCommandExecution(
    command: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.recordEvent(TELEMETRY_EVENTS.COMMAND_EXECUTED, {
      command,
      duration,
      success,
      ...metadata
    });

    await this.recordMetric(TELEMETRY_METRICS.RESPONSE_TIME, duration, 'ms', {
      command,
      success: success.toString()
    });
  }

  public async recordToolUsage(
    toolName: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.recordEvent(TELEMETRY_EVENTS.TOOL_USED, {
      toolName,
      duration,
      success,
      ...metadata
    });

    // Update metrics collector
    const metricsCollector = getMetricsCollector();
    metricsCollector.incrementCounter('tools_used');
    metricsCollector.recordHistogram('tool_duration', duration);
  }

  public async recordProviderSwitch(
    fromProvider: string,
    toProvider: string,
    reason?: string
  ): Promise<void> {
    await this.recordEvent(TELEMETRY_EVENTS.PROVIDER_SWITCHED, {
      fromProvider,
      toProvider,
      reason
    });
  }

  public async recordApprovalRequest(
    operation: string,
    approved: boolean,
    riskLevel: string,
    duration: number
  ): Promise<void> {
    const eventName = approved 
      ? TELEMETRY_EVENTS.APPROVAL_GRANTED 
      : TELEMETRY_EVENTS.APPROVAL_DENIED;

    await this.recordEvent(eventName, {
      operation,
      riskLevel,
      duration
    });

    // Update metrics
    const metricsCollector = getMetricsCollector();
    metricsCollector.incrementCounter('approvals_requested');
    if (approved) {
      metricsCollector.incrementCounter('approvals_granted');
    } else {
      metricsCollector.incrementCounter('approvals_denied');
    }
  }

  public async recordFileAccess(
    filePath: string,
    operation: string,
    success: boolean,
    size?: number
  ): Promise<void> {
    await this.recordEvent(TELEMETRY_EVENTS.FILE_ACCESSED, {
      operation,
      success,
      size,
      fileExtension: path.extname(filePath),
      // Don't log full path for privacy
      hasPath: !!filePath
    });

    const metricsCollector = getMetricsCollector();
    metricsCollector.incrementCounter('files_accessed');
  }

  public async recordNetworkRequest(
    url: string,
    method: string,
    statusCode: number,
    duration: number,
    size?: number
  ): Promise<void> {
    await this.recordEvent(TELEMETRY_EVENTS.NETWORK_REQUEST, {
      method,
      statusCode,
      duration,
      size,
      // Don't log full URL for privacy
      domain: new URL(url).hostname
    });

    await this.recordMetric(TELEMETRY_METRICS.NETWORK_LATENCY, duration, 'ms', {
      method,
      statusCode: statusCode.toString()
    });

    const metricsCollector = getMetricsCollector();
    metricsCollector.incrementCounter('network_requests');
  }

  public getSessionId(): string {
    return this.sessionId;
  }

  public getUserId(): string {
    return this.userId;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  private addDefaultProviders(telemetryLogger: any): void {
    // Add file provider for local logging
    const config = getConfig().getConfig();
    const logDir = path.join(os.homedir(), '.arien', 'telemetry');
    const fileProvider = new FileTelemetryProvider(logDir);
    telemetryLogger.addProvider(fileProvider);

    // Add console provider for debugging
    if (config.debug) {
      const consoleProvider = new ConsoleTelemetryProvider();
      telemetryLogger.addProvider(consoleProvider);
    }
  }

  private getContext() {
    return {
      version: process.env.ARIEN_VERSION || '1.0.0',
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      sessionId: this.sessionId,
      userId: this.userId
    };
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateUserId(): string {
    // Generate a stable but anonymous user ID
    const machineId = os.hostname() + os.platform() + os.arch();
    let hash = 0;
    for (let i = 0; i < machineId.length; i++) {
      const char = machineId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `user_${Math.abs(hash).toString(36)}`;
  }

  private assessErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const errorMessage = error.message.toLowerCase();
    const errorName = error.constructor.name.toLowerCase();

    // Critical errors
    if (errorMessage.includes('out of memory') || 
        errorMessage.includes('segmentation fault') ||
        errorName.includes('fatal')) {
      return 'critical';
    }

    // High severity errors
    if (errorMessage.includes('permission denied') ||
        errorMessage.includes('access denied') ||
        errorMessage.includes('authentication') ||
        errorName.includes('security')) {
      return 'high';
    }

    // Medium severity errors
    if (errorMessage.includes('network') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('connection') ||
        errorName.includes('network')) {
      return 'medium';
    }

    // Default to low severity
    return 'low';
  }
}

// Convenience functions for global access
export const telemetry = TelemetrySDK.getInstance();

export async function initTelemetry(config?: Partial<TelemetryConfig>): Promise<void> {
  await telemetry.initialize(config);
}

export async function shutdownTelemetry(): Promise<void> {
  await telemetry.shutdown();
}

export async function recordEvent(
  name: TelemetryEventName, 
  properties?: Record<string, any>
): Promise<void> {
  await telemetry.recordEvent(name, properties);
}

export async function recordMetric(
  name: TelemetryMetricName,
  value: number,
  unit?: string,
  tags?: Record<string, string>
): Promise<void> {
  await telemetry.recordMetric(name, value, unit, tags);
}

export async function recordError(
  error: Error, 
  context?: Record<string, any>
): Promise<void> {
  await telemetry.recordError(error, context);
}
