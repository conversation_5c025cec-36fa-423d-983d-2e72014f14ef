// Loading spinner for AI responses
import React, { useState, useEffect } from 'react';
import { Box, Text } from 'ink';
import Spinner from 'ink-spinner';

interface ArienRespondingSpinnerProps {
  message?: string;
  showElapsed?: boolean;
  variant?: 'dots' | 'line' | 'pipe' | 'simpleDots' | 'simpleDotsScrolling' | 'star' | 'toggle';
}

export const ArienRespondingSpinner: React.FC<ArienRespondingSpinnerProps> = ({
  message = '<PERSON><PERSON> is thinking...',
  showElapsed = true,
  variant = 'dots'
}) => {
  const [elapsed, setElapsed] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  useEffect(() => {
    const startTime = Date.now();
    
    const timer = setInterval(() => {
      setElapsed(Date.now() - startTime);
    }, 100);

    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    // Cycle through different thinking messages
    const messages = [
      '<PERSON><PERSON> is thinking...',
      'Processing your request...',
      'Analyzing context...',
      'Generating response...',
      'Almost ready...'
    ];

    const messageTimer = setInterval(() => {
      const randomMessage = messages[Math.floor(Math.random() * messages.length)];
      setCurrentMessage(randomMessage);
    }, 2000);

    return () => clearInterval(messageTimer);
  }, []);

  const formatElapsed = (ms: number): string => {
    const seconds = Math.floor(ms / 1000);
    const milliseconds = Math.floor((ms % 1000) / 100);
    return `${seconds}.${milliseconds}s`;
  };

  return (
    <Box flexDirection="row" alignItems="center">
      <Box marginRight={1}>
        <Spinner type={variant} />
      </Box>
      
      <Text color="blue">
        {currentMessage}
      </Text>
      
      {showElapsed && (
        <Text color="gray" dimColor>
          {' '}({formatElapsed(elapsed)})
        </Text>
      )}
    </Box>
  );
};
