// Secure credential storage system
import * as crypto from 'crypto';
import * as fs from 'fs-extra';
import * as path from 'path';
import * as os from 'os';
import { logger } from '../core/logger.js';
import { ArienError, ErrorCode } from '../utils/errors.js';

export interface EncryptedCredential {
  id: string;
  provider: string;
  type: string;
  encryptedData: string;
  iv: string;
  salt: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface CredentialStoreConfig {
  storePath?: string;
  encryptionAlgorithm: string;
  keyDerivationIterations: number;
  masterPasswordRequired: boolean;
  autoLock: boolean;
  lockTimeout: number; // in milliseconds
}

export class CredentialStore {
  private config: CredentialStoreConfig;
  private storePath: string;
  private masterKey?: Buffer;
  private isUnlocked = false;
  private lockTimer?: NodeJS.Timeout;
  private credentials = new Map<string, EncryptedCredential>();

  constructor(config: Partial<CredentialStoreConfig> = {}) {
    this.config = {
      storePath: config.storePath || path.join(os.homedir(), '.arien', 'credentials.json'),
      encryptionAlgorithm: 'aes-256-gcm',
      keyDerivationIterations: 100000,
      masterPasswordRequired: config.masterPasswordRequired ?? true,
      autoLock: config.autoLock ?? true,
      lockTimeout: config.lockTimeout ?? 15 * 60 * 1000 // 15 minutes
    };

    this.storePath = this.config.storePath!;
  }

  public async initialize(masterPassword?: string): Promise<void> {
    try {
      // Ensure store directory exists
      await fs.ensureDir(path.dirname(this.storePath));

      // Load existing credentials if store exists
      if (await fs.pathExists(this.storePath)) {
        await this.loadCredentials();
      }

      // Set up master key if password provided
      if (masterPassword) {
        await this.unlock(masterPassword);
      }

      logger.info('Credential store initialized', {
        storePath: this.storePath,
        credentialCount: this.credentials.size,
        unlocked: this.isUnlocked
      });

    } catch (error) {
      logger.error('Failed to initialize credential store', { error });
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to initialize credential store',
        { error }
      );
    }
  }

  public async unlock(masterPassword: string): Promise<void> {
    try {
      // Derive master key from password
      const salt = await this.getMasterSalt();
      this.masterKey = crypto.pbkdf2Sync(
        masterPassword,
        salt,
        this.config.keyDerivationIterations,
        32,
        'sha256'
      );

      this.isUnlocked = true;

      // Set up auto-lock timer
      if (this.config.autoLock) {
        this.resetLockTimer();
      }

      logger.info('Credential store unlocked');

    } catch (error) {
      logger.error('Failed to unlock credential store', { error });
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        'Failed to unlock credential store',
        { error }
      );
    }
  }

  public lock(): void {
    this.masterKey = undefined;
    this.isUnlocked = false;

    if (this.lockTimer) {
      clearTimeout(this.lockTimer);
      this.lockTimer = undefined;
    }

    logger.info('Credential store locked');
  }

  public async storeCredential(
    id: string,
    provider: string,
    type: string,
    credentials: Record<string, string>,
    metadata?: Record<string, any>
  ): Promise<void> {
    if (!this.isUnlocked) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        'Credential store is locked'
      );
    }

    try {
      const encryptedCredential = await this.encryptCredential({
        id,
        provider,
        type,
        data: credentials,
        metadata
      });

      this.credentials.set(id, encryptedCredential);
      await this.saveCredentials();

      this.resetLockTimer();

      logger.info('Credential stored', {
        id,
        provider,
        type
      });

    } catch (error) {
      logger.error('Failed to store credential', { id, provider, error });
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to store credential',
        { id, provider, error }
      );
    }
  }

  public async retrieveCredential(id: string): Promise<Record<string, string> | null> {
    if (!this.isUnlocked) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        'Credential store is locked'
      );
    }

    try {
      const encryptedCredential = this.credentials.get(id);
      if (!encryptedCredential) {
        return null;
      }

      const decryptedData = await this.decryptCredential(encryptedCredential);
      this.resetLockTimer();

      logger.debug('Credential retrieved', {
        id,
        provider: encryptedCredential.provider
      });

      return decryptedData;

    } catch (error) {
      logger.error('Failed to retrieve credential', { id, error });
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to retrieve credential',
        { id, error }
      );
    }
  }

  public async deleteCredential(id: string): Promise<boolean> {
    if (!this.isUnlocked) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        'Credential store is locked'
      );
    }

    try {
      const existed = this.credentials.has(id);
      if (existed) {
        this.credentials.delete(id);
        await this.saveCredentials();
        this.resetLockTimer();

        logger.info('Credential deleted', { id });
      }

      return existed;

    } catch (error) {
      logger.error('Failed to delete credential', { id, error });
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to delete credential',
        { id, error }
      );
    }
  }

  public listCredentials(): Array<{
    id: string;
    provider: string;
    type: string;
    createdAt: Date;
    updatedAt: Date;
    metadata?: Record<string, any>;
  }> {
    return Array.from(this.credentials.values()).map(cred => ({
      id: cred.id,
      provider: cred.provider,
      type: cred.type,
      createdAt: cred.createdAt,
      updatedAt: cred.updatedAt,
      metadata: cred.metadata
    }));
  }

  public isCredentialStoreUnlocked(): boolean {
    return this.isUnlocked;
  }

  public async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    if (!this.isUnlocked) {
      throw new ArienError(
        ErrorCode.AUTHENTICATION_ERROR,
        'Credential store is locked'
      );
    }

    try {
      // Verify old password
      const oldSalt = await this.getMasterSalt();
      const oldKey = crypto.pbkdf2Sync(
        oldPassword,
        oldSalt,
        this.config.keyDerivationIterations,
        32,
        'sha256'
      );

      if (!this.masterKey || !oldKey.equals(this.masterKey)) {
        throw new ArienError(
          ErrorCode.AUTHENTICATION_ERROR,
          'Current password is incorrect'
        );
      }

      // Generate new salt and key
      const newSalt = crypto.randomBytes(32);
      const newKey = crypto.pbkdf2Sync(
        newPassword,
        newSalt,
        this.config.keyDerivationIterations,
        32,
        'sha256'
      );

      // Re-encrypt all credentials with new key
      const oldMasterKey = this.masterKey;
      this.masterKey = newKey;

      // Decrypt with old key and re-encrypt with new key
      const reencryptedCredentials = new Map<string, EncryptedCredential>();
      
      for (const [id, credential] of this.credentials) {
        // Temporarily use old key for decryption
        this.masterKey = oldMasterKey;
        const decryptedData = await this.decryptCredential(credential);
        
        // Use new key for encryption
        this.masterKey = newKey;
        const reencryptedCredential = await this.encryptCredential({
          id: credential.id,
          provider: credential.provider,
          type: credential.type,
          data: decryptedData,
          metadata: credential.metadata
        });

        reencryptedCredentials.set(id, reencryptedCredential);
      }

      this.credentials = reencryptedCredentials;

      // Save new salt and credentials
      await this.saveMasterSalt(newSalt);
      await this.saveCredentials();

      logger.info('Master password changed successfully');

    } catch (error) {
      logger.error('Failed to change master password', { error });
      throw new ArienError(
        ErrorCode.CONFIGURATION_ERROR,
        'Failed to change master password',
        { error }
      );
    }
  }

  private async encryptCredential(data: {
    id: string;
    provider: string;
    type: string;
    data: Record<string, string>;
    metadata?: Record<string, any>;
  }): Promise<EncryptedCredential> {
    if (!this.masterKey) {
      throw new Error('Master key not available');
    }

    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.config.encryptionAlgorithm, this.masterKey);
    cipher.setAAD(Buffer.from(data.id));

    const plaintext = JSON.stringify(data.data);
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return {
      id: data.id,
      provider: data.provider,
      type: data.type,
      encryptedData: encrypted + ':' + authTag.toString('hex'),
      iv: iv.toString('hex'),
      salt: crypto.randomBytes(16).toString('hex'),
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: data.metadata
    };
  }

  private async decryptCredential(credential: EncryptedCredential): Promise<Record<string, string>> {
    if (!this.masterKey) {
      throw new Error('Master key not available');
    }

    const [encryptedData, authTagHex] = credential.encryptedData.split(':');
    const authTag = Buffer.from(authTagHex, 'hex');
    const iv = Buffer.from(credential.iv, 'hex');

    const decipher = crypto.createDecipher(this.config.encryptionAlgorithm, this.masterKey);
    decipher.setAAD(Buffer.from(credential.id));
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return JSON.parse(decrypted);
  }

  private async getMasterSalt(): Promise<Buffer> {
    const saltPath = path.join(path.dirname(this.storePath), '.salt');
    
    if (await fs.pathExists(saltPath)) {
      const saltHex = await fs.readFile(saltPath, 'utf8');
      return Buffer.from(saltHex, 'hex');
    } else {
      // Generate new salt
      const salt = crypto.randomBytes(32);
      await this.saveMasterSalt(salt);
      return salt;
    }
  }

  private async saveMasterSalt(salt: Buffer): Promise<void> {
    const saltPath = path.join(path.dirname(this.storePath), '.salt');
    await fs.writeFile(saltPath, salt.toString('hex'), { mode: 0o600 });
  }

  private async loadCredentials(): Promise<void> {
    try {
      const data = await fs.readFile(this.storePath, 'utf8');
      const credentialsArray: EncryptedCredential[] = JSON.parse(data);
      
      this.credentials.clear();
      for (const credential of credentialsArray) {
        // Convert date strings back to Date objects
        credential.createdAt = new Date(credential.createdAt);
        credential.updatedAt = new Date(credential.updatedAt);
        this.credentials.set(credential.id, credential);
      }

    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        logger.error('Failed to load credentials', { error });
      }
    }
  }

  private async saveCredentials(): Promise<void> {
    const credentialsArray = Array.from(this.credentials.values());
    const data = JSON.stringify(credentialsArray, null, 2);
    await fs.writeFile(this.storePath, data, { mode: 0o600 });
  }

  private resetLockTimer(): void {
    if (!this.config.autoLock) return;

    if (this.lockTimer) {
      clearTimeout(this.lockTimer);
    }

    this.lockTimer = setTimeout(() => {
      this.lock();
    }, this.config.lockTimeout);
  }
}

// Global credential store instance
let globalCredentialStore: CredentialStore | null = null;

export function getCredentialStore(): CredentialStore {
  if (!globalCredentialStore) {
    globalCredentialStore = new CredentialStore();
  }
  return globalCredentialStore;
}

export function initCredentialStore(config?: Partial<CredentialStoreConfig>): CredentialStore {
  globalCredentialStore = new CredentialStore(config);
  return globalCredentialStore;
}
