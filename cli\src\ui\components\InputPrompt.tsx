// Main input prompt with command processing
import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';

interface InputPromptProps {
  onSubmit: (message: string) => void;
  onExit: () => void;
  disabled?: boolean;
  placeholder?: string;
}

export const InputPrompt: React.FC<InputPromptProps> = ({
  onSubmit,
  onExit,
  disabled = false,
  placeholder = 'Type your message...'
}) => {
  const [input, setInput] = useState('');
  const [isActive, setIsActive] = useState(true);

  // Handle keyboard shortcuts
  useInput((input, key) => {
    if (key.ctrl && input === 'c') {
      onExit();
    }
  });

  const handleSubmit = () => {
    if (input.trim() && !disabled) {
      onSubmit(input.trim());
      setInput('');
    }
  };

  const handleInputChange = (value: string) => {
    if (!disabled) {
      setInput(value);
    }
  };

  // Focus management
  useEffect(() => {
    setIsActive(!disabled);
  }, [disabled]);

  return (
    <Box borderStyle="round" borderColor={disabled ? 'gray' : 'green'} padding={1}>
      <Box flexDirection="column" width="100%">
        <Box>
          <Text color={disabled ? 'gray' : 'green'}>
            {disabled ? '⏳' : '❯'}
          </Text>
          <Text> </Text>
          <TextInput
            value={input}
            onChange={handleInputChange}
            onSubmit={handleSubmit}
            placeholder={disabled ? 'Processing...' : placeholder}
            focus={isActive}
          />
        </Box>
        
        <Box marginTop={1}>
          <Text dimColor>
            {disabled 
              ? 'Please wait for the current request to complete...'
              : 'Press Enter to send, Ctrl+C to exit'
            }
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
