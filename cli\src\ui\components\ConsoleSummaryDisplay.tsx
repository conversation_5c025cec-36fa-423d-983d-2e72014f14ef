// Summarized console output display component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface ConsoleMessage {
  id: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  source?: string;
}

interface ConsoleSummaryDisplayProps {
  messages: ConsoleMessage[];
  maxMessages?: number;
  showTimestamps?: boolean;
  showSource?: boolean;
}

export const ConsoleSummaryDisplay: React.FC<ConsoleSummaryDisplayProps> = ({
  messages,
  maxMessages = 10,
  showTimestamps = false,
  showSource = false
}) => {
  const theme = useTheme();

  const recentMessages = messages.slice(-maxMessages);
  const errorCount = messages.filter(m => m.level === 'error').length;
  const warnCount = messages.filter(m => m.level === 'warn').length;

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return theme.colors.error;
      case 'warn': return theme.colors.warning;
      case 'info': return theme.colors.info;
      case 'debug': return theme.colors.muted;
      default: return theme.colors.text;
    }
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error': return '❌';
      case 'warn': return '⚠️';
      case 'info': return 'ℹ️';
      case 'debug': return '🐛';
      default: return '📝';
    }
  };

  if (messages.length === 0) {
    return (
      <Box flexDirection="column">
        <Text color={theme.colors.muted}>No console messages</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {/* Summary header */}
      <Box marginBottom={1}>
        <Text color={theme.colors.text}>Console Summary: </Text>
        {errorCount > 0 && (
          <Text color={theme.colors.error}>{errorCount} errors </Text>
        )}
        {warnCount > 0 && (
          <Text color={theme.colors.warning}>{warnCount} warnings </Text>
        )}
        <Text color={theme.colors.muted}>({messages.length} total)</Text>
      </Box>

      {/* Recent messages */}
      <Box flexDirection="column">
        {recentMessages.map((message) => (
          <Box key={message.id} marginBottom={0}>
            <Text color={getLevelColor(message.level)}>
              {getLevelIcon(message.level)} 
            </Text>
            {showTimestamps && (
              <Text color={theme.colors.muted}>
                [{message.timestamp.toLocaleTimeString()}] 
              </Text>
            )}
            {showSource && message.source && (
              <Text color={theme.colors.secondary}>
                [{message.source}] 
              </Text>
            )}
            <Text color={getLevelColor(message.level)}>
              {message.message}
            </Text>
          </Box>
        ))}
      </Box>

      {messages.length > maxMessages && (
        <Box marginTop={1}>
          <Text color={theme.colors.muted}>
            ... and {messages.length - maxMessages} more messages
          </Text>
        </Box>
      )}
    </Box>
  );
};
