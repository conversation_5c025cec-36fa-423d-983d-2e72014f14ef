// File system utilities
import { stat, readdir, access } from 'fs/promises';
import { join, resolve, relative, extname, basename, dirname } from 'path';
import { constants } from 'fs';

export interface FileInfo {
  path: string;
  name: string;
  size: number;
  isDirectory: boolean;
  isFile: boolean;
  extension: string;
  lastModified: Date;
  created: Date;
  permissions: {
    readable: boolean;
    writable: boolean;
    executable: boolean;
  };
}

export async function getFileInfo(filePath: string): Promise<FileInfo | null> {
  try {
    const stats = await stat(filePath);
    const name = basename(filePath);
    const extension = extname(filePath);

    // Check permissions
    const permissions = {
      readable: await checkAccess(filePath, constants.R_OK),
      writable: await checkAccess(filePath, constants.W_OK),
      executable: await checkAccess(filePath, constants.X_OK)
    };

    return {
      path: filePath,
      name,
      size: stats.size,
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile(),
      extension,
      lastModified: stats.mtime,
      created: stats.birthtime,
      permissions
    };
  } catch (error) {
    return null;
  }
}

async function checkAccess(filePath: string, mode: number): Promise<boolean> {
  try {
    await access(filePath, mode);
    return true;
  } catch {
    return false;
  }
}

export async function listDirectory(
  dirPath: string,
  options: {
    recursive?: boolean;
    includeHidden?: boolean;
    maxDepth?: number;
    filter?: (file: FileInfo) => boolean;
  } = {}
): Promise<FileInfo[]> {
  const { recursive = false, includeHidden = false, maxDepth = 10, filter } = options;
  const results: FileInfo[] = [];

  async function scanDirectory(currentPath: string, depth: number = 0): Promise<void> {
    if (depth > maxDepth) return;

    try {
      const entries = await readdir(currentPath);

      for (const entry of entries) {
        if (!includeHidden && entry.startsWith('.')) {
          continue;
        }

        const fullPath = join(currentPath, entry);
        const fileInfo = await getFileInfo(fullPath);

        if (fileInfo) {
          if (!filter || filter(fileInfo)) {
            results.push(fileInfo);
          }

          if (recursive && fileInfo.isDirectory) {
            await scanDirectory(fullPath, depth + 1);
          }
        }
      }
    } catch (error) {
      // Skip directories we can't read
    }
  }

  await scanDirectory(dirPath);
  return results;
}

export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
}

export function getRelativePath(from: string, to: string): string {
  return relative(from, to);
}

export function isSubPath(parent: string, child: string): boolean {
  const rel = relative(parent, child);
  return !rel.startsWith('..') && !rel.startsWith('/');
}

export function sanitizePath(path: string): string {
  // Remove dangerous path components
  return path
    .replace(/\.\./g, '') // Remove parent directory references
    .replace(/\/+/g, '/') // Normalize multiple slashes
    .replace(/^\/+/, '') // Remove leading slashes
    .trim();
}

export function getFileExtension(filePath: string): string {
  return extname(filePath).toLowerCase();
}

export function isTextFile(filePath: string): boolean {
  const textExtensions = [
    '.txt', '.md', '.json', '.js', '.ts', '.jsx', '.tsx', '.html', '.css',
    '.scss', '.sass', '.less', '.xml', '.yaml', '.yml', '.toml', '.ini',
    '.cfg', '.conf', '.log', '.sql', '.py', '.rb', '.php', '.java', '.c',
    '.cpp', '.h', '.hpp', '.cs', '.go', '.rs', '.swift', '.kt', '.scala',
    '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd', '.dockerfile',
    '.gitignore', '.gitattributes', '.editorconfig', '.eslintrc', '.prettierrc'
  ];

  const ext = getFileExtension(filePath);
  return textExtensions.includes(ext);
}

export function isBinaryFile(filePath: string): boolean {
  const binaryExtensions = [
    '.exe', '.dll', '.so', '.dylib', '.bin', '.dat', '.db', '.sqlite',
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.ico', '.svg', '.webp',
    '.mp3', '.wav', '.ogg', '.flac', '.mp4', '.avi', '.mkv', '.mov',
    '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
    '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz',
    '.dmg', '.iso', '.img'
  ];

  const ext = getFileExtension(filePath);
  return binaryExtensions.includes(ext);
}

export async function findFiles(
  searchPath: string,
  pattern: string | RegExp,
  options: {
    recursive?: boolean;
    includeHidden?: boolean;
    maxDepth?: number;
    caseSensitive?: boolean;
  } = {}
): Promise<FileInfo[]> {
  const { recursive = true, includeHidden = false, maxDepth = 10, caseSensitive = false } = options;

  const files = await listDirectory(searchPath, {
    recursive,
    includeHidden,
    maxDepth,
    filter: (file) => {
      if (file.isDirectory) return false;

      if (typeof pattern === 'string') {
        const fileName = caseSensitive ? file.name : file.name.toLowerCase();
        const searchPattern = caseSensitive ? pattern : pattern.toLowerCase();
        return fileName.includes(searchPattern);
      } else {
        return pattern.test(file.name);
      }
    }
  });

  return files;
}

export function createFileTree(files: FileInfo[], basePath: string): any {
  const tree: any = {};

  for (const file of files) {
    const relativePath = relative(basePath, file.path);
    const parts = relativePath.split(/[/\\]/);
    
    let current = tree;
    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      
      if (i === parts.length - 1) {
        // Leaf node (file)
        current[part] = {
          type: file.isDirectory ? 'directory' : 'file',
          size: file.size,
          lastModified: file.lastModified,
          extension: file.extension
        };
      } else {
        // Directory node
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }
    }
  }

  return tree;
}
