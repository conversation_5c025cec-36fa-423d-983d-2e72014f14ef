// Command history management hook
import { useState, useCallback, useEffect } from 'react';
import { readFile, writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { homedir } from 'os';

export interface HistoryEntry {
  id: string;
  command: string;
  timestamp: Date;
  type: 'user' | 'slash' | 'at';
  success?: boolean;
  duration?: number;
  context?: {
    workingDirectory: string;
    provider?: string;
    model?: string;
  };
}

export interface UseHistoryManagerReturn {
  history: HistoryEntry[];
  addToHistory: (command: string, type: HistoryEntry['type'], context?: HistoryEntry['context']) => void;
  getHistoryByType: (type: HistoryEntry['type']) => HistoryEntry[];
  searchHistory: (query: string) => HistoryEntry[];
  clearHistory: () => void;
  removeFromHistory: (id: string) => void;
  loadHistory: () => Promise<void>;
  saveHistory: () => Promise<void>;
  getRecentCommands: (count?: number) => string[];
  getCommandSuggestions: (partial: string) => string[];
}

export function useHistoryManager(maxHistorySize: number = 1000): UseHistoryManagerReturn {
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  const historyFilePath = join(homedir(), '.arien', 'history.json');

  // Load history from file
  const loadHistory = useCallback(async () => {
    try {
      const data = await readFile(historyFilePath, 'utf-8');
      const parsed = JSON.parse(data);
      
      const loadedHistory = parsed.map((entry: any) => ({
        ...entry,
        timestamp: new Date(entry.timestamp)
      }));
      
      setHistory(loadedHistory);
      setIsLoaded(true);
    } catch (error) {
      // File doesn't exist or is corrupted, start with empty history
      setHistory([]);
      setIsLoaded(true);
    }
  }, [historyFilePath]);

  // Save history to file
  const saveHistory = useCallback(async () => {
    try {
      // Ensure directory exists
      await mkdir(join(homedir(), '.arien'), { recursive: true });
      
      // Limit history size before saving
      const historyToSave = history.slice(-maxHistorySize);
      await writeFile(historyFilePath, JSON.stringify(historyToSave, null, 2));
    } catch (error) {
      console.error('Failed to save history:', error);
    }
  }, [history, historyFilePath, maxHistorySize]);

  // Load history on mount
  useEffect(() => {
    if (!isLoaded) {
      loadHistory();
    }
  }, [isLoaded, loadHistory]);

  // Auto-save history when it changes
  useEffect(() => {
    if (isLoaded && history.length > 0) {
      const timeoutId = setTimeout(() => {
        saveHistory();
      }, 1000); // Debounce saves

      return () => clearTimeout(timeoutId);
    }
  }, [history, isLoaded, saveHistory]);

  const addToHistory = useCallback((
    command: string,
    type: HistoryEntry['type'],
    context?: HistoryEntry['context']
  ) => {
    // Don't add empty commands or duplicates of the last command
    if (!command.trim()) return;
    
    const lastEntry = history[history.length - 1];
    if (lastEntry && lastEntry.command === command.trim()) return;

    const entry: HistoryEntry = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      command: command.trim(),
      timestamp: new Date(),
      type,
      context: context || {
        workingDirectory: process.cwd()
      }
    };

    setHistory(prev => {
      const newHistory = [...prev, entry];
      // Keep only the last maxHistorySize entries
      return newHistory.slice(-maxHistorySize);
    });
  }, [history, maxHistorySize]);

  const getHistoryByType = useCallback((type: HistoryEntry['type']): HistoryEntry[] => {
    return history.filter(entry => entry.type === type);
  }, [history]);

  const searchHistory = useCallback((query: string): HistoryEntry[] => {
    if (!query.trim()) return [];
    
    const lowerQuery = query.toLowerCase();
    return history.filter(entry =>
      entry.command.toLowerCase().includes(lowerQuery)
    ).reverse(); // Most recent first
  }, [history]);

  const clearHistory = useCallback(() => {
    setHistory([]);
  }, []);

  const removeFromHistory = useCallback((id: string) => {
    setHistory(prev => prev.filter(entry => entry.id !== id));
  }, []);

  const getRecentCommands = useCallback((count: number = 10): string[] => {
    return history
      .slice(-count)
      .reverse()
      .map(entry => entry.command);
  }, [history]);

  const getCommandSuggestions = useCallback((partial: string): string[] => {
    if (!partial.trim()) return [];
    
    const lowerPartial = partial.toLowerCase();
    const suggestions = new Set<string>();
    
    // Find commands that start with the partial
    history.forEach(entry => {
      if (entry.command.toLowerCase().startsWith(lowerPartial)) {
        suggestions.add(entry.command);
      }
    });
    
    // Find commands that contain the partial
    history.forEach(entry => {
      if (entry.command.toLowerCase().includes(lowerPartial) && 
          !entry.command.toLowerCase().startsWith(lowerPartial)) {
        suggestions.add(entry.command);
      }
    });
    
    return Array.from(suggestions)
      .slice(0, 10) // Limit suggestions
      .reverse(); // Most recent first
  }, [history]);

  return {
    history,
    addToHistory,
    getHistoryByType,
    searchHistory,
    clearHistory,
    removeFromHistory,
    loadHistory,
    saveHistory,
    getRecentCommands,
    getCommandSuggestions
  };
}
