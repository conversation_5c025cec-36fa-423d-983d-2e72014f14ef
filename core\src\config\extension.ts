// Extension system for custom configurations
import { z } from 'zod';
import { promises as fs } from 'fs';
import * as path from 'path';
import { logger } from '../core/logger.js';

export interface Extension {
  name: string;
  version: string;
  description: string;
  author?: string;
  homepage?: string;
  main: string;
  config?: Record<string, any>;
  dependencies?: Record<string, string>;
  permissions?: string[];
  enabled: boolean;
  loadedAt?: Date;
}

export interface ExtensionManifest {
  name: string;
  version: string;
  description: string;
  author?: string;
  homepage?: string;
  main: string;
  config?: Record<string, any>;
  dependencies?: Record<string, string>;
  permissions?: string[];
  engines?: {
    arien?: string;
    node?: string;
  };
}

export interface ExtensionContext {
  extensionPath: string;
  configPath: string;
  dataPath: string;
  logger: typeof logger;
  version: string;
}

export interface ExtensionAPI {
  registerCommand?: (name: string, handler: Function) => void;
  registerTool?: (tool: any) => void;
  registerTheme?: (theme: any) => void;
  onConfigChange?: (handler: (config: any) => void) => void;
  getConfig?: () => any;
  setConfig?: (config: any) => void;
}

const ExtensionManifestSchema = z.object({
  name: z.string().min(1),
  version: z.string().regex(/^\d+\.\d+\.\d+$/),
  description: z.string(),
  author: z.string().optional(),
  homepage: z.string().url().optional(),
  main: z.string(),
  config: z.record(z.any()).optional(),
  dependencies: z.record(z.string()).optional(),
  permissions: z.array(z.string()).optional(),
  engines: z.object({
    arien: z.string().optional(),
    node: z.string().optional()
  }).optional()
});

export class ExtensionManager {
  private extensions = new Map<string, Extension>();
  private extensionsPath: string;
  private loadedModules = new Map<string, any>();

  constructor(extensionsPath: string) {
    this.extensionsPath = extensionsPath;
  }

  public async loadExtensions(): Promise<void> {
    try {
      await fs.mkdir(this.extensionsPath, { recursive: true });
      
      const entries = await fs.readdir(this.extensionsPath, { withFileTypes: true });
      const extensionDirs = entries.filter(entry => entry.isDirectory());

      logger.info('Loading extensions', {
        extensionsPath: this.extensionsPath,
        foundExtensions: extensionDirs.length
      });

      for (const dir of extensionDirs) {
        try {
          await this.loadExtension(dir.name);
        } catch (error) {
          logger.error('Failed to load extension', {
            extensionName: dir.name,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      logger.info('Extensions loaded', {
        totalExtensions: this.extensions.size,
        enabledExtensions: Array.from(this.extensions.values()).filter(ext => ext.enabled).length
      });

    } catch (error) {
      logger.error('Failed to load extensions', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  public async loadExtension(extensionName: string): Promise<Extension> {
    const extensionPath = path.join(this.extensionsPath, extensionName);
    const manifestPath = path.join(extensionPath, 'package.json');

    // Check if manifest exists
    try {
      await fs.access(manifestPath);
    } catch {
      throw new Error(`Extension manifest not found: ${manifestPath}`);
    }

    // Load and validate manifest
    const manifestContent = await fs.readFile(manifestPath, 'utf-8');
    const manifestData = JSON.parse(manifestContent);
    const manifest = ExtensionManifestSchema.parse(manifestData);

    // Check if extension is already loaded
    if (this.extensions.has(manifest.name)) {
      throw new Error(`Extension ${manifest.name} is already loaded`);
    }

    // Validate permissions
    this.validatePermissions(manifest.permissions || []);

    // Check dependencies
    await this.checkDependencies(manifest.dependencies || {});

    // Create extension object
    const extension: Extension = {
      name: manifest.name,
      version: manifest.version,
      description: manifest.description,
      author: manifest.author,
      homepage: manifest.homepage,
      main: manifest.main,
      config: manifest.config,
      dependencies: manifest.dependencies,
      permissions: manifest.permissions,
      enabled: true,
      loadedAt: new Date()
    };

    // Load extension module
    const mainPath = path.join(extensionPath, manifest.main);
    try {
      const extensionModule = await this.loadExtensionModule(mainPath, extension, extensionPath);
      this.loadedModules.set(manifest.name, extensionModule);
    } catch (error) {
      throw new Error(`Failed to load extension module: ${error instanceof Error ? error.message : String(error)}`);
    }

    this.extensions.set(manifest.name, extension);

    logger.info('Extension loaded successfully', {
      name: manifest.name,
      version: manifest.version,
      author: manifest.author
    });

    return extension;
  }

  public async unloadExtension(extensionName: string): Promise<boolean> {
    const extension = this.extensions.get(extensionName);
    if (!extension) {
      return false;
    }

    try {
      // Call extension cleanup if available
      const module = this.loadedModules.get(extensionName);
      if (module && typeof module.deactivate === 'function') {
        await module.deactivate();
      }

      // Remove from loaded modules
      this.loadedModules.delete(extensionName);
      
      // Remove from extensions
      this.extensions.delete(extensionName);

      logger.info('Extension unloaded', { extensionName });
      return true;

    } catch (error) {
      logger.error('Failed to unload extension', {
        extensionName,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  public enableExtension(extensionName: string): boolean {
    const extension = this.extensions.get(extensionName);
    if (extension) {
      extension.enabled = true;
      logger.info('Extension enabled', { extensionName });
      return true;
    }
    return false;
  }

  public disableExtension(extensionName: string): boolean {
    const extension = this.extensions.get(extensionName);
    if (extension) {
      extension.enabled = false;
      logger.info('Extension disabled', { extensionName });
      return true;
    }
    return false;
  }

  public getExtension(extensionName: string): Extension | undefined {
    return this.extensions.get(extensionName);
  }

  public listExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getEnabledExtensions(): Extension[] {
    return Array.from(this.extensions.values()).filter(ext => ext.enabled);
  }

  public async installExtension(extensionPath: string): Promise<Extension> {
    // This would handle installing an extension from a path or URL
    // For now, we'll assume the extension is already in the extensions directory
    const extensionName = path.basename(extensionPath);
    return this.loadExtension(extensionName);
  }

  public async uninstallExtension(extensionName: string): Promise<boolean> {
    // Unload the extension first
    await this.unloadExtension(extensionName);

    // Remove extension directory
    const extensionPath = path.join(this.extensionsPath, extensionName);
    try {
      await fs.rm(extensionPath, { recursive: true, force: true });
      logger.info('Extension uninstalled', { extensionName });
      return true;
    } catch (error) {
      logger.error('Failed to uninstall extension', {
        extensionName,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  public getExtensionConfig(extensionName: string): any {
    const extension = this.extensions.get(extensionName);
    return extension?.config || {};
  }

  public async setExtensionConfig(extensionName: string, config: any): Promise<boolean> {
    const extension = this.extensions.get(extensionName);
    if (!extension) {
      return false;
    }

    extension.config = config;

    // Save config to extension's config file
    const configPath = path.join(this.extensionsPath, extensionName, 'config.json');
    try {
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      
      // Notify extension of config change
      const module = this.loadedModules.get(extensionName);
      if (module && typeof module.onConfigChange === 'function') {
        await module.onConfigChange(config);
      }

      logger.debug('Extension config updated', { extensionName });
      return true;
    } catch (error) {
      logger.error('Failed to save extension config', {
        extensionName,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }

  private async loadExtensionModule(modulePath: string, extension: Extension, extensionPath: string): Promise<any> {
    // Create extension context
    const context: ExtensionContext = {
      extensionPath,
      configPath: path.join(extensionPath, 'config.json'),
      dataPath: path.join(extensionPath, 'data'),
      logger: logger.child({ extension: extension.name }),
      version: extension.version
    };

    // Create extension API
    const api: ExtensionAPI = {
      getConfig: () => extension.config,
      setConfig: (config: any) => this.setExtensionConfig(extension.name, config)
    };

    // Load the module
    const module = await import(modulePath);
    
    // Call activation function if available
    if (typeof module.activate === 'function') {
      await module.activate(context, api);
    }

    return module;
  }

  private validatePermissions(permissions: string[]): void {
    const allowedPermissions = [
      'file:read',
      'file:write',
      'network:http',
      'network:https',
      'shell:execute',
      'config:read',
      'config:write',
      'tools:register',
      'commands:register',
      'themes:register'
    ];

    for (const permission of permissions) {
      if (!allowedPermissions.includes(permission)) {
        throw new Error(`Invalid permission: ${permission}`);
      }
    }
  }

  private async checkDependencies(dependencies: Record<string, string>): Promise<void> {
    // Check if required dependencies are available
    for (const [depName, depVersion] of Object.entries(dependencies)) {
      try {
        // This would check if the dependency is installed and compatible
        // For now, we'll just log the dependency
        logger.debug('Extension dependency', { name: depName, version: depVersion });
      } catch (error) {
        throw new Error(`Missing dependency: ${depName}@${depVersion}`);
      }
    }
  }

  public getExtensionStats(): {
    total: number;
    enabled: number;
    disabled: number;
    withPermissions: number;
    withDependencies: number;
  } {
    const extensions = Array.from(this.extensions.values());
    
    return {
      total: extensions.length,
      enabled: extensions.filter(ext => ext.enabled).length,
      disabled: extensions.filter(ext => !ext.enabled).length,
      withPermissions: extensions.filter(ext => ext.permissions && ext.permissions.length > 0).length,
      withDependencies: extensions.filter(ext => ext.dependencies && Object.keys(ext.dependencies).length > 0).length
    };
  }
}
