// Shell commands processor
import { useState, useCallback } from 'react';
import { spawn, ChildProcess } from 'child_process';
import { platform } from 'os';

export interface ShellCommandResult {
  command: string;
  args: string[];
  stdout: string;
  stderr: string;
  exitCode: number | null;
  signal: string | null;
  duration: number;
  success: boolean;
}

export interface ShellCommandOptions {
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  shell?: boolean;
  maxBuffer?: number;
}

export interface UseShellCommandProcessorReturn {
  executeCommand: (command: string, options?: ShellCommandOptions) => Promise<ShellCommandResult>;
  executeCommandStream: (
    command: string,
    onData: (data: string, type: 'stdout' | 'stderr') => void,
    options?: ShellCommandOptions
  ) => Promise<ShellCommandResult>;
  isExecuting: boolean;
  currentCommand: string | null;
  abortExecution: () => void;
  lastResult: ShellCommandResult | null;
}

export function useShellCommandProcessor(): UseShellCommandProcessorReturn {
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentCommand, setCurrentCommand] = useState<string | null>(null);
  const [currentProcess, setCurrentProcess] = useState<ChildProcess | null>(null);
  const [lastResult, setLastResult] = useState<ShellCommandResult | null>(null);

  const parseCommand = (command: string): { cmd: string; args: string[] } => {
    // Simple command parsing - in production, you might want to use a proper shell parser
    const parts = command.trim().split(/\s+/);
    const cmd = parts[0];
    const args = parts.slice(1);
    
    return { cmd, args };
  };

  const getShellCommand = (command: string): { cmd: string; args: string[] } => {
    const isWindows = platform() === 'win32';
    
    if (isWindows) {
      return {
        cmd: 'cmd',
        args: ['/c', command]
      };
    } else {
      return {
        cmd: 'sh',
        args: ['-c', command]
      };
    }
  };

  const executeCommand = useCallback(async (
    command: string,
    options: ShellCommandOptions = {}
  ): Promise<ShellCommandResult> => {
    setIsExecuting(true);
    setCurrentCommand(command);

    const startTime = Date.now();
    let stdout = '';
    let stderr = '';
    let exitCode: number | null = null;
    let signal: string | null = null;

    try {
      const { shell = true, cwd = process.cwd(), env, timeout = 30000, maxBuffer = 1024 * 1024 } = options;
      
      const { cmd, args } = shell ? getShellCommand(command) : parseCommand(command);

      return new Promise((resolve, reject) => {
        const childProcess = spawn(cmd, args, {
          cwd,
          env: { ...process.env, ...env },
          stdio: ['pipe', 'pipe', 'pipe']
        });

        setCurrentProcess(childProcess);

        // Set up timeout
        const timeoutId = setTimeout(() => {
          childProcess.kill('SIGTERM');
          setTimeout(() => {
            if (!childProcess.killed) {
              childProcess.kill('SIGKILL');
            }
          }, 5000);
        }, timeout);

        // Handle stdout
        childProcess.stdout?.on('data', (data: Buffer) => {
          const chunk = data.toString();
          stdout += chunk;
          
          if (stdout.length > maxBuffer) {
            childProcess.kill('SIGTERM');
            stderr += '\nOutput exceeded maximum buffer size\n';
          }
        });

        // Handle stderr
        childProcess.stderr?.on('data', (data: Buffer) => {
          const chunk = data.toString();
          stderr += chunk;
          
          if (stderr.length > maxBuffer) {
            childProcess.kill('SIGTERM');
            stderr += '\nError output exceeded maximum buffer size\n';
          }
        });

        // Handle process exit
        childProcess.on('close', (code, sig) => {
          clearTimeout(timeoutId);
          setCurrentProcess(null);
          
          exitCode = code;
          signal = sig;
          
          const duration = Date.now() - startTime;
          const result: ShellCommandResult = {
            command,
            args,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode,
            signal,
            duration,
            success: code === 0
          };

          setLastResult(result);
          resolve(result);
        });

        // Handle errors
        childProcess.on('error', (error) => {
          clearTimeout(timeoutId);
          setCurrentProcess(null);
          
          const duration = Date.now() - startTime;
          const result: ShellCommandResult = {
            command,
            args,
            stdout: stdout.trim(),
            stderr: `Process error: ${error.message}`,
            exitCode: null,
            signal: null,
            duration,
            success: false
          };

          setLastResult(result);
          resolve(result);
        });
      });
    } finally {
      setIsExecuting(false);
      setCurrentCommand(null);
      setCurrentProcess(null);
    }
  }, []);

  const executeCommandStream = useCallback(async (
    command: string,
    onData: (data: string, type: 'stdout' | 'stderr') => void,
    options: ShellCommandOptions = {}
  ): Promise<ShellCommandResult> => {
    setIsExecuting(true);
    setCurrentCommand(command);

    const startTime = Date.now();
    let stdout = '';
    let stderr = '';
    let exitCode: number | null = null;
    let signal: string | null = null;

    try {
      const { shell = true, cwd = process.cwd(), env, timeout = 30000 } = options;
      
      const { cmd, args } = shell ? getShellCommand(command) : parseCommand(command);

      return new Promise((resolve, reject) => {
        const childProcess = spawn(cmd, args, {
          cwd,
          env: { ...process.env, ...env },
          stdio: ['pipe', 'pipe', 'pipe']
        });

        setCurrentProcess(childProcess);

        // Set up timeout
        const timeoutId = setTimeout(() => {
          childProcess.kill('SIGTERM');
          setTimeout(() => {
            if (!childProcess.killed) {
              childProcess.kill('SIGKILL');
            }
          }, 5000);
        }, timeout);

        // Handle stdout with streaming
        childProcess.stdout?.on('data', (data: Buffer) => {
          const chunk = data.toString();
          stdout += chunk;
          onData(chunk, 'stdout');
        });

        // Handle stderr with streaming
        childProcess.stderr?.on('data', (data: Buffer) => {
          const chunk = data.toString();
          stderr += chunk;
          onData(chunk, 'stderr');
        });

        // Handle process exit
        childProcess.on('close', (code, sig) => {
          clearTimeout(timeoutId);
          setCurrentProcess(null);
          
          exitCode = code;
          signal = sig;
          
          const duration = Date.now() - startTime;
          const result: ShellCommandResult = {
            command,
            args,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode,
            signal,
            duration,
            success: code === 0
          };

          setLastResult(result);
          resolve(result);
        });

        // Handle errors
        childProcess.on('error', (error) => {
          clearTimeout(timeoutId);
          setCurrentProcess(null);
          
          const duration = Date.now() - startTime;
          const result: ShellCommandResult = {
            command,
            args,
            stdout: stdout.trim(),
            stderr: `Process error: ${error.message}`,
            exitCode: null,
            signal: null,
            duration,
            success: false
          };

          setLastResult(result);
          resolve(result);
        });
      });
    } finally {
      setIsExecuting(false);
      setCurrentCommand(null);
      setCurrentProcess(null);
    }
  }, []);

  const abortExecution = useCallback(() => {
    if (currentProcess) {
      currentProcess.kill('SIGTERM');
      setTimeout(() => {
        if (currentProcess && !currentProcess.killed) {
          currentProcess.kill('SIGKILL');
        }
      }, 5000);
    }
  }, [currentProcess]);

  return {
    executeCommand,
    executeCommandStream,
    isExecuting,
    currentCommand,
    abortExecution,
    lastResult
  };
}
