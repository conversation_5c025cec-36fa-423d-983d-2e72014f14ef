// Code assistance type definitions
export interface CodeAssistConfig {
  enabled: boolean;
  autoComplete: boolean;
  inlineHelp: boolean;
  codeReview: boolean;
  refactoring: boolean;
}

export interface CodeContext {
  filePath: string;
  language: string;
  content: string;
  cursorPosition: number;
  selection?: {
    start: number;
    end: number;
  };
}

export interface CodeSuggestion {
  type: 'completion' | 'fix' | 'refactor' | 'documentation';
  title: string;
  description: string;
  code: string;
  range?: {
    start: number;
    end: number;
  };
  confidence: number;
}

export interface CodeAssistRequest {
  context: CodeContext;
  type: 'completion' | 'help' | 'review' | 'refactor';
  options?: Record<string, any>;
}

export interface CodeAssistResponse {
  suggestions: CodeSuggestion[];
  metadata?: Record<string, any>;
}
