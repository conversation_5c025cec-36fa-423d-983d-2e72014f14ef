// Auto-accept mode visual indicator
import React from 'react';
import { Box, Text } from 'ink';

interface AutoAcceptIndicatorProps {
  level: 'default' | 'auto-edit' | 'yolo';
  visible: boolean;
}

export const AutoAcceptIndicator: React.FC<AutoAcceptIndicatorProps> = ({ 
  level, 
  visible 
}) => {
  if (!visible || level === 'default') {
    return null;
  }

  const getIndicatorConfig = () => {
    switch (level) {
      case 'auto-edit':
        return {
          icon: '🔧',
          text: 'AUTO-EDIT',
          color: 'yellow' as const,
          description: 'Auto-approving file edits'
        };
      case 'yolo':
        return {
          icon: '⚠️',
          text: 'YOLO MODE',
          color: 'red' as const,
          description: 'Auto-approving ALL operations'
        };
      default:
        return null;
    }
  };

  const config = getIndicatorConfig();
  if (!config) return null;

  return (
    <Box 
      borderStyle="round" 
      borderColor={config.color}
      paddingX={1}
      marginBottom={1}
    >
      <Box flexDirection="column" alignItems="center">
        <Box>
          <Text color={config.color}>
            {config.icon} {config.text}
          </Text>
        </Box>
        <Text dimColor fontSize={12}>
          {config.description}
        </Text>
      </Box>
    </Box>
  );
};
