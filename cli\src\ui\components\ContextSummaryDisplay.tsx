// Context information display component
import React from 'react';
import { Box, Text } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface ContextInfo {
  workingDirectory: string;
  gitBranch?: string;
  gitStatus?: {
    modified: number;
    added: number;
    deleted: number;
    untracked: number;
  };
  fileCount: number;
  projectType?: string;
  lastActivity?: Date;
  memoryUsage?: {
    used: number;
    total: number;
    percentage: number;
  };
}

interface ContextSummaryDisplayProps {
  context: ContextInfo;
  compact?: boolean;
  showGitInfo?: boolean;
  showMemoryInfo?: boolean;
}

export const ContextSummaryDisplay: React.FC<ContextSummaryDisplayProps> = ({
  context,
  compact = false,
  showGitInfo = true,
  showMemoryInfo = true
}) => {
  const theme = useTheme();

  const formatPath = (path: string) => {
    const maxLength = compact ? 30 : 50;
    if (path.length <= maxLength) return path;
    return '...' + path.slice(-(maxLength - 3));
  };

  const formatMemoryUsage = (usage: ContextInfo['memoryUsage']) => {
    if (!usage) return 'N/A';
    const usedMB = (usage.used / 1024 / 1024).toFixed(1);
    const totalMB = (usage.total / 1024 / 1024).toFixed(1);
    return `${usedMB}MB / ${totalMB}MB (${usage.percentage.toFixed(1)}%)`;
  };

  const getMemoryColor = (percentage: number) => {
    if (percentage > 80) return theme.colors.error;
    if (percentage > 60) return theme.colors.warning;
    return theme.colors.success;
  };

  if (compact) {
    return (
      <Box flexDirection="row" gap={2}>
        <Text color={theme.colors.muted}>📁</Text>
        <Text color={theme.colors.text}>{formatPath(context.workingDirectory)}</Text>
        {context.gitBranch && showGitInfo && (
          <>
            <Text color={theme.colors.muted}>🌿</Text>
            <Text color={theme.colors.secondary}>{context.gitBranch}</Text>
          </>
        )}
        <Text color={theme.colors.muted}>📄 {context.fileCount}</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column">
      {/* Working Directory */}
      <Box marginBottom={1}>
        <Text color={theme.colors.muted}>Working Directory: </Text>
        <Text color={theme.colors.text}>{formatPath(context.workingDirectory)}</Text>
      </Box>

      {/* Git Information */}
      {showGitInfo && context.gitBranch && (
        <Box flexDirection="column" marginBottom={1}>
          <Box>
            <Text color={theme.colors.muted}>Git Branch: </Text>
            <Text color={theme.colors.secondary}>{context.gitBranch}</Text>
          </Box>
          {context.gitStatus && (
            <Box marginLeft={2}>
              {context.gitStatus.modified > 0 && (
                <Text color={theme.colors.warning}>
                  {context.gitStatus.modified} modified 
                </Text>
              )}
              {context.gitStatus.added > 0 && (
                <Text color={theme.colors.success}>
                  {context.gitStatus.added} added 
                </Text>
              )}
              {context.gitStatus.deleted > 0 && (
                <Text color={theme.colors.error}>
                  {context.gitStatus.deleted} deleted 
                </Text>
              )}
              {context.gitStatus.untracked > 0 && (
                <Text color={theme.colors.info}>
                  {context.gitStatus.untracked} untracked
                </Text>
              )}
            </Box>
          )}
        </Box>
      )}

      {/* Project Information */}
      <Box flexDirection="row" gap={2} marginBottom={1}>
        <Box>
          <Text color={theme.colors.muted}>Files: </Text>
          <Text color={theme.colors.text}>{context.fileCount}</Text>
        </Box>
        {context.projectType && (
          <Box>
            <Text color={theme.colors.muted}>Type: </Text>
            <Text color={theme.colors.text}>{context.projectType}</Text>
          </Box>
        )}
      </Box>

      {/* Memory Usage */}
      {showMemoryInfo && context.memoryUsage && (
        <Box>
          <Text color={theme.colors.muted}>Memory: </Text>
          <Text color={getMemoryColor(context.memoryUsage.percentage)}>
            {formatMemoryUsage(context.memoryUsage)}
          </Text>
        </Box>
      )}

      {/* Last Activity */}
      {context.lastActivity && (
        <Box marginTop={1}>
          <Text color={theme.colors.muted}>
            Last activity: {context.lastActivity.toLocaleString()}
          </Text>
        </Box>
      )}
    </Box>
  );
};
