// ASCII art generation utilities
export const ARIEN_LOGO = `
 █████╗ ██████╗ ██╗███████╗███╗   ██╗
██╔══██╗██╔══██╗██║██╔════╝████╗  ██║
███████║██████╔╝██║█████╗  ██╔██╗ ██║
██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║
██║  ██║██║  ██║██║███████╗██║ ╚████║
╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝
`;

export const ARIEN_SMALL_LOGO = `
 ▄▀█ █▀█ █ █▀▀ █▄░█
 █▀█ █▀▄ █ ██▄ █░▀█
`;

export const LOADING_SPINNER = [
  '⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'
];

export const THINKING_DOTS = [
  '⠁', '⠂', '⠄', '⡀', '⢀', '⠠', '⠐', '⠈'
];

export const PROGRESS_BARS = {
  blocks: ['▏', '▎', '▍', '▌', '▋', '▊', '▉', '█'],
  dots: ['⣀', '⣄', '⣤', '⣦', '⣶', '⣷', '⣿'],
  arrows: ['▹', '▸', '▶'],
  circles: ['◐', '◓', '◑', '◒']
};

export const STATUS_ICONS = {
  success: '✅',
  error: '❌',
  warning: '⚠️',
  info: 'ℹ️',
  loading: '⏳',
  thinking: '🤔',
  robot: '🤖',
  file: '📄',
  folder: '📁',
  code: '💻',
  network: '🌐',
  security: '🔒',
  tool: '🔧',
  memory: '🧠',
  chat: '💬',
  user: '👤',
  ai: '🤖'
};

export const BORDERS = {
  single: {
    topLeft: '┌',
    topRight: '┐',
    bottomLeft: '└',
    bottomRight: '┘',
    horizontal: '─',
    vertical: '│',
    cross: '┼',
    teeUp: '┴',
    teeDown: '┬',
    teeLeft: '┤',
    teeRight: '├'
  },
  double: {
    topLeft: '╔',
    topRight: '╗',
    bottomLeft: '╚',
    bottomRight: '╝',
    horizontal: '═',
    vertical: '║',
    cross: '╬',
    teeUp: '╩',
    teeDown: '╦',
    teeLeft: '╣',
    teeRight: '╠'
  },
  rounded: {
    topLeft: '╭',
    topRight: '╮',
    bottomLeft: '╰',
    bottomRight: '╯',
    horizontal: '─',
    vertical: '│',
    cross: '┼',
    teeUp: '┴',
    teeDown: '┬',
    teeLeft: '┤',
    teeRight: '├'
  }
};

export function generateProgressBar(
  progress: number,
  width: number = 20,
  style: keyof typeof PROGRESS_BARS = 'blocks'
): string {
  const chars = PROGRESS_BARS[style];
  const filled = Math.floor((progress / 100) * width);
  const partial = Math.floor(((progress / 100) * width - filled) * chars.length);
  
  let bar = '';
  
  // Add filled characters
  for (let i = 0; i < filled; i++) {
    bar += chars[chars.length - 1];
  }
  
  // Add partial character
  if (partial > 0 && filled < width) {
    bar += chars[partial - 1];
  }
  
  // Add empty characters
  const remaining = width - filled - (partial > 0 ? 1 : 0);
  for (let i = 0; i < remaining; i++) {
    bar += ' ';
  }
  
  return bar;
}

export function createBox(
  content: string,
  width: number,
  borderStyle: keyof typeof BORDERS = 'single'
): string {
  const border = BORDERS[borderStyle];
  const lines = content.split('\n');
  const maxLineLength = Math.max(...lines.map(line => line.length));
  const boxWidth = Math.max(width, maxLineLength + 4);
  
  let result = '';
  
  // Top border
  result += border.topLeft + border.horizontal.repeat(boxWidth - 2) + border.topRight + '\n';
  
  // Content lines
  for (const line of lines) {
    const padding = boxWidth - line.length - 2;
    const leftPadding = Math.floor(padding / 2);
    const rightPadding = padding - leftPadding;
    
    result += border.vertical + 
              ' '.repeat(leftPadding) + 
              line + 
              ' '.repeat(rightPadding) + 
              border.vertical + '\n';
  }
  
  // Bottom border
  result += border.bottomLeft + border.horizontal.repeat(boxWidth - 2) + border.bottomRight;
  
  return result;
}

export function createTable(
  headers: string[],
  rows: string[][],
  borderStyle: keyof typeof BORDERS = 'single'
): string {
  const border = BORDERS[borderStyle];
  
  // Calculate column widths
  const colWidths = headers.map((header, i) => {
    const maxRowWidth = Math.max(...rows.map(row => (row[i] || '').length));
    return Math.max(header.length, maxRowWidth);
  });
  
  let result = '';
  
  // Top border
  result += border.topLeft;
  for (let i = 0; i < colWidths.length; i++) {
    result += border.horizontal.repeat(colWidths[i] + 2);
    if (i < colWidths.length - 1) {
      result += border.teeDown;
    }
  }
  result += border.topRight + '\n';
  
  // Header row
  result += border.vertical;
  for (let i = 0; i < headers.length; i++) {
    const padding = colWidths[i] - headers[i].length;
    result += ' ' + headers[i] + ' '.repeat(padding + 1) + border.vertical;
  }
  result += '\n';
  
  // Header separator
  result += border.teeRight;
  for (let i = 0; i < colWidths.length; i++) {
    result += border.horizontal.repeat(colWidths[i] + 2);
    if (i < colWidths.length - 1) {
      result += border.cross;
    }
  }
  result += border.teeLeft + '\n';
  
  // Data rows
  for (const row of rows) {
    result += border.vertical;
    for (let i = 0; i < colWidths.length; i++) {
      const cell = row[i] || '';
      const padding = colWidths[i] - cell.length;
      result += ' ' + cell + ' '.repeat(padding + 1) + border.vertical;
    }
    result += '\n';
  }
  
  // Bottom border
  result += border.bottomLeft;
  for (let i = 0; i < colWidths.length; i++) {
    result += border.horizontal.repeat(colWidths[i] + 2);
    if (i < colWidths.length - 1) {
      result += border.teeUp;
    }
  }
  result += border.bottomRight;
  
  return result;
}

export function getRandomSpinner(): string[] {
  const spinners = [LOADING_SPINNER, THINKING_DOTS];
  return spinners[Math.floor(Math.random() * spinners.length)];
}

export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.substring(0, maxLength - 3) + '...';
}

export function centerText(text: string, width: number): string {
  if (text.length >= width) {
    return text;
  }
  
  const padding = width - text.length;
  const leftPadding = Math.floor(padding / 2);
  const rightPadding = padding - leftPadding;
  
  return ' '.repeat(leftPadding) + text + ' '.repeat(rightPadding);
}

export function padText(text: string, width: number, align: 'left' | 'right' | 'center' = 'left'): string {
  if (text.length >= width) {
    return text;
  }
  
  const padding = width - text.length;
  
  switch (align) {
    case 'right':
      return ' '.repeat(padding) + text;
    case 'center':
      return centerText(text, width);
    case 'left':
    default:
      return text + ' '.repeat(padding);
  }
}
