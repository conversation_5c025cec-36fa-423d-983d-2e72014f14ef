// Detailed message history display component
import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { useTheme } from '../../hooks/useTheme.js';

export interface DetailedMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  metadata?: {
    model?: string;
    tokens?: {
      input: number;
      output: number;
    };
    duration?: number;
    toolCalls?: any[];
    toolResults?: any[];
  };
  isStreaming?: boolean;
  error?: string;
}

interface DetailedMessagesDisplayProps {
  messages: DetailedMessage[];
  maxHeight?: number;
  showMetadata?: boolean;
  showTimestamps?: boolean;
  allowScrolling?: boolean;
  highlightLatest?: boolean;
}

export const DetailedMessagesDisplay: React.FC<DetailedMessagesDisplayProps> = ({
  messages,
  maxHeight = 20,
  showMetadata = false,
  showTimestamps = true,
  allowScrolling = true,
  highlightLatest = true
}) => {
  const theme = useTheme();
  const [scrollOffset, setScrollOffset] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(messages.length - 1);

  // Auto-scroll to latest message
  useEffect(() => {
    if (highlightLatest && messages.length > 0) {
      setSelectedIndex(messages.length - 1);
      setScrollOffset(Math.max(0, messages.length - maxHeight));
    }
  }, [messages.length, maxHeight, highlightLatest]);

  // Handle keyboard input for scrolling
  useInput((input, key) => {
    if (!allowScrolling) return;

    if (key.upArrow && selectedIndex > 0) {
      setSelectedIndex(selectedIndex - 1);
      if (selectedIndex - scrollOffset <= 2) {
        setScrollOffset(Math.max(0, scrollOffset - 1));
      }
    } else if (key.downArrow && selectedIndex < messages.length - 1) {
      setSelectedIndex(selectedIndex + 1);
      if (selectedIndex - scrollOffset >= maxHeight - 3) {
        setScrollOffset(Math.min(messages.length - maxHeight, scrollOffset + 1));
      }
    } else if (key.pageUp) {
      const newIndex = Math.max(0, selectedIndex - maxHeight);
      setSelectedIndex(newIndex);
      setScrollOffset(Math.max(0, newIndex - Math.floor(maxHeight / 2)));
    } else if (key.pageDown) {
      const newIndex = Math.min(messages.length - 1, selectedIndex + maxHeight);
      setSelectedIndex(newIndex);
      setScrollOffset(Math.min(messages.length - maxHeight, newIndex - Math.floor(maxHeight / 2)));
    }
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'user': return theme.colors.user;
      case 'assistant': return theme.colors.assistant;
      case 'system': return theme.colors.system;
      case 'tool': return theme.colors.secondary;
      default: return theme.colors.text;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'user': return '👤';
      case 'assistant': return '🤖';
      case 'system': return '⚙️';
      case 'tool': return '🔧';
      default: return '💬';
    }
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const visibleMessages = messages.slice(scrollOffset, scrollOffset + maxHeight);

  if (messages.length === 0) {
    return (
      <Box flexDirection="column" height={maxHeight}>
        <Text color={theme.colors.muted}>No messages to display</Text>
      </Box>
    );
  }

  return (
    <Box flexDirection="column" height={maxHeight}>
      {/* Header */}
      <Box marginBottom={1}>
        <Text color={theme.colors.text}>
          Messages ({messages.length}) 
        </Text>
        {allowScrolling && (
          <Text color={theme.colors.muted}>
            - Use ↑↓ to navigate, PgUp/PgDn to jump
          </Text>
        )}
      </Box>

      {/* Messages */}
      <Box flexDirection="column" flexGrow={1}>
        {visibleMessages.map((message, index) => {
          const actualIndex = scrollOffset + index;
          const isSelected = actualIndex === selectedIndex;
          const isLatest = actualIndex === messages.length - 1;

          return (
            <Box
              key={message.id}
              flexDirection="column"
              marginBottom={1}
              paddingX={isSelected ? 1 : 0}
              borderStyle={isSelected ? 'single' : undefined}
              borderColor={isSelected ? theme.colors.primary : undefined}
            >
              {/* Message header */}
              <Box>
                <Text color={getRoleColor(message.role)}>
                  {getRoleIcon(message.role)} {message.role}
                </Text>
                {showTimestamps && (
                  <Text color={theme.colors.muted}>
                    {' '}[{message.timestamp.toLocaleTimeString()}]
                  </Text>
                )}
                {isLatest && highlightLatest && (
                  <Text color={theme.colors.success}> ✨ Latest</Text>
                )}
                {message.isStreaming && (
                  <Text color={theme.colors.warning}> 🔄 Streaming</Text>
                )}
              </Box>

              {/* Message content */}
              <Box marginLeft={2} marginTop={0}>
                {message.error ? (
                  <Text color={theme.colors.error}>❌ {message.error}</Text>
                ) : (
                  <Text color={theme.colors.text}>{message.content}</Text>
                )}
              </Box>

              {/* Metadata */}
              {showMetadata && message.metadata && (
                <Box marginLeft={2} marginTop={0}>
                  <Text color={theme.colors.muted}>
                    {message.metadata.model && `Model: ${message.metadata.model} `}
                    {message.metadata.tokens && 
                      `Tokens: ${message.metadata.tokens.input}→${message.metadata.tokens.output} `}
                    {message.metadata.duration && 
                      `Duration: ${formatDuration(message.metadata.duration)} `}
                    {message.metadata.toolCalls && 
                      `Tools: ${message.metadata.toolCalls.length} calls`}
                  </Text>
                </Box>
              )}
            </Box>
          );
        })}
      </Box>

      {/* Scroll indicator */}
      {allowScrolling && messages.length > maxHeight && (
        <Box marginTop={1}>
          <Text color={theme.colors.muted}>
            Showing {scrollOffset + 1}-{Math.min(scrollOffset + maxHeight, messages.length)} of {messages.length}
          </Text>
        </Box>
      )}
    </Box>
  );
};
