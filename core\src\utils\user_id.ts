// User identification utilities
import { randomUUID } from 'crypto';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { getConfigPath } from './paths.js';

export function generateUserId(): string {
  return randomUUID();
}

export function getUserId(): string {
  const configPath = getConfigPath();
  const userIdPath = join(configPath, 'user_id');

  try {
    if (existsSync(userIdPath)) {
      return readFileSync(userIdPath, 'utf-8').trim();
    }
  } catch (error) {
    // File doesn't exist or can't be read, create new user ID
  }

  // Create new user ID
  const userId = generateUserId();
  
  try {
    // Ensure config directory exists
    if (!existsSync(configPath)) {
      mkdirSync(configPath, { recursive: true });
    }
    
    writeFileSync(userIdPath, userId);
    return userId;
  } catch (error) {
    // If we can't write the file, return a temporary ID
    return generateUserId();
  }
}
