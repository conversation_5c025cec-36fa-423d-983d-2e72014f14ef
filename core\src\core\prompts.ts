// System prompts and prompt engineering
import { getConfig } from '../config/config.js';
import { logger } from './logger.js';

export interface PromptContext {
  userRole?: string;
  projectType?: string;
  language?: string;
  framework?: string;
  tools?: string[];
  preferences?: {
    codeStyle?: string;
    verbosity?: 'concise' | 'detailed' | 'verbose';
    explanationLevel?: 'beginner' | 'intermediate' | 'expert';
  };
  sessionContext?: {
    previousTopics?: string[];
    currentTask?: string;
    workingDirectory?: string;
  };
}

export interface PromptTemplate {
  name: string;
  template: string;
  variables: string[];
  description: string;
}

export class PromptManager {
  private templates = new Map<string, PromptTemplate>();
  private config = getConfig();

  constructor() {
    this.initializeDefaultTemplates();
  }

  public generateSystemPrompt(context: PromptContext = {}): string {
    const {
      userRole = 'developer',
      projectType,
      language,
      framework,
      tools = [],
      preferences = {},
      sessionContext = {}
    } = context;

    let prompt = this.getBaseSystemPrompt();

    // Add role-specific instructions
    prompt += this.getRoleInstructions(userRole);

    // Add project context
    if (projectType) {
      prompt += `\n\nYou are working on a ${projectType} project`;
      if (language) {
        prompt += ` using ${language}`;
      }
      if (framework) {
        prompt += ` with ${framework}`;
      }
      prompt += '.';
    }

    // Add tool information
    if (tools.length > 0) {
      prompt += '\n\n## Available Tools\n';
      prompt += 'You have access to the following tools:\n';
      tools.forEach(tool => {
        prompt += `- ${tool}\n`;
      });
      prompt += '\nUse these tools when appropriate to help the user accomplish their tasks.';
    }

    // Add preferences
    if (Object.keys(preferences).length > 0) {
      prompt += '\n\n## User Preferences\n';
      
      if (preferences.codeStyle) {
        prompt += `- Code style: ${preferences.codeStyle}\n`;
      }
      
      if (preferences.verbosity) {
        prompt += `- Response style: ${preferences.verbosity}\n`;
      }
      
      if (preferences.explanationLevel) {
        prompt += `- Explanation level: ${preferences.explanationLevel}\n`;
      }
    }

    // Add session context
    if (sessionContext.currentTask) {
      prompt += `\n\nCurrent task: ${sessionContext.currentTask}`;
    }

    if (sessionContext.workingDirectory) {
      prompt += `\nWorking directory: ${sessionContext.workingDirectory}`;
    }

    if (sessionContext.previousTopics && sessionContext.previousTopics.length > 0) {
      prompt += `\nPrevious topics discussed: ${sessionContext.previousTopics.join(', ')}`;
    }

    return prompt;
  }

  public generateTaskPrompt(
    task: string,
    context: {
      files?: string[];
      codeSnippets?: string[];
      requirements?: string[];
      constraints?: string[];
    } = {}
  ): string {
    let prompt = `Task: ${task}\n\n`;

    if (context.requirements && context.requirements.length > 0) {
      prompt += 'Requirements:\n';
      context.requirements.forEach((req, index) => {
        prompt += `${index + 1}. ${req}\n`;
      });
      prompt += '\n';
    }

    if (context.constraints && context.constraints.length > 0) {
      prompt += 'Constraints:\n';
      context.constraints.forEach((constraint, index) => {
        prompt += `${index + 1}. ${constraint}\n`;
      });
      prompt += '\n';
    }

    if (context.files && context.files.length > 0) {
      prompt += 'Relevant files:\n';
      context.files.forEach(file => {
        prompt += `- ${file}\n`;
      });
      prompt += '\n';
    }

    if (context.codeSnippets && context.codeSnippets.length > 0) {
      prompt += 'Code context:\n';
      context.codeSnippets.forEach((snippet, index) => {
        prompt += `\`\`\`\n${snippet}\n\`\`\`\n\n`;
      });
    }

    return prompt;
  }

  public generateCodeReviewPrompt(
    code: string,
    language: string,
    focusAreas: string[] = []
  ): string {
    let prompt = `Please review the following ${language} code:\n\n`;
    prompt += `\`\`\`${language}\n${code}\n\`\`\`\n\n`;

    if (focusAreas.length > 0) {
      prompt += 'Please focus on:\n';
      focusAreas.forEach(area => {
        prompt += `- ${area}\n`;
      });
      prompt += '\n';
    }

    prompt += 'Provide feedback on:\n';
    prompt += '1. Code quality and best practices\n';
    prompt += '2. Potential bugs or issues\n';
    prompt += '3. Performance considerations\n';
    prompt += '4. Security concerns\n';
    prompt += '5. Suggestions for improvement\n';

    return prompt;
  }

  public generateDebugPrompt(
    error: string,
    code?: string,
    context?: string
  ): string {
    let prompt = `I'm encountering the following error:\n\n`;
    prompt += `\`\`\`\n${error}\n\`\`\`\n\n`;

    if (code) {
      prompt += 'Relevant code:\n';
      prompt += `\`\`\`\n${code}\n\`\`\`\n\n`;
    }

    if (context) {
      prompt += `Additional context: ${context}\n\n`;
    }

    prompt += 'Please help me:\n';
    prompt += '1. Understand what this error means\n';
    prompt += '2. Identify the root cause\n';
    prompt += '3. Provide a solution or fix\n';
    prompt += '4. Suggest how to prevent similar issues\n';

    return prompt;
  }

  public generateExplanationPrompt(
    topic: string,
    level: 'beginner' | 'intermediate' | 'expert' = 'intermediate',
    includeExamples: boolean = true
  ): string {
    let prompt = `Please explain ${topic}`;

    switch (level) {
      case 'beginner':
        prompt += ' in simple terms, assuming no prior knowledge';
        break;
      case 'intermediate':
        prompt += ' with moderate technical detail';
        break;
      case 'expert':
        prompt += ' with comprehensive technical depth';
        break;
    }

    prompt += '.';

    if (includeExamples) {
      prompt += ' Please include practical examples and use cases.';
    }

    return prompt;
  }

  public registerTemplate(template: PromptTemplate): void {
    this.templates.set(template.name, template);
    logger.debug('Prompt template registered', { name: template.name });
  }

  public getTemplate(name: string): PromptTemplate | undefined {
    return this.templates.get(name);
  }

  public renderTemplate(name: string, variables: Record<string, string>): string {
    const template = this.templates.get(name);
    if (!template) {
      throw new Error(`Template '${name}' not found`);
    }

    let rendered = template.template;
    
    // Replace variables
    for (const [key, value] of Object.entries(variables)) {
      const placeholder = `{{${key}}}`;
      rendered = rendered.replace(new RegExp(placeholder, 'g'), value);
    }

    // Check for unreplaced variables
    const unreplacedVars = rendered.match(/\{\{[^}]+\}\}/g);
    if (unreplacedVars) {
      logger.warn('Unreplaced variables in template', {
        template: name,
        unreplacedVars
      });
    }

    return rendered;
  }

  public listTemplates(): PromptTemplate[] {
    return Array.from(this.templates.values());
  }

  private getBaseSystemPrompt(): string {
    return `You are Arien, an AI assistant designed to help developers with coding, debugging, and technical tasks. You are knowledgeable, helpful, and provide accurate information.

## Core Principles
- Be helpful and provide practical solutions
- Give clear, well-structured responses
- Include code examples when relevant
- Explain your reasoning when appropriate
- Ask clarifying questions if the request is ambiguous
- Prioritize security and best practices
- Be honest about limitations and uncertainties

## Response Guidelines
- Use markdown formatting for better readability
- Structure responses with clear headings and sections
- Provide step-by-step instructions for complex tasks
- Include relevant code snippets with proper syntax highlighting
- Suggest alternative approaches when applicable`;
  }

  private getRoleInstructions(role: string): string {
    const roleInstructions = {
      developer: '\n\nAs a developer assistant, focus on practical coding solutions, best practices, and efficient implementation strategies.',
      
      student: '\n\nAs a learning assistant, provide educational explanations, break down complex concepts, and encourage good programming habits.',
      
      architect: '\n\nAs a system architecture assistant, focus on design patterns, scalability, maintainability, and high-level system design.',
      
      devops: '\n\nAs a DevOps assistant, emphasize automation, deployment strategies, monitoring, and infrastructure best practices.',
      
      tester: '\n\nAs a testing assistant, focus on test strategies, quality assurance, debugging techniques, and testing best practices.',
      
      manager: '\n\nAs a technical management assistant, provide insights on project planning, team coordination, and technical decision-making.'
    };

    return roleInstructions[role as keyof typeof roleInstructions] || roleInstructions.developer;
  }

  private initializeDefaultTemplates(): void {
    // Code review template
    this.registerTemplate({
      name: 'code_review',
      template: `Please review the following {{language}} code for {{focus_areas}}:

\`\`\`{{language}}
{{code}}
\`\`\`

Provide feedback on code quality, potential issues, and suggestions for improvement.`,
      variables: ['language', 'code', 'focus_areas'],
      description: 'Template for code review requests'
    });

    // Bug fix template
    this.registerTemplate({
      name: 'bug_fix',
      template: `I'm experiencing the following issue:

**Error:** {{error}}

**Code:**
\`\`\`{{language}}
{{code}}
\`\`\`

**Context:** {{context}}

Please help me identify and fix this issue.`,
      variables: ['error', 'language', 'code', 'context'],
      description: 'Template for bug fix requests'
    });

    // Feature implementation template
    this.registerTemplate({
      name: 'feature_implementation',
      template: `I need to implement the following feature:

**Description:** {{description}}

**Requirements:**
{{requirements}}

**Constraints:**
{{constraints}}

Please provide a solution with code examples.`,
      variables: ['description', 'requirements', 'constraints'],
      description: 'Template for feature implementation requests'
    });

    logger.debug('Default prompt templates initialized');
  }
}
