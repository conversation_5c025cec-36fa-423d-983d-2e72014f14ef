// Theme hook for consistent styling
import { useState, useEffect } from 'react';

export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    text: string;
    muted: string;
    border: string;
    background: string;
    user: string;
    assistant: string;
    system: string;
  };
}

const themes: Record<string, Theme> = {
  default: {
    name: 'Default',
    colors: {
      primary: '#00D9FF',
      secondary: '#FF6B6B',
      success: '#51CF66',
      warning: '#FFD43B',
      error: '#FF6B6B',
      info: '#74C0FC',
      text: '#FFFFFF',
      muted: '#868E96',
      border: '#495057',
      background: '#000000',
      user: '#00D9FF',
      assistant: '#51CF66',
      system: '#FFD43B'
    }
  },
  
  dark: {
    name: 'Dark',
    colors: {
      primary: '#0EA5E9',
      secondary: '#8B5CF6',
      success: '#10B981',
      warning: '#F59E0B',
      error: '#EF4444',
      info: '#06B6D4',
      text: '#F8FAFC',
      muted: '#64748B',
      border: '#374151',
      background: '#0F172A',
      user: '#0EA5E9',
      assistant: '#10B981',
      system: '#F59E0B'
    }
  },
  
  light: {
    name: 'Light',
    colors: {
      primary: '#2563EB',
      secondary: '#7C3AED',
      success: '#059669',
      warning: '#D97706',
      error: '#DC2626',
      info: '#0891B2',
      text: '#1F2937',
      muted: '#6B7280',
      border: '#D1D5DB',
      background: '#FFFFFF',
      user: '#2563EB',
      assistant: '#059669',
      system: '#D97706'
    }
  },
  
  cyberpunk: {
    name: 'Cyberpunk',
    colors: {
      primary: '#FF0080',
      secondary: '#00FFFF',
      success: '#00FF41',
      warning: '#FFFF00',
      error: '#FF0040',
      info: '#0080FF',
      text: '#00FFFF',
      muted: '#808080',
      border: '#FF0080',
      background: '#000000',
      user: '#FF0080',
      assistant: '#00FF41',
      system: '#FFFF00'
    }
  },
  
  matrix: {
    name: 'Matrix',
    colors: {
      primary: '#00FF41',
      secondary: '#008F11',
      success: '#00FF41',
      warning: '#FFFF00',
      error: '#FF0000',
      info: '#00FFFF',
      text: '#00FF41',
      muted: '#008F11',
      border: '#00FF41',
      background: '#000000',
      user: '#00FFFF',
      assistant: '#00FF41',
      system: '#FFFF00'
    }
  }
};

export function useTheme(themeName?: string): Theme {
  const [currentTheme, setCurrentTheme] = useState<string>(() => {
    // Try to load theme from environment or config
    return process.env.ARIEN_THEME || themeName || 'default';
  });

  useEffect(() => {
    if (themeName && themeName !== currentTheme) {
      setCurrentTheme(themeName);
    }
  }, [themeName, currentTheme]);

  const theme = themes[currentTheme] || themes.default;

  return theme;
}

export function getAvailableThemes(): string[] {
  return Object.keys(themes);
}

export function setTheme(themeName: string): boolean {
  if (themes[themeName]) {
    // In a real implementation, this would persist the theme choice
    process.env.ARIEN_THEME = themeName;
    return true;
  }
  return false;
}
