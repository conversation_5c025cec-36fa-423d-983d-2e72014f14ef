// Git ignore file parsing utilities
import { readFile } from 'fs/promises';
import { join, resolve, relative, sep } from 'path';
import { minimatch } from 'minimatch';
import { logger } from '../core/logger.js';

export interface GitIgnoreRule {
  pattern: string;
  isNegation: boolean;
  isDirectory: boolean;
  isAbsolute: boolean;
  source: string;
}

export interface GitIgnoreResult {
  ignored: boolean;
  matchedRule?: GitIgnoreRule;
  reason?: string;
}

export class GitIgnoreParser {
  private rules: GitIgnoreRule[] = [];
  private rulesByDirectory = new Map<string, GitIgnoreRule[]>();

  constructor(private rootPath: string = process.cwd()) {}

  public async loadGitIgnore(gitIgnorePath?: string): Promise<void> {
    const ignoreFile = gitIgnorePath || join(this.rootPath, '.gitignore');
    
    try {
      const content = await readFile(ignoreFile, 'utf-8');
      this.parseGitIgnore(content, ignoreFile);
      logger.debug('Loaded .gitignore', { path: ignoreFile, rules: this.rules.length });
    } catch (error) {
      // .gitignore file doesn't exist or can't be read
      logger.debug('No .gitignore file found', { path: ignoreFile });
    }
  }

  public async loadGitIgnoreFromDirectory(directory: string): Promise<void> {
    const gitIgnorePath = join(directory, '.gitignore');
    
    try {
      const content = await readFile(gitIgnorePath, 'utf-8');
      const rules = this.parseGitIgnoreContent(content, gitIgnorePath);
      this.rulesByDirectory.set(directory, rules);
      logger.debug('Loaded directory .gitignore', { path: gitIgnorePath, rules: rules.length });
    } catch (error) {
      // No .gitignore in this directory
    }
  }

  public parseGitIgnore(content: string, source: string = 'inline'): void {
    const rules = this.parseGitIgnoreContent(content, source);
    this.rules.push(...rules);
  }

  private parseGitIgnoreContent(content: string, source: string): GitIgnoreRule[] {
    const rules: GitIgnoreRule[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines and comments
      if (!line || line.startsWith('#')) {
        continue;
      }

      const rule = this.parseRule(line, source);
      if (rule) {
        rules.push(rule);
      }
    }

    return rules;
  }

  private parseRule(pattern: string, source: string): GitIgnoreRule | null {
    let cleanPattern = pattern.trim();
    
    if (!cleanPattern) return null;

    const isNegation = cleanPattern.startsWith('!');
    if (isNegation) {
      cleanPattern = cleanPattern.slice(1);
    }

    const isDirectory = cleanPattern.endsWith('/');
    if (isDirectory) {
      cleanPattern = cleanPattern.slice(0, -1);
    }

    const isAbsolute = cleanPattern.startsWith('/');
    if (isAbsolute) {
      cleanPattern = cleanPattern.slice(1);
    }

    return {
      pattern: cleanPattern,
      isNegation,
      isDirectory,
      isAbsolute,
      source
    };
  }

  public isIgnored(filePath: string, isDirectory: boolean = false): GitIgnoreResult {
    const relativePath = relative(this.rootPath, resolve(filePath));
    
    // Normalize path separators
    const normalizedPath = relativePath.split(sep).join('/');
    
    // Check global rules first
    const globalResult = this.checkRules(normalizedPath, isDirectory, this.rules);
    if (globalResult.ignored) {
      return globalResult;
    }

    // Check directory-specific rules
    const pathParts = normalizedPath.split('/');
    for (let i = 0; i < pathParts.length; i++) {
      const dirPath = pathParts.slice(0, i + 1).join('/');
      const absoluteDirPath = join(this.rootPath, dirPath);
      
      const dirRules = this.rulesByDirectory.get(absoluteDirPath);
      if (dirRules) {
        const remainingPath = pathParts.slice(i + 1).join('/');
        const dirResult = this.checkRules(remainingPath, isDirectory, dirRules);
        if (dirResult.ignored) {
          return dirResult;
        }
      }
    }

    return { ignored: false };
  }

  private checkRules(path: string, isDirectory: boolean, rules: GitIgnoreRule[]): GitIgnoreResult {
    let ignored = false;
    let matchedRule: GitIgnoreRule | undefined;

    for (const rule of rules) {
      if (this.matchesRule(path, isDirectory, rule)) {
        ignored = !rule.isNegation;
        matchedRule = rule;
        
        // Continue checking for negation rules that might override
        if (!rule.isNegation) {
          // Look for negation rules that might un-ignore this file
          for (let j = rules.indexOf(rule) + 1; j < rules.length; j++) {
            const laterRule = rules[j];
            if (laterRule.isNegation && this.matchesRule(path, isDirectory, laterRule)) {
              ignored = false;
              matchedRule = laterRule;
              break;
            }
          }
        }
      }
    }

    return {
      ignored,
      matchedRule,
      reason: matchedRule ? `Matched rule: ${matchedRule.pattern} from ${matchedRule.source}` : undefined
    };
  }

  private matchesRule(path: string, isDirectory: boolean, rule: GitIgnoreRule): boolean {
    // If rule is for directories only, skip non-directories
    if (rule.isDirectory && !isDirectory) {
      return false;
    }

    let pattern = rule.pattern;
    let testPath = path;

    // Handle absolute patterns
    if (rule.isAbsolute) {
      // Pattern should match from root
      return minimatch(testPath, pattern);
    }

    // For non-absolute patterns, check if it matches any part of the path
    const pathParts = testPath.split('/');
    
    // Try matching the full path
    if (minimatch(testPath, pattern)) {
      return true;
    }

    // Try matching any suffix of the path
    for (let i = 0; i < pathParts.length; i++) {
      const suffix = pathParts.slice(i).join('/');
      if (minimatch(suffix, pattern)) {
        return true;
      }
    }

    // Try matching just the filename
    const filename = pathParts[pathParts.length - 1];
    if (minimatch(filename, pattern)) {
      return true;
    }

    return false;
  }

  public getRules(): GitIgnoreRule[] {
    return [...this.rules];
  }

  public addRule(pattern: string, source: string = 'manual'): void {
    const rule = this.parseRule(pattern, source);
    if (rule) {
      this.rules.push(rule);
    }
  }

  public clearRules(): void {
    this.rules = [];
    this.rulesByDirectory.clear();
  }

  public async loadAllGitIgnores(rootPath?: string): Promise<void> {
    const searchRoot = rootPath || this.rootPath;
    
    // Load root .gitignore
    await this.loadGitIgnore(join(searchRoot, '.gitignore'));
    
    // Recursively load .gitignore files from subdirectories
    await this.loadGitIgnoresRecursive(searchRoot);
  }

  private async loadGitIgnoresRecursive(directory: string, maxDepth: number = 10, currentDepth: number = 0): Promise<void> {
    if (currentDepth >= maxDepth) return;

    try {
      const { readdir, stat } = await import('fs/promises');
      const entries = await readdir(directory);

      for (const entry of entries) {
        // Skip .git directory
        if (entry === '.git') continue;

        const entryPath = join(directory, entry);
        
        try {
          const stats = await stat(entryPath);
          
          if (stats.isDirectory()) {
            // Load .gitignore from this directory
            await this.loadGitIgnoreFromDirectory(entryPath);
            
            // Recursively check subdirectories
            await this.loadGitIgnoresRecursive(entryPath, maxDepth, currentDepth + 1);
          }
        } catch (error) {
          // Skip entries we can't access
        }
      }
    } catch (error) {
      logger.debug('Failed to read directory for .gitignore files', { directory, error });
    }
  }
}

// Convenience functions
export async function createGitIgnoreParser(rootPath?: string): Promise<GitIgnoreParser> {
  const parser = new GitIgnoreParser(rootPath);
  await parser.loadAllGitIgnores();
  return parser;
}

export async function isFileIgnored(filePath: string, rootPath?: string): Promise<boolean> {
  const parser = await createGitIgnoreParser(rootPath);
  const result = parser.isIgnored(filePath);
  return result.ignored;
}

export async function filterIgnoredFiles(filePaths: string[], rootPath?: string): Promise<string[]> {
  const parser = await createGitIgnoreParser(rootPath);
  
  return filePaths.filter(filePath => {
    const result = parser.isIgnored(filePath);
    return !result.ignored;
  });
}
